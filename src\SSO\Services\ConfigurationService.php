<?php

namespace SSO\Services;

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Http;
use SSO\DTO\KeycloakUserInfo;
use SSO\Facades\SSO;

class ConfigurationService
{
    public static function getPublicKey()
    {
        $issuerUrl = self::getWellKnownConfiguration('issuer');
        // dd($issuerUrl);

        return Cache::driver('file')->rememberForever('keycloak_public_key', function () use ($issuerUrl) {
            return @Http::get($issuerUrl)->json()['public_key'];
        });
    }


    public static function getWellKnownConfiguration($key = null)
    {
        // dd(Http::get(SSO::wellKnownConfigurationUrl())->json());
        $config = Cache::driver('file')->rememberForever('keycloak_wellknown', function () {
            return Http::get(SSO::wellKnownConfigurationUrl())->json();
        });

        return $key ? @$config[$key] : $config;
    }


    public static function SSOIdpConnectionVerified($key)
    {
        if (!$key) {
            return;
        }
        $value = tenant()->getMeta($key);
        if ($value && is_array($value)) {
            $value['connection_verified'] = 1;
        }

        tenant()->setMeta($key, $value);
        // if(session()->get(config('galaxysso.config.access_token_session_key'))){

        // }
    }
}
