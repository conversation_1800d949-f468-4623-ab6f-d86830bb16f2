<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('rto_course_extension_history', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->unsignedBigInteger('college_id');
            $table->unsignedBigInteger('student_id');
            $table->unsignedBigInteger('course_id');
            $table->unsignedBigInteger('student_course_id');
            $table->date('start_date')->nullable();
            $table->date('finish_date')->nullable();
            $table->date('new_finish_date')->nullable();
            $table->integer('old_total_weeks')->nullable();
            $table->integer('new_total_weeks')->nullable();
            $table->text('extension_reason')->nullable();
            $table->unsignedBigInteger('created_by');
            $table->unsignedBigInteger('updated_by');
            $table->timestamps();

            $table->charset = 'utf8mb4';
            $table->collation = 'utf8mb4_bin';

            // Add foreign key constraints
            // $table->foreign('student_course_id')->references('id')->on('rto_student_courses')->onDelete('cascade');
            // $table->foreign('created_by')->references('id')->on('rto_users')->onDelete('cascade');
            // $table->foreign('updated_by')->references('id')->on('rto_users')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('rto_course_extension_history');
    }
};
