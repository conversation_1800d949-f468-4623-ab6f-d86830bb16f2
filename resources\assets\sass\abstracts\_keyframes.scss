@keyframes fadeInAnimation {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes popupAnimation {
    0% {
        opacity: 0;
        transform: scale(0.98);
    }
    30% {
        opacity: 1;
    }
    100% {
        opacity: 1;
        transform: none;
    }
}

@keyframes slideFromRight {
    0% {
        transform: translateX(100%);
    }
    100% {
        transform: translateX(0);
    }
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-8px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(8px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

// .tw-popup {
//     animation: popupAnimation 0.15s cubic-bezier(0.4, 0, 0.2, 1);
//     transform-origin: top;
// }

.tw-fadein {
    opacity: 0;
    animation: fadeInAnimation 0.3s ease forwards;
}

.tw-fadein-450 {
    opacity: 0;
    animation: fadeInAnimation 450ms ease forwards;
}

.tw-animate {
    animation-duration: 300ms;
    animation-timing-function: ease;
    animation-direction: forwards;
    opacity: 0;
    &.tw-fadein {
        opacity: 0;
        animation: fadeInAnimation 300ms ease forwards;
    }

    &.tw-slideup {
        opacity: 0;
        animation: slideUp 150ms ease forwards;
    }

    &.tw-slidedown {
        animation-name: slideDown;
    }

    &.tw-duration-450 {
        animation-duration: 450ms;
    }
}
