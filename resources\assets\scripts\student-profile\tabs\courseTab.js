var unitGridFlag = true;
var resultGridFlag = true;
var addCertificateForm = true;
var isLoadOfferService = true;
var unitConfirmationDivId = '#unitConfirmationDiv';
var alreadyDownloadedDivId = '#alreadyDownloaded';
var unitSyncToMoodleFromCourseModalId = '#unitSyncToMoodleFromCourseModal';
var enrollStudentSyncToMoodleFromCourseModalId = '#enrollStudentSyncToMoodleFromCourseModal';
var certificateRequirementsModalId = '#certificateRequirementsModal';
var certificateOverrideConfirmModalId = '#certificateOverrideConfirmModal';
var currentCourseGridSelector = null;

loadMoodleSyncModal();
intializeCourseTab();

function modalOpen(title) {
    return {
        title: title,
        width: '34%',
        actions: ['close'],
        draggable: false,
        resizable: false,
        modal: true,
        position: {
            top: '15%',
            left: '33%',
        },
        animation: {
            close: {
                effects: 'fade:out',
            },
        },
    };
}

function intializeCourseTab() {
    $('#currentCourseHistoryModal').kendoWindow(openCenterWindow('Add note', 40, 15, 33));

    $('#addCOENumberModal').kendoWindow(openCenterWindow('Add COE Number', 40, 15, 30));

    $('#studentCourseExtendModal').kendoWindow(openCenterWindow('Extend Due Date & Manage History'));

    $(alreadyDownloadedDivId).kendoDialog({
        width: '400px',
        title: 'Certificate',
        content:
            "Certificate is already downloaded. Are you sure you want to re-download this Certificate ? <input type='hidden' name='id' id='id' />",
        actions: [
            { text: 'Close' },
            {
                text: 'Download',
                primary: true,
                action: function () {
                    downloadStudentCertificate($(alreadyDownloadedDivId).find('#id').val());
                },
            },
        ],
        animation: defaultOpenAnimation(),
        open: onOpenDeleteDialog(alreadyDownloadedDivId),
        visible: false,
    });

    // Certificate requirements modal
    $(certificateRequirementsModalId).kendoDialog({
        width: '500px',
        title: 'Certificate Requirements',
        content:
            "The student does not meet the requirements to generate the certificate. <input type='hidden' name='certificateType' id='certificateType' />",
        actions: [
            { text: 'Cancel' },
            {
                text: 'Override and Continue',
                primary: true,
                action: function () {
                    let certificateType = $(certificateRequirementsModalId)
                        .find('#certificateType')
                        .val();
                    $(certificateOverrideConfirmModalId).data('kendoDialog').open();
                    $(certificateOverrideConfirmModalId)
                        .find('#certificateTypeConfirm')
                        .val(certificateType);
                },
            },
        ],
        animation: defaultOpenAnimation(),
        open: onOpenDeleteDialog(certificateRequirementsModalId),
        visible: false,
    });

    // Certificate override confirmation modal
    $(certificateOverrideConfirmModalId).kendoDialog({
        width: '500px',
        title: 'Confirm Override',
        content:
            "Do you wish to continue to generate the certificate even though the student does not meet the requirements? <input type='hidden' name='certificateTypeConfirm' id='certificateTypeConfirm' />",
        actions: [
            { text: 'Cancel' },
            {
                text: 'Generate Certificate',
                primary: true,
                action: function () {
                    let certificateType = $(certificateOverrideConfirmModalId)
                        .find('#certificateTypeConfirm')
                        .val();
                    downloadCertificateBeta(certificateType);
                },
            },
        ],
        animation: defaultOpenAnimation(),
        open: onOpenDeleteDialog(certificateOverrideConfirmModalId),
        visible: false,
    });

    // let isDownloadForUnit = ''; // maybe from a form, variable, or data

    // $(unitConfirmationDivId).kendoDialog({
    //     width: "600px",
    //     title: "Please confirm",
    //     content: `
    //     The certificate cannot be generated as the student has not completed the required
    //     number of core (<span id="coreUnitsCount"></span>) and elective units
    //     (<span id="electiveUnitsCount"></span>) for this qualification.
    //     Do you still want to generate the certificate?`,actions: [
    //         { text: "Close" },
    //         {
    //             text: "Download",
    //             primary: true,
    //             action: function () {
    //                 console.log('action isDownloadForUnit',isDownloadForUnit);
    //                 downloadCertificate(isDownloadForUnit)
    //             },
    //         },
    //     ],
    //     animation: defaultOpenAnimation(),
    //     open: onOpenDeleteDialog(unitConfirmationDivId),
    //     visible: false,
    // });

    loadMoodleSyncModal();
    // COMMENT: Check why are we initing same dialog one with variable id another with direct id #alreadyDownloaded
    // $("#alreadyDownloaded").kendoDialog({
    //     width: "400px",
    //     title: "Certificate",
    //     content:
    //         "Certificate is already downloaded. Are you sure you want to re-download this Certificate ? <input type='hidden' name='id' id='id' />",
    //     actions: [
    //         { text: "Close" },
    //         {
    //             text: "Download",
    //             primary: true,
    //             action: function () {
    //                 downloadStudentCertificate(
    //                     $("#alreadyDownloaded").find("#id").val(),
    //                 );
    //             },
    //         },
    //     ],
    //     animation: defaultOpenAnimation(),
    //     open: onOpenDeleteDialog("#alreadyDownloaded"),
    //     visible: false,
    // });
}

function loadMoodleSyncModal() {
    $(unitSyncToMoodleFromCourseModalId).kendoDialog({
        width: '400px',
        title: 'Sync Unit',
        content:
            "Are you sure you want to sync this unit to Moodle? <input type='hidden' name='id' id='subjectUnitId' />",
        actions: [
            { text: 'Close' },
            {
                text: 'Yes',
                primary: true,
                action: function () {
                    unitSyncWithMoodleFromCourseTab(
                        $(unitSyncToMoodleFromCourseModalId).find('#subjectUnitId').val()
                    );
                },
            },
        ],
        animation: defaultOpenAnimation(),
        open: onOpenCenterDialog(unitSyncToMoodleFromCourseModalId),
        visible: false,
    });

    $(enrollStudentSyncToMoodleFromCourseModalId).kendoDialog({
        width: '400px',
        title: 'Sync Student Subject Enroll',
        content:
            "Are you sure you want to sync this enroll data to Moodle? <input type='hidden' name='id' id='studentSubjectEnrollId' />",
        actions: [
            { text: 'Close' },
            {
                text: 'Yes',
                primary: true,
                action: function () {
                    studentSubjectEnrollSyncWithMoodleFromCourseTab(
                        $(enrollStudentSyncToMoodleFromCourseModalId)
                            .find('#studentSubjectEnrollId')
                            .val()
                    );
                },
            },
        ],
        animation: defaultOpenAnimation(),
        open: onOpenCenterDialog(enrollStudentSyncToMoodleFromCourseModalId),
        visible: false,
    });
}

$(document).ready(function () {
    $('.generateEnrollSubjectsCertificatePdf').on('click', function (e) {
        e.preventDefault();
        var selectedValue = $('#generate_certificate_list').data('kendoDropDownList').value();
        if (!selectedValue) {
            $('.err-msg').show();
            return;
        }
        $('.download-certificate-btngroup').removeClass('hidden');
        getLoadingDialog();
        generatePdf(selectedValue);
    });

    $('.cancel-generate-pdf').on('click', function (e) {
        $('.loadingDialog').data('kendoDialog').close();
        $('.embed-content').hide();
        $('.download-certificate-btngroup').addClass('hidden');
        $('.enroll-certificate-preview-placeholder').show();
        $('.studentEnrollDocumentsPreview').attr('src', '');
    });
});

function getLoadingDialog() {
    $('#loadingDialog').kendoDialog({
        width: '450px',
        title: 'Generating Certificate',
        closable: true,
        modal: false,
    });
}

function manageCourseTab() {
    console.log("What's running - Course", selectedTabText);
    if (selectedTabText === 'course' && !isSameTab) intializeCourseTab();
    loadCourseTab();
    //loadTrainingPlan();
    //loadOfferService();
    // manageTrainingPlanFlag(selectedDataArr);
    // manageOfferServiceFlag();
}

function manageOfferServiceFlag() {
    if (isLoadOfferService) {
        loadOfferService();
        isLoadOfferService = false;
    }
}

// function manageTrainingPlanFlag(tempSelectedDataArr){
//     if(isLoadTrainingPlan){
//         loadTrainingPlan(tempSelectedDataArr);
//         isLoadTrainingPlan = false;
//     }
// }
function checkCertificateRequirements(certificateType) {
    console.log('certificateType', certificateType);
    let type = $('#generate_certificate_list_beta').data('kendoDropDownList').value();
    if (type.length == 0) {
        notificationDisplay('Please select certificate type first', '', 'error');
        return false;
    }

    // Prepare data for the AJAX request
    let dataArrv2 = {
        studCourseId: selectedStudCourseID,
        type: type,
    };

    // Make AJAX call to check if student meets requirements
    ajaxActionV2('api/check-certificate-requirements', 'POST', dataArrv2, function (response) {
        if (response.status === 'error') {
            console.log('response.status', response.status);
            // Student does not meet requirements, show warning modal
            $(certificateRequirementsModalId).data('kendoDialog').open();
            $(certificateRequirementsModalId).find('#certificateType').val(certificateType);
        } else {
            // Student meets requirements, proceed with certificate download
            downloadCertificateBeta();
        }
    });
}

function downloadCertificateBeta() {
    let certificateId = $('#generate_certificate_list_beta').data('kendoDropDownList').value();
    let dataArry = [
        {
            studCourseId: selectedStudCourseID,
            isDownload: 1,
            certificateId: certificateId,
            studentId: studentId,
        },
    ];

    let encodedDataArr = encodeURIComponent(JSON.stringify(dataArry));
    let url = site_url + 'spa/generte-certificate?data=' + encodedDataArr;
    window.location.href = url;

    setTimeout(() => {
        reloadGrid('#studentCertificateIssueList');
    }, 500);
}

function checkCourseUnitResult(certificateType) {
    console.log('certificateType', certificateType);
    let type = $('#generate_certificate_list').data('kendoDropDownList').value();
    if (type.length == 0) {
        notificationDisplay('Please select certificate type first', '', 'error');
        return false;
    }
    if (type == 'C' && !isHigherEd) {
        let dataArr = [
            {
                completion_date: getYmdDateFormate(
                    $('#completion_date').data('kendoDatePicker').value()
                ),
                issued_date: getYmdDateFormate($('#issued_date').data('kendoDatePicker').value()),
                convert_course_status: $('#convert_course_status').data('kendoSwitch').value(),
                studCourseId: selectedStudCourseID,
                subject_enrollment_id: $('#subject_enrollment_id').val(),
                include_failed_subject: $('#include_failed_subject').data('kendoSwitch').value(),
                isCompleted: 1,
                type: type,
            },
        ];

        let encodedDataArr = encodeURIComponent(JSON.stringify(dataArr));
        let dataArrv2 = {
            studCourseId: selectedStudCourseID,
            type: type,
        };

        ajaxActionV2('api/check-course-unit-result', 'POST', dataArrv2, function (response) {
            console.log('response.status', response.status);
            if (response.status == 'error') {
                setTimeout(() => {
                    $('#coreUnitsCount').text(response.data.core_unit);
                    $('#electiveUnitsCount').text(response.data.elective_unit);
                    $('#isDownloadUnit').val(certificateType);
                    $(unitConfirmationDivId).data('kendoDialog').open();
                }, 300);
            } else {
                console.log('response.status certificateType', certificateType);
                downloadCertificate(certificateType);
            }
        });
    } else {
        console.log('response.status here 1', certificateType);
        downloadCertificate(certificateType);
    }
}

function downloadCertificate(isDownload) {
    console.log('downloadCertificate', isDownload);

    let type = $('#generate_certificate_list').data('kendoDropDownList').value();
    if (type.length == 0) {
        notificationDisplay('Please select certificate type first', '', 'error');
        return false;
    }

    let dataArr = [
        {
            completion_date: getYmdDateFormate(
                $('#completion_date').data('kendoDatePicker').value()
            ),
            issued_date: getYmdDateFormate($('#issued_date').data('kendoDatePicker').value()),
            convert_course_status: $('#convert_course_status').data('kendoSwitch').value(),
            studCourseId: selectedStudCourseID,
            subject_enrollment_id: $('#subject_enrollment_id').val(),
            isDownload: isDownload,
            include_failed_subject: $('#include_failed_subject').data('kendoSwitch').value(),
            isCompleted: 1,
            type: type,
        },
    ];

    let encodedDataArr = encodeURIComponent(JSON.stringify(dataArr));
    let dataArrv2 = {
        studCourseId: selectedStudCourseID,
        isDownload: isDownload,
        type: type,
    };
    console.log('dataArr', dataArr);
    ajaxActionV2('api/check-certificate-is-download', 'POST', dataArrv2, function (response) {
        if (response.status == 'error') {
            // notificationDisplay(response.message, "", response.status);
            $(alreadyDownloadedDivId).data('kendoDialog').open();
            $(alreadyDownloadedDivId)
                .find('#id')
                .val(site_url + 'api/generate-stud-enroll-subject-pdf?data=' + encodedDataArr);
        } else {
            setTimeout(() => {
                reloadGrid('#studentCertificateIssueList');
            }, 500);
            window.location.href =
                site_url + 'api/generate-stud-enroll-subject-pdf?data=' + encodedDataArr;
        }
    });
}

function downloadStudentCertificate(url) {
    setTimeout(() => {
        reloadGrid('#studentCertificateIssueList');
    }, 500);
    window.location.href = url;
}

function loadCourseTab() {
    let tempSelectedDataArr = selectedDataArr;
    tempSelectedDataArr.is_higher_ed = isHigherEd;

    ajaxActionV2(
        'api/get-student-summary-tab',
        'POST',
        selectedDataArr,
        function (response) {
            let responseArr = {
                arr: response.data.course_detail,
                arr2: response.data.current_course,
            };
            $(document)
                .find('#studCourseTab')
                .html(kendo.template($('#studCourseTemplate').html())(responseArr));
            $(document)
                .find('#studCourseProgress')
                .html(kendo.template($('#courseProgressTemplate').html())(responseArr));
        },
        false,
        function (flag) {
            toggleTabLoader('course', flag);
            toggleContentLoader('#studCourseTab', flag);
        }
    );
    ajaxActionV2(
        'api/get-course-tab-data',
        'POST',
        selectedDataArr,
        function (response) {
            let responseData = response.data;
            let responseArr = {
                data: responseData.courseSummary.currentCourseSummary,
                getResultCalculationMethod: responseData.courseSummary.getResultCalculationMethod,
                studentDetails: responseData.courseSummary.studentDetails,
            };
            $('#course_id').val(responseData.courseSummary.studentDetails[0].course_id);
            $('#activity_start_date').val(responseData.courseSummary.studentDetails[0].start_date);
            $('#activity_finish_date').val(
                responseData.courseSummary.studentDetails[0].finish_date
            );
            $('#sub_enroll_start_date').val(
                responseData.courseSummary.studentDetails[0].start_date
            );
            $('#sub_enroll_finish_date').val(
                responseData.courseSummary.studentDetails[0].finish_date
            );

            $(document)
                .find('.currentCourseSummary')
                .html(kendo.template($('#currentCourseSummaryTemplate').html())(responseArr));
            $(document)
                .find('.studentCourseDetailHeader')
                .html(kendo.template($('#studentCourseDetailHeaderTemplate').html())(responseArr));

            // $('#courseTimeline')
            //     .html('')
            //     .kendoTimeline({
            //         dataSource: {
            //             data: responseData?.courseProgress,
            //             // transport: getTransportReadOnly(url, dataArr),
            //             schema: {
            //                 model: {
            //                     // id: "title",
            //                     fields: {
            //                         date: {
            //                             type: 'date',
            //                         },
            //                     },
            //                 },
            //             },
            //             sort: { field: 'date', dir: 'desc' },
            //         },
            //         orientation: 'horizontal',
            //         eventTemplate: kendo.template($('#course-timeline').html()),
            //         dataBound: function (e) {
            //             setKendoTimelineTitle(e, '#courseTimeline', 'No Unit Details Found');
            //         },
            //         navigate: function (e) {
            //             console.log('eventContainer ', e.action);
            //         },
            //     });
            renderHTimelineV2(responseData?.courseProgress, '#courseTimeline');

            $('#generate_certificate_list').kendoDropDownList({
                filter: 'contains',
                optionLabel: 'Select',
                filterInput: {
                    width: '100%',
                },
                dataTextField: 'Name',
                dataValueField: 'Id',
                dataType: 'json',
                // dataSource: getDropdownDataSource('get-constant-data', {'action': 'generateCertificateList'}),
                dataSource: {
                    schema: { data: 'data' },
                    data: responseData.generateCertificateList,
                    // transport: getTransportReadOnly(apiUrl, postArr)
                },
                select: function (e) {
                    if (e.dataItem.Id == 'C') {
                        $('.certificateDiv').removeClass('hidden');
                        $('.transcriptDiv').addClass('hidden');
                    } else if (e.dataItem.Id == 'TOCA') {
                        $('.certificateDiv').addClass('hidden');
                        $('.transcriptDiv').removeClass('hidden');
                    } else {
                        $('.certificateDiv').addClass('hidden');
                        $('.transcriptDiv').addClass('hidden');
                    }
                    $('.err-msg').hide();
                    generatePdf(e.dataItem.Id);
                },
                // value: value,
            });
            $('#generate_certificate_list_beta').kendoDropDownList({
                filter: 'contains',
                optionLabel: 'Select Certificate',
                filterInput: {
                    width: '100%',
                },
                dataTextField: 'Name',
                dataValueField: 'Id',
                dataType: 'json',
                // dataSource: getDropdownDataSource('get-constant-data', {'action': 'generateCertificateList'}),
                dataSource: {
                    schema: { data: 'data' },
                    data: responseData.generateCertificateBetaList,
                    // transport: getTransportReadOnly(apiUrl, postArr)
                },
                select: function (e) {
                    if (e.dataItem.Id == 'C') {
                        $('.certificateDiv').removeClass('hidden');
                        $('.transcriptDiv').addClass('hidden');
                    } else if (e.dataItem.Id == 'TOCA') {
                        $('.certificateDiv').addClass('hidden');
                        $('.transcriptDiv').removeClass('hidden');
                    } else {
                        $('.certificateDiv').addClass('hidden');
                        $('.transcriptDiv').addClass('hidden');
                    }
                    $('.err-msg').hide();
                    generateBetaPdf(e.dataItem.Id);
                },
                // value: value,
            });
            // defaultSetSwitchValue('#convert_course_status','N');
            if (!$('#convert_course_status').data('kendoSwitch')) {
                $('#convert_course_status').kendoSwitch({
                    size: 'medium',
                    change: function (e) {
                        switchValueManage('#convert_course_status', e.checked);
                    },
                });
            }
            if (!$('#include_failed_subject').data('kendoSwitch')) {
                $('#include_failed_subject').kendoSwitch({
                    size: 'medium',
                    change: function (e) {
                        switchValueManage('#include_failed_subject', e.checked);
                        generatePdf(
                            $('#generate_certificate_list').data('kendoDropDownList').value()
                        );
                    },
                });
            }
            setTimeout(() => {
                $('.getGridOfCurrentCourseSummary .accordion:first').trigger('click');
            }, 500);
        },
        false,
        function (flag) {
            toggleContentLoader('.currentCourseSummary', flag);
            // toggleTabLoader("course", flag);
            // toggleContentLoader("#studCourseTab", flag);
        }
    );
    manageUnitList(tempSelectedDataArr);
    setKendoDatePicker('#completion_date');
    setKendoDatePicker('#issued_date');
}

function switchValueManage(inputId, switchValue) {
    $(document)
        .find(inputId)
        .closest('.customSwitchButton')
        .siblings('div')
        .find('.switchText')
        .text(switchValue ? 'Yes' : 'No');
}

function manageUnitList(tempSelectedDataArr) {
    // let isMoodleConnectVal = false;
    if (unitGridFlag) {
        unitGridFlag = false;
        // unitGridFlag = true;
        $('#unitList').kendoGrid({
            /*dataSource: customDataSource(
                "api/student-unit-data",
                {
                    semester_name: { type: "string" },
                    subject_name: { type: "string" },
                    campus_name: { type: "string" },
                    final_outcome: { type: "string" },
                },
                tempSelectedDataArr,
            ),*/
            dataSource: {
                type: 'json',
                transport: {
                    read: {
                        url: site_url + 'api/student-unit-data',
                        dataType: 'json',
                        type: 'POST',
                        data: tempSelectedDataArr,
                    },
                },
                requestEnd: function (e) {
                    if (e.type === 'read' && e.response) {
                        isMoodleConnectVal = e.response.data.isMoodleConnect;
                        manageMoodleColumnsForGrid('#unitList', isMoodleConnectVal);
                    }
                },
                schema: defaultSchema({
                    semester_name: { type: 'string' },
                    subject_name: { type: 'string' },
                    campus_name: { type: 'string' },
                    final_outcome: { type: 'string' },
                }),
                pageSize: 10,
                serverPaging: true,
                serverFiltering: true,
                serverSorting: true,
            },
            pageable: customPageableArr(),
            sortable: true,
            resizable: true,
            columns: [
                {
                    template:
                        "<div class='flex items-center text-sm leading-5 font-normal text-gray-600 action-div'>#: unit_id #</div>",
                    field: 'unit_id',
                    title: 'Sn.',
                    minResizableWidth: 50,
                    width: '50px',
                },
                {
                    template:
                        "<div class='flex items-center text-sm leading-5 font-normal text-gray-600 action-div'>#: unit_code #</div>",
                    field: 'unit_code',
                    title: 'Unit Code',
                    minResizableWidth: 100,
                    width: 120,
                },
                {
                    template:
                        "<div class='flex items-center text-sm leading-5 font-normal text-gray-600 action-div'>#: unit_name #</div>",
                    field: 'unit_name',
                    title: 'Unit Name',
                    minResizableWidth: 100,
                    width: 350,
                },
                {
                    // template: function(dataItem){
                    //     return manageProgressHours(dataItem.schedule_hours);
                    // },
                    template:
                        "<div class='flex items-center text-sm leading-5 font-normal text-gray-600 action-div'>#: ((batch !=null) ? batch :'--') #</div>",
                    field: 'batch',
                    title: 'Batch',
                    minResizableWidth: 100,
                    width: 200,
                },
                {
                    template:
                        "<div class='flex items-center text-sm leading-5 font-normal text-gray-600 action-div'>#: competency #</div>",
                    field: 'competency',
                    title: 'Competency',
                    minResizableWidth: 100,
                },
                {
                    template:
                        "<div class='flex items-center text-sm leading-5 font-normal text-gray-600 action-div'>#: (moodleData !== false && moodleData.unit_moodle_sync_status !== null) ? moodleData.unit_moodle_sync_status : 'Not Sync' #</div>",
                    field: 'moodle_status',
                    title: 'Moodle Status',
                    minResizableWidth: 100,
                    sortable: false,
                },
                {
                    template:
                        "<div class='flex items-center text-sm leading-5 font-normal text-gray-600 action-div'>#: (moodleData !== false && moodleData.unit_moodle_synced_at !== null) ? moodleData.unit_moodle_synced_at : '--' #</div>",
                    field: 'moodle_synced_at',
                    title: 'Sync AT',
                    minResizableWidth: 100,
                    sortable: false,
                },
            ],
            noRecords: noRecordTemplate(),
            dataBound: function (e) {
                togglePagination('#unitList');
            },
        });
        customGridHtml('#unitList');
    } else {
        refreshGrid('#unitList', tempSelectedDataArr);
    }
}

function dynamicCount(e) {
    $('.countOfSelectedSub').text($('#selectedSubject option').length);
}

function dynamicCountUnit(e) {
    $('.countOfSelectedUnit').text(
        $('#selectedUnit option').length > 0 ? $('#selectedUnit option').length : '0'
    );
}

function getWidgetName(e) {
    var listBoxId = e.sender.element.attr('id');
    var widgetName = listBoxId === 'optional' ? 'left widget' : 'right widget';
    return widgetName;
}

function manageResultData(dataArr1, resultGrid) {
    $('.' + resultGrid)
        .kendoGrid({
            dataSource: customDataSourceForInlineV2(
                'api/student-result-data',
                {
                    unit_name: { type: 'string', editable: false },
                    start_date: { type: 'date' },
                    finish_date: { type: 'date' },
                    final_outcome: { type: 'string', editable: !isHigherEd },
                    marks: { type: 'string', editable: false }, // Don't give option to edit marks
                    moodle_status: { type: 'string', editable: false },
                    moodle_synced_at: { type: 'date', editable: false },
                },
                dataArr1,
                {},
                'api/student-result-data-update',
                'api/student-result-data-delete',
                true,
                '.' + resultGrid
            ),

            // pageable: customPageableArr(),
            sortable: true,
            resizable: true,
            columns: [
                {
                    template: "<div data-id='#: id #' data-subject='#: unit_name #'></div>",
                    field: 'id',
                    hidden: true,
                },
                {
                    template:
                        "<div data-id='#: id #' data-subject='#: unit_name #' data-unit_id='#: unit_id #'></div>",
                    field: 'unit_id',
                    hidden: true,
                },
                {
                    //template: "<div data-id='#: id #' data-subject='#: subject_name #' class='flex items-center text-sm leading-5 font-normal text-gray-600 action-div'>#: subject_name #</div>",
                    template:
                        "<div class='flex items-center text-sm leading-5 font-normal text-gray-600 action-div'>#:unit_code # : #: unit_name # (#: batch #)  </div>",
                    field: 'unit_name',
                    title: 'Unit Name',
                    width: '40%',
                    editable: false,
                },
                {
                    template:
                        "<div class='flex items-center text-sm leading-5 font-normal text-gray-600 action-div'>#: ((final_outcome !=null) ? final_outcome :'--') #</div>",
                    field: 'final_outcome',
                    title: 'Final Outcome',
                    editor: dropDownForFinalOutcome,
                    width: '15%',
                },
                {
                    template:
                        "<div class='flex items-center text-sm leading-5 font-normal text-gray-600 action-div'>#: ((marks !=null) ? marks :'--') #</div>",
                    field: 'marks',
                    title: 'Marks',
                    hidden: !isHigherEd,
                    // editor: dropDownForFinalOutcome,
                    width: '15%',
                },
                {
                    template:
                        "<div class='flex items-center text-sm leading-5 font-normal text-gray-600 action-div'>#: ((start_date != null)?kendo.toString(start_date, displayDateFormatJS):'--') # </div>",
                    field: 'start_date',
                    title: 'Start Date',
                    format: '{0:dd/MM/yyyy}',
                    editor: datePickerForFinalOutCome1,
                    width: '10%',
                },
                {
                    template:
                        "<div class='flex items-center text-sm leading-5 font-normal text-gray-600 action-div'>#: ((finish_date != null)?kendo.toString(finish_date, displayDateFormatJS):'--') # </div>",
                    field: 'finish_date',
                    title: 'Finish Date',
                    format: '{0:dd/MM/yyyy}',
                    editor: datePickerForFinalOutCome1,
                    width: '10%',
                },
                {
                    template:
                        "<div class='flex items-center text-sm leading-5 font-normal text-gray-600 action-div'>#: (moodleData !== false && moodleData.unit_moodle_sync_status !== null) ? moodleData.unit_moodle_sync_status : 'Not Sync' #</div>",
                    field: 'moodle_status',
                    title: 'Moodle Status',
                    width: '10%',
                    sortable: false,
                },
                {
                    template:
                        "<div class='flex items-center text-sm leading-5 font-normal text-gray-600 action-div'>#: (moodleData !== false && moodleData.unit_moodle_synced_at !== null) ? moodleData.unit_moodle_synced_at : '--' #</div>",
                    field: 'moodle_synced_at',
                    title: 'Sync AT',
                    width: '10%',
                    sortable: false,
                },
                {
                    headerTemplate:
                        "<a class='k-link text-xs font-normal tracking-wide leading-none text-gray-500' style='cursor: default !important;'>Action</a>",
                    field: 'action',
                    title: 'Action',
                    filterable: false,
                    sortable: false,
                    width: '12%',
                    command: [
                        {
                            name: 'edit',
                            iconClass: 'k-icon k-i-edit',
                            className: 'summary-action',
                        },
                        {
                            name: 'destroy',
                            iconClass: 'k-icon k-i-delete',
                            className: 'summary-action',
                        },
                        {
                            template: function (dataItem) {
                                return `<span class="action-schedule k-button k-button-icontext glob-tooltip" data-id="${dataItem?.id}" title="More options">
                                    <span class="k-icon k-i-more-horizontal" style="color: #9CA3AF;"></span>
                                </span>`;
                            },
                        },
                    ],
                    attributes: {
                        class: 'tw-sticky-cell',
                    },
                    headerAttributes: {
                        class: 'tw-sticky-header',
                    },
                },
            ],
            editable: 'inline',
            save: function (e) {
                if (
                    e.model.final_outcome !== undefined &&
                    e.model.is_result_lock != '1' &&
                    (e.model.final_outcome == 'C' || e.model.final_outcome == 'NYC')
                ) {
                    e.preventDefault(); // Prevent auto-saving
                    // Display confirmation popup
                    if (confirm(resultConformationMessage)) {
                        // Proceed with saving the data if confirmed
                        e.sender.saveChanges(); // Manually trigger the save
                    }
                }
            },
            noRecords: noRecordTemplate(),
            dataBound: function (e) {
                togglePagination('.' + resultGrid);
                $(document).find('.course-skeleton').hide();
                $('.' + resultGrid).show();
                this.tbody.find('.k-grid-edit').attr('title', 'Edit');
                this.tbody.find('.k-grid-delete').attr('title', 'Delete');
                //this.tbody.find(".k-i-refresh").attr("title", "Sync to Moodle");
                getActionTooltip('.summary-action');
                this.tbody
                    .find('.unitSyncToMoodleBtn')
                    .off('click')
                    .on('click', function (e) {
                        console.log('Click', e);
                        unitSyncWithMoodleHandler(e, '.' + resultGrid, id);
                    });

                this.tbody
                    .find('.enrollStudentToMoodleBtn')
                    .off('click')
                    .on('click', function (e) {
                        enrollStudentSyncWithMoodleHandler(e, '.' + resultGrid);
                    });

                this.tbody.find('.moodleBtn').toggle(window.isMoodleConnected);
            },
        })
        .on('click', '.btn-history', function (e) {
            console.log('Here', e);
            // let treelist = $(e.delegateTarget).data("kendoGrid");
            let id = $(e.currentTarget)
                .closest('tr')
                .find('td:first-child')
                .find('div')
                .attr('data-id');
            let subject_name = $(e.currentTarget)
                .closest('tr')
                .find('td:first-child')
                .find('div')
                .attr('data-subject');
            ajaxActionV2(
                'api/get-stud-subject-enroll-history',
                'POST',
                { student_subject_enrolment_id: id },
                function (response) {
                    kendoWindowOpen('#currentCourseHistoryModal');
                    $('#currentCourseHistoryModal')
                        .prev()
                        .find('.k-window-title')
                        .text(subject_name);
                    let currentCourseHistoryTemplate = kendo.template(
                        $('#currentCourseHistoryTemplate').html()
                    )(response.data);
                    $(document).find('#currentCourseHistoryDiv').html(currentCourseHistoryTemplate);
                }
            );
        });
    $('.' + resultGrid).kendoTooltip({
        filter: 'td .action-schedule',
        position: 'bottom',
        showOn: 'click',
        width: 200,
        show: function (e) {
            // Remove the arrow element from the tooltip
            e.sender.popup.element.find('.k-callout').remove();
            e.sender.popup.wrapper.addClass('tw-fadein');
            let actionLeftPosition = e.sender.element
                .find('.action-schedule')
                .first()
                .offset().left;
            let documentWidth = $(document).width();
            let actionWidth = e.sender.element.find('.action-schedule').first().width();
            let offset = documentWidth - (actionLeftPosition + actionWidth);
            e.sender.popup.wrapper.css({
                width: '200px',
                right: offset,
                left: 'unset',
            });
        },
        content: function (e) {
            let dataItem = $('.' + resultGrid)
                .data('kendoGrid')
                .dataItem(e.target.closest('tr'));
            currentCourseGridSelector = '.' + resultGrid;
            let id = e.target.closest('tr').find('td:first-child').find('div').attr('data-id');
            let subject_name = e.target
                .closest('tr')
                .find('td:first-child')
                .find('div')
                .attr('data-subject');
            return kendo.template($('#currentCourseActionMenu').html())({
                id: id,
                subject_name: subject_name,
                unit_id: dataItem?.unit_id,
            });
        },
    });
}

function manageProgressHours(schedule_hours) {
    let cAttendanceHtml = '';
    cAttendanceHtml =
        '<div class="action-div w-full bg-gray-200 rounded-full h-2 dark:bg-green-500">' +
        '<div class="h-2 bg-gradient-to-r from-green-300 to-green-500 rounded-full-full dark:bg-gray-200" style="width: ' +
        randomIntFromInterval(70, 100) +
        '%">' +
        '</div>' +
        '</div>';
    return cAttendanceHtml;
}

function randomIntFromInterval(min, max) {
    // min and max included
    return Math.floor(Math.random() * (max - min + 1) + min);
}

function datePickerForFinalOutCome1(container, options) {
    var isEditable = options.model.is_result_lock == 0;
    $(
        '<input data-text-field="' +
            options.field +
            '" data-value-field="' +
            options.field +
            '" data-bind="value:' +
            options.field +
            '" data-format="' +
            options.format +
            '"  id="' +
            options.field +
            '"/>'
    )
        .appendTo(container)
        .kendoDatePicker()
        .data('kendoDatePicker') // Get the DatePicker instance
        .enable(isEditable);
    triggerCalendar('#' + options.field, 'dd/MM/yyyy');
}

function datePickerForInlineEdit(container, options) {
    var isEditable = options.model.is_result_lock == 0;
    $(
        '<input data-text-field="' +
            options.field +
            '" data-value-field="' +
            options.field +
            '" data-bind="value:' +
            options.field +
            '" data-format="' +
            options.format +
            '" id="' +
            options.field +
            '"/>'
    )
        .appendTo(container)
        .kendoDatePicker()
        .data('kendoDatePicker') // Get the DatePicker instance
        .enable(isEditable);
    triggerCalendar('#' + options.field, 'dd/MM/yyyy');
}

function datePickerForFinalOutCome(container, options) {
    $(
        '<input data-text-field="' +
            options.field +
            '" data-value-field="' +
            options.field +
            '" data-bind="value:' +
            options.field +
            '" data-format="' +
            options.format +
            '"  id="' +
            options.field +
            '"/>'
    )
        .appendTo(container)
        .kendoDatePicker();
    triggerCalendar('#' + options.field, 'dd/MM/yyyy');
}

function dropDownForFinalOutcome(container, options) {
    var isEditable = options.model.is_result_lock == 0;
    $(
        '<input name="final_outcome" required data-text-field="text" data-value-field="value" data-bind="value:' +
            options.field +
            '"/>'
    )
        .appendTo(container)
        .kendoDropDownList({
            autoBind: true,
            dataTextField: 'text',
            dataValueField: 'value',
            valuePrimitive: true,
            dataSource: getDropdownDataSource('get-constant-data', {
                action: 'arrFinalOutcome',
                college_id: collegeId,
                is_higher_ed: isHigherEd,
            }),
            enable: isEditable,
        });
}

function getYmdDateFormate(yourDate) {
    var formattedDate = new Date(yourDate);
    var d = formattedDate.getDate();
    var m = formattedDate.getMonth();
    m += 1;
    var y = formattedDate.getFullYear();
    return y + '-' + m + '-' + d;
}

function generatePdf(type = '') {
    /*let isTypeEmpty = (type.length <= 0) ? true : false;
    $(".embed-content").toggle(!isTypeEmpty);
    $(".enroll-certificate-preview-placeholder").toggle(isTypeEmpty);*/

    if (type.length > 0) {
        $('.embed-content').show();
        $('.enroll-certificate-preview-placeholder').hide();
        let dataArr = [
            {
                completion_date: getYmdDateFormate(
                    $('#completion_date').data('kendoDatePicker').value()
                ),
                issued_date: getYmdDateFormate($('#issued_date').data('kendoDatePicker').value()),
                convert_course_status: $('#convert_course_status').data('kendoSwitch').value(),
                studCourseId: selectedStudCourseID,
                isDownload: 0,
                subject_enrollment_id: $('#subject_enrollment_id').val(),
                include_failed_subject: $('#include_failed_subject').data('kendoSwitch').value(),
                isCompleted: 0,
                type: type,
            },
        ];
        let encodedDataArr = encodeURIComponent(JSON.stringify(dataArr));
        let url = site_url + 'api/generate-stud-enroll-subject-pdf?data=' + encodedDataArr;
        $('.enroll-certificate-preview-placeholder').hide();
        $('.embed-content').show();
        $('.studentEnrollDocumentsPreview').attr('src', url);
    } else {
        $('.embed-content').hide();
        $('.enroll-certificate-preview-placeholder').show();
    }

    // setTimeout(function () {
    //     $("#loadingDialog").data("kendoDialog").close();
    // }, 2000);
}

function generateBetaPdf(type = '') {
    console.log(type.length > 0);
    if (type) {
        $('.embed-content').show();
        $('.enroll-certificate-preview-placeholder').hide();
        let dataArr = [
            {
                // completion_date: getYmdDateFormate(
                //     $("#completion_date").data("kendoDatePicker").value(),
                // ),
                // issued_date: getYmdDateFormate(
                //     $("#issued_date").data("kendoDatePicker").value(),
                // ),
                // convert_course_status: $("#convert_course_status")
                //     .data("kendoSwitch")
                //     .value(),
                studCourseId: selectedStudCourseID,
                studentId: studentId,
                // isDownload: 0,
                subject_enrollment_id: $('#subject_enrollment_id').val(),
                // include_failed_subject: $("#include_failed_subject")
                //     .data("kendoSwitch")
                //     .value(),
                // isCompleted: 0,
                certificateId: type,
            },
        ];
        let encodedDataArr = encodeURIComponent(JSON.stringify(dataArr));
        let url = site_url + 'spa/generte-certificate?data=' + encodedDataArr;
        $('.enroll-certificate-preview-placeholder').hide();
        $('.embed-content').show();
        $('.studentEnrollDocumentsPreview').attr('src', url);
    } else {
        $('.embed-content').hide();
        $('.enroll-certificate-preview-placeholder').show();
    }
}

function unitSyncWithMoodleHandler(e, gridId, unit_id) {
    e.preventDefault();
    let unitId = unit_id;

    let $unitModal = $(unitSyncToMoodleFromCourseModalId);
    $unitModal.data('kendoDialog').open();
    $unitModal.find('#subjectUnitId').val(unitId);
}

function enrollStudentSyncWithMoodleHandler(e, id) {
    console.log('id', id);
    e.preventDefault();
    let studentSubjectEnrollId = id;

    let $enrollModal = $(enrollStudentSyncToMoodleFromCourseModalId);
    $enrollModal.data('kendoDialog').open();
    $enrollModal.find('#studentSubjectEnrollId').val(studentSubjectEnrollId);
}

function unitSyncWithMoodleFromCourseTab(subjectUnitId) {
    let tempSelectedDataArr = selectedDataArr;
    tempSelectedDataArr.subject_unit_id = subjectUnitId;

    ajaxActionV2(
        'api/unit-sync-with-moodle',
        'POST',
        tempSelectedDataArr,
        function (response) {
            notificationDisplay(response.message, '', response.status);
        },
        true
    );
}

function studentSubjectEnrollSyncWithMoodleFromCourseTab(studentSubjectEnrollId) {
    let tempSelectedDataArr = selectedDataArr;
    tempSelectedDataArr.student_subject_enroll_id = studentSubjectEnrollId;

    ajaxActionV2(
        'api/enroll-student-subject-sync-with-moodle',
        'POST',
        tempSelectedDataArr,
        function (response) {
            notificationDisplay(response.message, '', response.status);
        },
        true
    );
}

$('body').on('change', '#completion_date, #issued_date', function (e) {
    e.preventDefault();
    generatePdf($('#generate_certificate_list').data('kendoDropDownList').value());
});

$(document).on('click', '.accordion', function (e) {
    e.preventDefault();
    $(this).toggleClass('is-open');
    let content = this.nextElementSibling;
    let dataArr1 = { student_id: studentId };
    dataArr1['student_course_id'] = $(this).attr('data-student-course-id');
    dataArr1['semester_id'] = $(this).attr('data-semester');
    dataArr1['term_id'] = $(this).attr('data-term');
    let resultGrid = $(this).attr('data-grid-id');
    $(content).slideToggle('fast');
    if ($(this).hasClass('is-open')) {
        $('.' + resultGrid).hide();
        $(content).find('.course-skeleton').show();
        manageResultData(dataArr1, resultGrid);
    }
});

$(document).on('click', '.accordionDetail', function (e) {
    e.preventDefault();
    $(this).toggleClass('is-open');
    let content = this.nextElementSibling;
    let dataArr1 = { student_id: studentId };
    dataArr1['student_course_id'] = $(this).attr('data-student-course-id');
    dataArr1['semester_id'] = $(this).attr('data-semester');
    dataArr1['term_id'] = $(this).attr('data-term');
    let resultGrid = $(this).attr('data-grid-id');
    // var resultGridFlag = true;
    if (content.style.maxHeight) {
        content.style.maxHeight = null;
    } else {
        manageResultData(dataArr1, resultGrid);
        setTimeout(() => {
            content.style.maxHeight = content.scrollHeight + 'px';
        }, 800);
    }
});

$('body').on('click', '.downloadEnrollSubjectsCertificatePdf', function (e) {
    e.preventDefault();
    checkCourseUnitResult(1);
});
$('body').on('click', '.downloadEnrollSubjectsBetaCertificatePdf', function (e) {
    e.preventDefault();
    checkCertificateRequirements(1);
    return false;
});

$('body').on('click', '.downloadECertificatePdf', function (e) {
    e.preventDefault();
    checkCourseUnitResult(3);
});

$('body').on('click', '.downloadCertificateWithWaterMarkPdf', function (e) {
    e.preventDefault();
    ajaxActionV2(
        'api/verify-letter-watermark',
        'POST',
        { college_id: collegeId },
        function (response) {
            if (response.status == 'success') {
                checkCourseUnitResult(2);
            } else {
                notificationDisplay(response.message, '', 'error');
                return false;
            }
        }
    );
});

$('body').on('click', '.previewOfferLetterBtn', function (e) {
    e.preventDefault();
    let tempSelectedDataArr = selectedDataArr;
    if ($(this).hasAttr('data-sc-id')) {
        tempSelectedDataArr = {
            college_id: collegeId,
            student_id: studentId,
            student_course_id: $(this).attr('data-sc-id'),
        };
    }
    ajaxActionV2('api/preview-offer-letter', 'POST', tempSelectedDataArr, function (response) {
        if (response.data > 0) {
            let url = `${site_url}preview-student-offer-letter/${response.data}/${studentId}/${selectedStudCourseID}`;
            window.open(
                url,
                'student-offer-letter',
                'width=400, height=420, left=250, top=250, scrollbars=1, resizable=1'
            );
        } else {
            notificationDisplay('Course Data not found', '', 'error');
        }
    });

    //let encodedDataArr = encodeURIComponent(JSON.stringify(selectedDataArr));
    //window.location.href = '/api/preview-offer-letter?data=' + encodedDataArr;

    //let url = 'http://iie.galaxy360.com/student-offer-letter/21/17/526';
    //window.open(url, "student-offer-letter", "width=400, height=420, left=250, top=250, scrollbars=1, resizable=1");
});

$('body').on('click', '.downloadOfferLetterBtn', function (e) {
    e.preventDefault();
    let tempSelectedDataArr = selectedDataArr;
    let offerLettertype = $(this).data('type');

    ajaxActionV2('api/preview-offer-letter', 'POST', tempSelectedDataArr, function (response) {
        if (response.data > 0) {
            let url = '';
            if (offerLettertype == 'single') {
                url = `${site_url}student-offer-letter-pdf/${response.data}/${studentId}/${selectedStudCourseID}/download`;
            } else {
                url = `${site_url}offer-letter-pdf-new/${response.data}/${studentId}/${selectedStudCourseID}/download`;
            }

            window.location.href = url;
        } else {
            notificationDisplay('Course Data not found', '', 'error');
        }
    });
});

$('body').on('click', '.generateAgentInvoiceBtn', function (e) {
    e.preventDefault();
    let tempSelectedDataArr = selectedDataArr;
    if ($(this).hasAttr('data-sc-id')) {
        tempSelectedDataArr = {
            college_id: collegeId,
            student_id: studentId,
            student_course_id: $(this).attr('data-sc-id'),
        };
    }
    let encodedDataArr = encodeURIComponent(JSON.stringify(tempSelectedDataArr));
    window.location.href = site_url + 'api/generate-agent-invoice-pdf?data=' + encodedDataArr;
});

/*$('body').on('click', '.offerCommunicationHeaderBtn', function (e){
    let tempSelectedDataArr = selectedDataArr;
    if ($(this).hasAttr("data-sc-id")) {
        tempSelectedDataArr = { 'college_id': collegeId, 'student_id': studentId, 'student_course_id': $(this).attr('data-sc-id') };
    }
    e.preventDefault();
    manageOfferCommunicationModal(tempSelectedDataArr);
});*/

$('body').on('click', '.combinedCourseStudentInvoiceBtn', function (e) {
    e.preventDefault();
    let dataArr = {
        college_id: collegeId,
        student_id: studentId,
        student_course_id: selectedStudCourseID,
    };
    let encodedDataArr = encodeURIComponent(JSON.stringify(dataArr));
    window.location.href =
        site_url + 'api/generate-combined-course-invoice-pdf?data=' + encodedDataArr;
});

$('body').on('click', '.addCoeNumberBtn', function (e) {
    ajaxActionV2('api/get-student-coe-details', 'POST', selectedDataArr, function (response) {
        $('.applicable').attr('value', response.data.coe_applicable);
        $('#code_no').val(response.data.coe_name);
        $('.attachFileCOEDocument').text(
            response.data.coe_image ? response.data.coe_image : 'Attach Files'
        );
        if (response.data.coe_applicable == '1') {
            $('#is_applicable').prop('checked', true);
            $('#code_no').attr('disabled', true);
        } else {
            $('#is_applicable').prop('checked', false);
            $('#code_no').attr('disabled', false);
        }
        if (response.data.coe_image != '') {
            $('#downLoadCoeFile').html(
                '<a download href="' +
                    site_url +
                    'uploads/' +
                    collegeId +
                    '/StudentCOE/' +
                    studentId +
                    '/' +
                    response.data.coe_image +
                    '"  class="flex space-x-2 items-center justify-start">' +
                    '<img class="rounded-lg" src="' +
                    site_url +
                    '/v2/img/download_arrow.svg" />' +
                    '</a>'
            );
        }
        kendoWindowOpen('#addCOENumberModal');
    });
});

$('body').on('change', '.applicable', function (e) {
    if ($('.applicable:checked').val()) {
        console.log('in');
        $(this).attr('value', '1');
        $('#code_no').addClass('bg-gray-100');
        $('#code_no').attr('disabled', true);
        $('#code_no').val('N/A');
    } else {
        $(this).attr('value', '0');
        $('#code_no').removeClass('bg-gray-100');
        $('#code_no').attr('disabled', false);
    }
});

$('body').on('click', '.saveCOENumberBtn', function (e) {
    var formData1 = new FormData($('#addCOENumberForm')[0]);
    formData1.append('student_id', studentId);
    formData1.append('college_id', collegeId);
    formData1.append('student_course_id', selectedStudCourseID);
    ajaxcallwithMethodFileKendo(
        site_url + 'api/save-student-coe-details',
        formData1,
        'POST',
        function (output) {
            closeKendoWindow('#addCOENumberModal');
            notificationDisplay(output.message, '', output.status);
        }
    );
});

$('input[name="COEDocument"]').change(function (e) {
    $('.attachFileCOEDocument').text(e.target.files[0].name);
});

function renderHTimelineV2(dataArray, selector, dateInterval = 250) {
    const $timeline = $(selector);
    $timeline.empty();
    try {
        $(selector).horizontalTimeline('destroy');
    } catch (error) {
        console.warn('Error attempting to destroy horizontalTimeline:', error);
    }

    $timeline.append(`<div class="events-content">
                                    <ol class="timelineEvents"></ol>
                                </div>`);
    const $eventsContainer = $timeline.find('.timelineEvents');
    // Check if dataArray is valid
    if (
        !dataArray ||
        !Array.isArray(dataArray) ||
        dataArray.length === 0 ||
        dataArray.every((item) => item == null)
    ) {
        $eventsContainer.append(`<div class="flex flex-col items-center gap-3 rounded-lg border border-gray-100 py-8 text-center px-4">
            <span class="k-font-icon k-i-clipboard icon-color-blue !text-[2rem]"></span>
            <h4 class="text-center font-medium text-gray-800 text-sm leading-5">No Notes Found
            </h4>
        </div>`);
        $timeline.hide();
        return;
    } else {
        $timeline.show();
    }
    // Sort by unit_name
    dataArray.sort((a, b) => {
        const aName = a.unit_name || '';
        const bName = b.unit_name || '';
        return aName.localeCompare(bName);
    });

    dataArray.forEach((item, index) => {
        // Validate item properties
        if (!item || typeof item !== 'object') {
            console.warn(`Invalid item at index ${index}:`, item);
            return;
        }

        // Ensure required fields have fallback values
        const unitName = item.unit_name || 'Untitled';
        const unitCode = item.unit_code || 'N/A';
        const startDate = item.activity_start_date || 'N/A';
        const endDate = item.activity_finish_date || 'N/A';
        const outcome = item.final_outcome || 'N/A';

        const li = document.createElement('li');
        li.className = index === 0 ? 'selected' : '';

        // Use synthetic date as fallback if library requires date
        const syntheticDate = new Date('2025-01-01');
        syntheticDate.setDate(syntheticDate.getDate() + index);
        const formattedDate = syntheticDate.toLocaleDateString('en-GB');

        const timelineData = {
            date: formattedDate, // Fallback for date-based libraries
            customDisplay: `${unitName}<hr />${startDate}`,
            customOrder: index, // Optional, for custom sorting
        };
        li.setAttribute('data-horizontal-timeline', JSON.stringify(timelineData));
        console.log(`Timeline data for item ${index}:`, timelineData);

        li.innerHTML = `
        <div class="p-4 space-y-2 bg-white rounded shadow border border-gray-200">
            <div class="flex space-x-3 items-center">
                <div class="w-2 h-6 bg-green-500"></div>
                <span class="text-lg font-medium leading-6 text-gray-900 unit_name truncate" title="${unitName}">${unitName}</span>
            </div>
            <div class="grid grid-cols-4 gap-2">
                <div class="flex flex-col justify-start items-start">
                    <span class="text-xs font-medium leading-4 text-gray-500">Unit code</span>
                    <span class="text-xs 3xl:text-sm leading-5 text-gray-700">${unitCode}</span>
                </div>
                <div class="flex flex-col justify-start items-start">
                    <span class="text-xs font-medium leading-4 text-gray-500">Start date</span>
                    <span class="text-xs 3xl:text-sm leading-5 text-gray-700">${startDate}</span>
                </div>
                <div class="flex flex-col justify-start items-start">
                    <span class="text-xs font-medium leading-4 text-gray-500">End date</span>
                    <span class="text-xs 3xl:text-sm leading-5 text-gray-700">${endDate}</span>
                </div>
                <div class="flex flex-col justify-start items-start">
                    <span class="text-xs font-medium leading-4 text-gray-500">Competency</span>
                    <span class="text-xs 3xl:text-sm leading-5 text-gray-700">${outcome}</span>
                </div>
            </div>
            <span class="k-timeline-card-callout k-card-callout k-callout-n">
            </span>
        </div>
        `;

        $eventsContainer.append(li);
    });
    try {
        $(selector).horizontalTimeline({
            iconClass: {
                base: 'fas fa-3x',
                scrollLeft: 'hidden',
                scrollRight: 'hidden',
                prev: 'k-button k-timeline-arrow k-timeline-arrow-left',
                next: 'k-button k-timeline-arrow k-timeline-arrow-right',
                pause: 'fa-pause-circle',
                play: 'fa-play-circle',
            },
            iconBaseClass: '',
            scrollLeft_iconClass: 'hidden',
            scrollRight_iconClass: 'hidden',
            prev_iconClass: 'k-icon k-i-arrow-60-left',
            next_iconClass: 'k-icon k-i-arrow-60-right',
            desktopDateIntervals: dateInterval,
            tabletDateIntervals: 150,
            mobileDateIntervals: 120,
            minimalFirstDateInterval: true,
            dateIntervals: {
                desktop: dateInterval,
                tablet: 150,
                mobile: 120,
                minimal: true,
            },
        });
    } catch (error) {
        console.error('Error initializing horizontalTimeline:', error);
    }
}

function fetchCurrentCourseApi(id, subject_name) {
    // let subject_name = $(e.currentTarget)
    //     .closest('tr')
    //     .find('td:first-child')
    //     .find('div')
    //     .attr('data-subject');
    ajaxActionV2(
        'api/get-stud-subject-enroll-history',
        'POST',
        { student_subject_enrolment_id: id },
        function (response) {
            kendoWindowOpen('#currentCourseHistoryModal');
            $('#currentCourseHistoryModal').prev().find('.k-window-title').text(subject_name);
            let currentCourseHistoryTemplate = kendo.template(
                $('#currentCourseHistoryTemplate').html()
            )(response.data);
            $(document).find('#currentCourseHistoryDiv').html(currentCourseHistoryTemplate);
        }
    );
}

$(document).on('click', '.btn-history', function () {
    const id = $(this).data('id');
    const subject_name = $(this).data('subject');
    fetchCurrentCourseApi(id, subject_name);
    kendoWindowOpen('#currentCourseHistoryModal');
});

$(document).on('click', '.unitSyncToMoodleBtn', function (e) {
    const unit_id = $(this).attr('data-unit_id');
    kendoWindowOpen('#unitSyncToMoodleFromCourseModal');
    unitSyncWithMoodleHandler(e, currentCourseGridSelector, unit_id);
});

$(document).on('click', '.enrollStudentToMoodleBtn', function (e) {
    const id = $(this).data('id');
    enrollStudentSyncWithMoodleHandler(e, id);
    kendoWindowOpen('#enrollStudentSyncToMoodleFromCourseModal');
});

$('#currentCourseHistoryModal').kendoWindow(modalOpen());
$('#unitSyncToMoodleFromCourseModal').kendoWindow(modalOpen());
$('#enrollStudentSyncToMoodleFromCourseModal').kendoWindow(modalOpen());
addModalClassToWindows([
    '#currentCourseHistoryModal',
    '#unitSyncToMoodleFromCourseModal',
    '#enrollStudentSyncToMoodleFromCourseModal',
]);
