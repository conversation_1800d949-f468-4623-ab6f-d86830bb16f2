var reportsLetter = true;
$(document).ready(function () {
    $.ajaxSetup({
        headers: {
            "X-CSRF-TOKEN": $('meta[name="csrf-token"]').attr("content"),
        },
    });

    $("#loader").kendoLoader();
    kendo.ui.progress.messages = {
        loading:
            '<div class="vue-simple-spinner animate-spin" style="position:absolute;top:50%;left:50%;transfrom:translate(-50%,-50%); margin: 0px auto; border-radius: 100%; border-color: rgb(50, 157, 243) rgb(238, 238, 238) rgb(238, 238, 238); border-style: solid; border-width: 3px; border-image: none 100% / 1 / 0 stretch; width: 30px; height: 30px;"></div>',
    };

    $("#manageReportList").kendoGrid({
        dataSource: customDataSource("api/manage-reports-data", {
            report_name: { type: "string" },
            category: { type: "string" },
            sub_category_name: { type: "string" },
            access_roles: { type: "string" },
        }),
        pageable: customPageableArr(),
        dataBound: function (e) {
            setTimeout(function () {
                setFilterIcon("#manageReportList");
            }, 100);
        },
        filterable: true,
        sortable: true,
        resizable: true,
        columns: [
            {
                template:
                    "<div class='flex items-center text-sm leading-5 font-normal text-gray-600 action-div'>#: report_name #</div>",
                headerTemplate:
                    "<a class='k-link text-xs font-medium tracking-wide leading-none text-gray-500' style='cursor: default !important;'>Report Name</a>",
                field: "report_name",
                title: "Report Name",
                filterable: false,
            },
            {
                template:
                    "<div class='flex items-center text-sm leading-5 font-normal text-gray-600 action-div'>#: category #</div>",
                headerTemplate:
                    "<a class='k-link text-xs font-medium tracking-wide leading-none text-gray-500' style='cursor: default !important;'>Category</a>",
                field: "category",
                title: "Category",
                width: 200,
                filterable: false,
                sortable: false,
            },
            {
                template:
                    "<div class='flex items-center text-sm leading-5 font-normal text-gray-600 action-div'>#: sub_category_name #</div>",
                headerTemplate:
                    "<a class='k-link text-xs font-medium tracking-wide leading-none text-gray-500' style='cursor: default !important;'>Sub Category</a>",
                field: "sub_category_name",
                title: "Sub Category",
                width: 200,
                filterable: false,
                sortable: false,
            },
            {
                template:
                    "<div class='flex items-center text-sm leading-5 font-normal text-gray-600 action-div'>Reports  : #: ((access_roles  !=null) ? access_roles  :'')  #</div>",
                headerTemplate:
                    "<a class='k-link text-xs font-medium tracking-wide leading-none text-gray-500' style='cursor: default !important;'>Type</a>",
                field: "access_roles",
                title: "Type",
                filterable: false,
                sortable: false,
            },
            {
                headerTemplate:
                    "<a class='k-link text-xs font-medium tracking-wide leading-none text-gray-500' style='cursor: default !important;'>Action</a>",
                template: function (dataItem) {
                    return manageAction(dataItem.id);
                },
                width: 100,
                field: "action",
                title: "Actions",
                filterable: false,
                sortable: false,
            },
        ],
        noRecords: noRecordTemplate(),
    });
    customGridHtml("#manageReportList");

    function manageAction(id) {
        return (
            '<div class="action-div flex justify-start items-center space-x-1">' +
            '<div class="editReport action-menu flex-col space-x-3 items-start justify-start p-2 cursor-pointer" data-id = "' +
            id +
            '"><span class="k-icon k-i-edit k-icon-edit"></span></div>' +
            "</div>"
        );
    }

    function kendoWindowOpen(windowID) {
        const isUpdateReport = windowID === "#updateReportModal";
        let kendoWindow = $(document).find(windowID);
        kendoWindow.getKendoWindow().open();
        kendoWindow
            .parent("div")
            .find(".k-window-titlebar")
            .addClass(
                "titlebar-sms-modal bg-gradient-to-l from-green-400 to-blue-500",
            )
            .find(".k-window-title")
            .addClass("text-lg font-medium leading-normal text-white");
        if (isUpdateReport) {
            kendoWindow.parent("div").addClass("k-modal-window");
        }
    }

    function openCenterWindow(
        titleText,
        widthVal = 34,
        topVal = 15,
        leftVal = 33,
    ) {
        return {
            title: titleText,
            width: widthVal + "%",
            // height: "70%",
            actions: ["close"],
            draggable: false,
            resizable: false,
            modal: true,
            position: {
                top: topVal + "%",
                left: leftVal + "%",
            },
            animation: defaultCloseAnimation(),
        };
    }
    $("#reportDetailsModal").kendoWindow(
        openCenterWindow("Reports Details", 70, 5, 15),
    );
    $("#generateReportModal").kendoWindow(
        defaultWindowSlideFormat("Generate Reports", 80),
    );
    $("#generateReportLetterModal").kendoWindow(
        defaultWindowSlideFormat("Generate Reports Letter", 80),
    );
    $("#updateReportModal").kendoWindow(
        openCenterWindow("Update Reports", 50, 10, 33),
    );

    $("body").on("click", ".generateReports", function () {
        kendoWindowOpen("#generateReportModal");
    });

    $("body").on("click", ".generateReportsLetter", function () {
        kendoWindowOpen("#generateReportLetterModal");
    });

    $("body").on("click", ".editReport", function (e) {
        // $("#updateReportForm")[0].reset();
        let dataItem = $("#manageReportList")
            .data("kendoGrid")
            .dataItem(e.target.closest("tr"));
        let updateReportModal = $(document).find("#updateReportForm");
        updateReportModal.find("#report_id").val(dataItem.id);
        updateReportModal.find("#report_name").val(dataItem.report_name);
        updateReportModal.find("#category").val(dataItem.category);
        updateReportModal.find("#sub_category").val(dataItem.sub_category_name);
        if (dataItem.roles_id) {
            let roles = dataItem.roles_id.split(", ");
            updateReportModal
                .find("#role_id")
                .data("kendoCheckBoxGroup")
                .value(roles);
        } else {
            updateReportModal
                .find("#role_id")
                .data("kendoCheckBoxGroup")
                .value("");
        }
        kendoWindowOpen("#updateReportModal");
    });

    function setDropdownList(fieldID, api_url, postArr = {}) {
        $("#" + fieldID).kendoDropDownList({
            autoWidth: true,
            dataTextField: "Name",
            dataValueField: "Id",
            filter: "contains",
            dataSource: getDropdownDataSource(api_url, postArr),
            dataBound: function (e) {
                this.select(0);
                this.trigger("change");
                this.trigger("select");
                onchangeSetDropdownList(
                    fieldID,
                    $("#" + fieldID)
                        .data("kendoDropDownList")
                        .value(),
                );
            },
            select: function (e) {
                if (e.dataItem) {
                    onchangeSetDropdownList(fieldID, e.dataItem.Id);
                }
            },
            change: function (e) {
                if (this.value()) {
                    onchangeShowHide(fieldID, this.value());
                }
            },
        });
    }

    function onchangeSetDropdownList(fieldID, value) {
        if (fieldID == "course_id") {
            let postArr = {
                course_id: value,
                course_type: $("#course_type")
                    .data("kendoDropDownList")
                    .value(),
            };
            setDropdownList("year", "get-course-year", postArr);
            setDropdownList("semester_id", "get-course-semester", postArr);
        } else if (fieldID == "year") {
            let postArr = {
                year: value,
                course_id: $("#course_id").data("kendoDropDownList").value(),
                course_type: $("#course_type")
                    .data("kendoDropDownList")
                    .value(),
            };
            setDropdownList("intake_date", "get-course-intake-date", postArr);
        } else if (fieldID == "semester_id") {
            let postArr = { semester_id: value };
            setDropdownList("term", "get-course-term", postArr);
        } else if (fieldID == "term") {
            let postArr = {
                term: value,
                semester_id: $("#semester_id")
                    .data("kendoDropDownList")
                    .value(),
            };
            setDropdownList("subject_id", "get-course-subject", postArr);
        } else if (fieldID == "subject_id") {
            let postArr = {
                subject_id: value,
                semester_id: $("#semester_id")
                    .data("kendoDropDownList")
                    .value(),
            };
            setDropdownList("batch", "get-course-batch", postArr);
        } else if (fieldID == "batch") {
            let postArr = {
                batch: value,
                term: $("#term").data("kendoDropDownList").value(),
                subject_id: $("#subject_id").data("kendoDropDownList").value(),
                semester_id: $("#semester_id")
                    .data("kendoDropDownList")
                    .value(),
            };
            setDropdownList("class_id", "get-course-class", postArr);
        } else if (fieldID == "letter_course_id") {
            let postArr = {
                course_id: value,
                course_type: $("#letter_course_type")
                    .data("kendoDropDownList")
                    .value(),
            };
            setDropdownList(
                "letter_semester_id",
                "get-course-semester",
                postArr,
            );
        } else if (fieldID == "letter_report_id") {
            let postArr = { report_id: value };
            setDropdownList(
                "report_letter_id",
                "get-report-letter-list",
                postArr,
            );
        }
    }

    function onchangeShowHide(fieldID, value) {
        if (fieldID == "report_id") {
            $("#from_date-form-label").closest(".k-form-field").hide();
            $("#to_date-form-label").closest(".k-form-field").hide();
            $("#value-form-label").closest(".k-form-field").hide();
            $("#course_type-form-label").closest(".k-form-field").hide();
            $("#course_id-form-label").closest(".k-form-field").hide();
            $("#status-form-label").closest(".k-form-field").hide();
            $("#date-form-label").closest(".k-form-field").hide();
            $("#agent_id-form-label").closest(".k-form-field").hide();
            $("#agent_status-form-label").closest(".k-form-field").hide();
            $("#country_id-form-label").closest(".k-form-field").hide();
            $("#year-form-label").closest(".k-form-field").hide();
            $("#intake_date-form-label").closest(".k-form-field").hide();
            $("#semester_id-form-label").closest(".k-form-field").hide();
            $("#term-form-label").closest(".k-form-field").hide();
            $("#subject_id-form-label").closest(".k-form-field").hide();
            $("#attendance_date-form-label").closest(".k-form-field").hide();
            $("#batch-form-label").closest(".k-form-field").hide();
            $("#class_id-form-label").closest(".k-form-field").hide();
            if (value == 1 || value == 3 || value == 4) {
                $("#from_date-form-label").closest(".k-form-field").show();
                $("#to_date-form-label").closest(".k-form-field").show();
            } else if (value == 2) {
                $("#course_type-form-label").closest(".k-form-field").show();
                $("#course_id-form-label").closest(".k-form-field").show();
                $("#status-form-label").closest(".k-form-field").show();
            } else if (value == 5) {
                $("#value-form-label").closest(".k-form-field").show();
            } else if (value == 6 || value == 8) {
                $("#from_date-form-label").closest(".k-form-field").show();
                $("#to_date-form-label").closest(".k-form-field").show();
            } else if (value == 7) {
                $("#date-form-label").closest(".k-form-field").show();
            } else if (value == 9) {
                $("#course_type-form-label").closest(".k-form-field").show();
                $("#agent_id-form-label").closest(".k-form-field").show();
                $("#from_date-form-label").closest(".k-form-field").show();
                $("#to_date-form-label").closest(".k-form-field").show();
            } else if (value == 10) {
                $("#agent_status-form-label").closest(".k-form-field").show();
                $("#country_id-form-label").closest(".k-form-field").show();
            } else if (value == 11) {
                $("#course_type-form-label").closest(".k-form-field").show();
                $("#status-form-label").closest(".k-form-field").show();
                $("#agent_id-form-label").closest(".k-form-field").show();
                $("#from_date-form-label").closest(".k-form-field").show();
                $("#to_date-form-label").closest(".k-form-field").show();
            } else if (value == 12) {
                $("#course_type-form-label").closest(".k-form-field").show();
                $("#course_id-form-label").closest(".k-form-field").show();
            } else if (value == 13 || value == 14 || value == 15) {
                $("#from_date-form-label").closest(".k-form-field").show();
                $("#to_date-form-label").closest(".k-form-field").show();
            } else if (value == 16) {
                $("#course_id-form-label").closest(".k-form-field").show();
                $("#course_type-form-label").closest(".k-form-field").show();
                $("#year-form-label").closest(".k-form-field").show();
                $("#intake_date-form-label").closest(".k-form-field").show();
            } else if (value == 17) {
                $("#course_type-form-label").closest(".k-form-field").show();
                $("#status-form-label").closest(".k-form-field").show();
                $("#from_date-form-label").closest(".k-form-field").show();
                $("#to_date-form-label").closest(".k-form-field").show();
            } else if (value == 18 || value == 19) {
                $("#course_type-form-label").closest(".k-form-field").show();
                $("#course_id-form-label").closest(".k-form-field").show();
                $("#semester_id-form-label").closest(".k-form-field").show();
            } else if (value == 20) {
                $("#course_type-form-label").closest(".k-form-field").show();
                $("#course_id-form-label").closest(".k-form-field").show();
                $("#from_date-form-label").closest(".k-form-field").show();
                $("#to_date-form-label").closest(".k-form-field").show();
            } else if (value == 21) {
                $("#course_type-form-label").closest(".k-form-field").show();
                $("#course_id-form-label").closest(".k-form-field").show();
                $("#semester_id-form-label").closest(".k-form-field").show();
                $("#term-form-label").closest(".k-form-field").show();
                $("#subject_id-form-label").closest(".k-form-field").show();
                $("#attendance_date-form-label")
                    .closest(".k-form-field")
                    .show();
                $("#batch-form-label").closest(".k-form-field").show();
                $("#class_id-form-label").closest(".k-form-field").show();
            } else if (value == 22) {
                $("#course_type-form-label").closest(".k-form-field").show();
                $("#course_id-form-label").closest(".k-form-field").show();
                $("#semester_id-form-label").closest(".k-form-field").show();
                $("#status-form-label").closest(".k-form-field").show();
            } else if (value == 23) {
                $("#course_type-form-label").closest(".k-form-field").show();
                $("#course_id-form-label").closest(".k-form-field").show();
                $("#semester_id-form-label").closest(".k-form-field").show();
                $("#term-form-label").closest(".k-form-field").show();
                $("#subject_id-form-label").closest(".k-form-field").show();
                $("#batch-form-label").closest(".k-form-field").show();
                $("#class_id-form-label").closest(".k-form-field").show();
            } else if (value == 24) {
                $("#course_type-form-label").closest(".k-form-field").show();
                $("#course_id-form-label").closest(".k-form-field").show();
                $("#status-form-label").closest(".k-form-field").show();
                $("#from_date-form-label").closest(".k-form-field").show();
                $("#to_date-form-label").closest(".k-form-field").show();
            }
        }
    }

    $("#updateReportForm").kendoForm({
        orientation: "vertical",
        layout: "grid",
        type: "group",
        grid: { cols: 6, gutter: 16 },
        items: [
            {
                field: "report_name",
                label: "Report Name",
                editor: function (container, options) {
                    $(
                        '<input type="text" id="' +
                            options.field +
                            '" name="' +
                            options.field +
                            '"  disabled />',
                    )
                        .appendTo(container)
                        .kendoTextBox();
                },
                colSpan: 6,
            },
            {
                field: "category",
                label: "Category",
                editor: function (container, options) {
                    $(
                        '<input type="text" id="' +
                            options.field +
                            '" name="' +
                            options.field +
                            '"  disabled />',
                    )
                        .appendTo(container)
                        .kendoTextBox();
                },
                colSpan: 3,
            },
            {
                field: "sub_category",
                label: "Sub Category",
                editor: function (container, options) {
                    $(
                        '<input type="text" id="' +
                            options.field +
                            '" name="' +
                            options.field +
                            '"  disabled />',
                    )
                        .appendTo(container)
                        .kendoTextBox();
                },
                colSpan: 3,
            },
            {
                field: "role_id",
                editor: "CheckBoxGroup",
                label: "Access Level",
                validation: { required: true },
                editorOptions: {
                    items: [
                        { label: "Account", value: "1" },
                        { label: "Admin", value: "2" },
                        { label: "DOS", value: "3" },
                        { label: "DOS-ELICOS", value: "4" },
                        { label: "DOS-HS", value: "5" },
                        { label: "DOS-VET", value: "6" },
                        { label: "IT", value: "7" },
                        { label: "Marketing", value: "8" },
                        { label: "Sadmin", value: "9" },
                        { label: "Staff", value: "10" },
                        { label: "StudentServices", value: "11" },
                        { label: "Teacher", value: "12" },
                        { label: "Student", value: "13" },
                        { label: "Agent", value: "14" },
                    ],
                    layout: "horizontal",
                    labelPosition: "after",
                },
                colSpan: 6,
            },
        ],
        buttonsTemplate:
            '<div class="w-full inline-flex space-x-4 items-center justify-end py-2 px-1">\n' +
            '<div class="float-right flex space-x-4 items-center justify-end">\n' +
            '<button type="button" class="flex justify-center px-6 py-2 bg-white shadow border hover:shadow-lg rounded-lg  border-gray-300 focus:ring-2 focus:ring-offset-2 focus:ring-offset-white focus:ring-gray-400 cancelBtn">\n' +
            '<p type="button" class="text-sm font-medium leading-4 text-gray-700">Cancel</p>\n' +
            "</button>\n" +
            '<input type="hidden" id="report_id" name="report_id">\n' +
            '<button type="button" class="updateReportBtn flex justify-center h-full px-3 py-2 bg-primary-blue-500 hover:shadow-lg rounded-lg focus:ring-2 focus:ring-offset-2 focus:ring-offset-white focus:ring-primary-blue-600">\n' +
            '<p class="text-sm font-medium leading-4 text-white">Update Report</p>\n' +
            "</button>\n" +
            "</div>\n" +
            "</div>",
    });

    $("body").on("click", ".updateReportBtn", function (e) {
        let serializeArr = $(document)
            .find("#updateReportForm")
            .find("input[name], select[name], textarea[name]")
            .serializeArray();
        let dataArr = {};
        $(serializeArr).each(function (i, field) {
            if (dataArr[field.name] !== undefined) {
                if (!dataArr[field.name].push) {
                    dataArr[field.name] = [dataArr[field.name]];
                }
                dataArr[field.name].push(field.value);
            } else {
                dataArr[field.name] = field.value;
            }
        });
        ajaxcallwithMethodKendo(
            site_url + "api/update-report-data",
            dataArr,
            "POST",
            function (resultData) {
                $(document).find("#updateReportModal").getKendoWindow().close();
                $("#manageReportList").data("kendoGrid").refresh();
                $("#manageReportList").data("kendoGrid").dataSource.read();
                notificationDisplay(resultData.message, "", resultData.status);
            },
        );
    });

    $("body").on("click", ".cancelBtn", function (e) {
        e.preventDefault();
        $(this).closest(".k-window-content").data("kendoWindow").close();
    });

    $("#generateReportForm").kendoForm({
        orientation: "vertical",
        layout: "grid",
        type: "group",
        grid: { cols: 6, gutter: 16 },
        formData: {
            selection_type: "All Reports",
            from_date: new Date(),
            to_date: new Date(),
            date: new Date(),
            attendance_date: new Date(),
        },
        items: [
            {
                field: "selection_type",
                editor: "RadioGroup",
                label: "Selection Type",
                editorOptions: {
                    items: ["All Reports", "Favourite Reports"],
                    layout: "horizontal",
                    labelPosition: "after",
                },
                validation: { required: true },
                colSpan: 6,
            },
            {
                field: "category_id",
                editor: "DropDownList",
                label: "Select Category",
                colSpan: 6,
                editorOptions: {
                    dataSource: category,
                    dataValueField: "Id",
                    dataTextField: "Name",
                    filter: "contains",
                    filterInput: {
                        width: "100%",
                    },
                    dataBound: function (e) {
                        setTimeout(() => {
                            this.select(0);
                            this.trigger("change");
                            this.trigger("select");
                            let postArr = {
                                category_id: $("#category_id")
                                    .data("kendoDropDownList")
                                    .value(),
                            };
                            setDropdownList(
                                "report_id",
                                "get-report-name",
                                postArr,
                            );
                        }, 1000);
                    },
                    select: function (e) {
                        if (e.dataItem) {
                            let postArr = { category_id: e.dataItem.Id };
                            setDropdownList(
                                "report_id",
                                "get-report-name",
                                postArr,
                            );
                        }
                    },
                    change: function (e) {},
                },
            },
            {
                field: "report_id",
                label: "Select Report",
                editor: "DropDownList",
                colSpan: 6,
            },
            {
                field: "value",
                label: "Value",
                colSpan: 3,
            },
            {
                field: "course_type",
                editor: "DropDownList",
                label: "Course Type",
                colSpan: 6,
                editorOptions: {
                    dataSource: courseType,
                    dataValueField: "Id",
                    dataTextField: "Name",
                    filter: "contains",
                    filterInput: {
                        width: "100%",
                    },
                    dataBound: function (e) {
                        setTimeout(() => {
                            this.select(0);
                            this.trigger("change");
                            this.trigger("select");
                            let postArr = {
                                course_type_id: $("#course_type")
                                    .data("kendoDropDownList")
                                    .value(),
                            };
                            setDropdownList(
                                "course_id",
                                "get-course-by-type",
                                postArr,
                            );
                        }, 1000);
                    },
                    select: function (e) {
                        if (e.dataItem) {
                            let postArr = { course_type_id: e.dataItem.Id };
                            setDropdownList(
                                "course_id",
                                "get-course-by-type",
                                postArr,
                            );
                        }
                    },
                },
            },
            {
                field: "course_id",
                editor: "DropDownList",
                label: "Course",
                colSpan: 6,
            },
            {
                field: "status",
                editor: "DropDownList",
                label: "Status",
                colSpan: 6,
                editorOptions: {
                    dataSource: statusArr,
                    dataValueField: "Id",
                    dataTextField: "Name",
                    filter: "contains",
                    filterInput: {
                        width: "100%",
                    },
                    dataBound: function (e) {
                        setTimeout(() => {
                            this.select(0);
                            this.trigger("change");
                            this.trigger("select");
                        }, 1000);
                    },
                },
            },
            {
                field: "agent_id",
                editor: "DropDownList",
                label: "Agent",
                colSpan: 6,
                editorOptions: {
                    dataSource: agentArr,
                    dataValueField: "Id",
                    dataTextField: "Name",
                    filter: "contains",
                    filterInput: {
                        width: "100%",
                    },
                    dataBound: function (e) {
                        setTimeout(() => {
                            this.select(0);
                            this.trigger("change");
                            this.trigger("select");
                        }, 1000);
                    },
                },
            },
            {
                field: "agent_status",
                editor: "DropDownList",
                label: "Agent Status",
                colSpan: 6,
                editorOptions: {
                    dataSource: agentStatusArr,
                    dataValueField: "Id",
                    dataTextField: "Name",
                    filter: "contains",
                    filterInput: {
                        width: "100%",
                    },
                    dataBound: function (e) {
                        setTimeout(() => {
                            this.select(0);
                            this.trigger("change");
                            this.trigger("select");
                        }, 1000);
                    },
                },
            },
            {
                field: "country_id",
                editor: "DropDownList",
                label: "Primary Country",
                colSpan: 6,
                editorOptions: {
                    dataSource: countryArr,
                    dataValueField: "Id",
                    dataTextField: "Name",
                    filter: "contains",
                    filterInput: {
                        width: "100%",
                    },
                    dataBound: function (e) {
                        setTimeout(() => {
                            this.select(0);
                            this.trigger("change");
                            this.trigger("select");
                        }, 1000);
                    },
                },
            },
            {
                field: "year",
                label: "Year",
                editor: "DropDownList",
                colSpan: 6,
            },
            {
                field: "intake_date",
                label: "Intake Date",
                editor: "DropDownList",
                colSpan: 6,
            },
            {
                field: "semester_id",
                label: "Semester",
                editor: "DropDownList",
                colSpan: 6,
            },
            {
                field: "term",
                label: "Term",
                editor: "DropDownList",
                colSpan: 6,
            },
            {
                field: "subject_id",
                label: "Subject",
                editor: "DropDownList",
                colSpan: 6,
            },
            {
                field: "batch",
                label: "Batch",
                editor: "DropDownList",
                colSpan: 6,
            },
            {
                field: "class_id",
                label: "Class",
                editor: "DropDownList",
                colSpan: 6,
            },
            {
                field: "from_date",
                editor: "DatePicker",
                label: "From Date",
                colSpan: 3,
            },
            {
                field: "to_date",
                editor: "DatePicker",
                label: "To Date",
                colSpan: 3,
            },
            {
                field: "date",
                editor: "DatePicker",
                label: "Enter Date",
                colSpan: 3,
            },
            {
                field: "attendance_date",
                editor: "DatePicker",
                label: "Last Attendance Date",
                colSpan: 3,
            },
        ],
        // buttonsTemplate:
        //     '<div class="w-full inline-flex space-x-4 items-center justify-end py-2">\n' +
        //     '<div class="float-right flex space-x-4 items-center justify-end">\n' +
        //     '<button type="submit" class="flex justify-center h-full px-4 py-2 bg-primary-blue-500 hover:shadow-lg rounded-lg focus:ring-2 focus:ring-offset-2 focus:ring-offset-white focus:ring-primary-blue-600">\n' +
        //     '<p class="text-sm font-medium leading-5 text-white">Search</p>\n' +
        //     "</button>\n" +
        //     "</div>\n" +
        //     "</div>",

        buttonsTemplate:
            '<div class="w-full inline-flex space-x-4 items-center justify-end py-2 px-1">\n' +
            '<div class="flex space-x-4 items-center justify-end">\n' +
            '<button type="button" id="exportReportData" class="flex justify-center px-6 py-2 bg-white shadow border hover:shadow-lg rounded-lg  border-gray-300 focus:ring-2 focus:ring-offset-2 focus:ring-offset-white focus:ring-gray-400 cancelBtn">\n' +
            '<p type="button" class="text-sm font-medium leading-4 text-gray-700">Export</p>\n' +
            "</button>\n" +
            '<input type="hidden" id="report_id" name="report_id">\n' +
            '<button type="submit" class="flex justify-center h-full px-3 py-2 bg-primary-blue-500 hover:shadow-lg rounded-lg focus:ring-2 focus:ring-offset-2 focus:ring-offset-white focus:ring-primary-blue-600">\n' +
            '<p class="text-sm font-medium leading-4 text-white">Search</p>\n' +
            "</button>\n" +
            "</div>\n" +
            "</div>",
        submit: function (ev) {
            let dataArr = {};
            let serializeArr = $(document)
                .find("#generateReportForm")
                .find("input[name], select[name], textarea[name]")
                .serializeArray();
            $(serializeArr).each(function (i, field) {
                dataArr[field.name] = field.value;
            });
            if (dataArr) {
                $.ajax({
                    type: "POST",
                    url: site_url + "api/manage-reports/ajaxAction",
                    dataType: "json",
                    data: { action: "generateReport", data: dataArr },
                    success: function (response) {
                        $("#reportList").html(response.html);
                        $(
                            "#report_" +
                                $("#report_id")
                                    .data("kendoDropDownList")
                                    .value(),
                        ).kendoGrid({
                            pageable: customPageableArr(),
                            filterable: false,
                            sortable: true,
                            resizable: true,
                            noRecords: noRecordTemplate(),
                        });
                        customGridHtml(
                            "#report_" +
                                $("#report_id")
                                    .data("kendoDropDownList")
                                    .value(),
                        );
                    },
                });
            }
            ev.preventDefault();
            return false;
        },
    });

    $("body").on("click", "#exportReportData", function (e) {
        $("#report_" + $("#report_id").data("kendoDropDownList").value())
            .data("kendoGrid")
            .saveAsExcel();
    });

    $("#reportLetterForm").kendoForm({
        orientation: "vertical",
        layout: "grid",
        type: "group",
        grid: { cols: 6, gutter: 16 },
        formData: {
            letter_selection_type: "All Reports",
        },
        items: [
            {
                field: "letter_selection_type",
                editor: "RadioGroup",
                label: "Selection Type",
                editorOptions: {
                    items: ["All Reports", "Favourite Reports"],
                    layout: "horizontal",
                    labelPosition: "after",
                },
                validation: { required: true },
                colSpan: 6,
            },
            {
                field: "letter_category_id",
                editor: "DropDownList",
                label: "Select Category",
                colSpan: 6,
                editorOptions: {
                    dataSource: category,
                    dataValueField: "Id",
                    dataTextField: "Name",
                    filter: "contains",
                    filterInput: {
                        width: "100%",
                    },
                    dataBound: function (e) {
                        setTimeout(() => {
                            this.select(0);
                            this.trigger("change");
                            this.trigger("select");
                            let postArr = {
                                category_id: $("#letter_category_id")
                                    .data("kendoDropDownList")
                                    .value(),
                            };
                            setDropdownList(
                                "letter_report_id",
                                "get-report-name",
                                postArr,
                            );
                        }, 1000);
                    },
                    select: function (e) {
                        if (e.dataItem) {
                            let postArr = { category_id: e.dataItem.Id };
                            setDropdownList(
                                "letter_report_id",
                                "get-report-name",
                                postArr,
                            );
                        }
                    },
                    change: function (e) {},
                },
            },
            {
                field: "letter_report_id",
                label: "Select Report",
                editor: "DropDownList",
                colSpan: 6,
            },
            {
                field: "letter_course_type",
                editor: "DropDownList",
                label: "Course Type",
                colSpan: 6,
                editorOptions: {
                    dataSource: courseType,
                    dataValueField: "Id",
                    dataTextField: "Name",
                    filter: "contains",
                    filterInput: {
                        width: "100%",
                    },
                    dataBound: function (e) {
                        setTimeout(() => {
                            this.select(0);
                            this.trigger("change");
                            this.trigger("select");
                            let postArr = {
                                course_type_id: $("#course_type")
                                    .data("kendoDropDownList")
                                    .value(),
                            };
                            setDropdownList(
                                "letter_course_id",
                                "get-course-by-type",
                                postArr,
                            );
                        }, 1000);
                    },
                    select: function (e) {
                        if (e.dataItem) {
                            let postArr = { course_type_id: e.dataItem.Id };
                            setDropdownList(
                                "letter_course_id",
                                "get-course-by-type",
                                postArr,
                            );
                        }
                    },
                },
            },
            {
                field: "letter_course_id",
                editor: "DropDownList",
                label: "Course",
                colSpan: 6,
            },
            {
                field: "letter_semester_id",
                label: "Semester",
                editor: "DropDownList",
                colSpan: 6,
            },
        ],
        buttonsTemplate:
            '<div class="w-full inline-flex space-x-4 items-center justify-end py-2">\n' +
            '<div class="float-right flex space-x-4 items-center justify-end">\n' +
            '<button type="submit" class="flex justify-center h-full px-4 py-2 bg-primary-blue-500 hover:shadow-lg rounded-lg focus:ring-2 focus:ring-offset-2 focus:ring-offset-white focus:ring-primary-blue-600">\n' +
            '<p class="text-sm font-medium leading-5 text-white">Search</p>\n' +
            "</button>\n" +
            "</div>\n" +
            "</div>",
        submit: function (ev) {
            if (reportsLetter) {
                reportsLetter = false;
                getLetterReportsData();
            } else {
                $("#reportLetterList").kendoGrid("destroy").empty();
                getLetterReportsData();
            }
            ev.preventDefault();
            return false;
        },
    });

    // var reportFlag = true;
    function getLetterReportsData() {
        $("#reportLetterList").kendoGrid({
            dataSource: customDataSource(
                "api/student-report-letter-data",
                {},
                {
                    course_id: $("#letter_course_id")
                        .data("kendoDropDownList")
                        .value(),
                    semester_id: $("#letter_semester_id")
                        .data("kendoDropDownList")
                        .value(),
                },
            ),
            dataBound: function (e) {
                setTimeout(function () {
                    setFilterIcon("#reportLetterList");
                }, 100);
            },
            pageable: customPageableArr(),
            change: onChange,
            // filterable: false,
            sortable: true,
            resizable: true,
            columns: [
                {
                    selectable: true,
                    width: "50px",
                },
                {
                    template:
                        "<div class='flex items-center text-sm leading-5 font-normal text-gray-600 action-div'>#: generated_stud_id #</div>",
                    field: "generated_stud_id",
                    title: "SEMESTER ID",
                },
                {
                    template:
                        "<div class='flex items-center text-sm leading-5 font-normal text-gray-600 action-div'>#: first_name #</div>",
                    field: "first_name",
                    title: "NAME",
                },
                {
                    template:
                        "<div class='flex items-center text-sm leading-5 font-normal text-gray-600 action-div'>#: student_type #</div>",
                    field: "student_type",
                    title: "ORIGIN",
                },
                {
                    template:
                        "<div class='flex items-center text-sm leading-5 font-normal text-gray-600 action-div'>#: email #</div>",
                    field: "email",
                    title: "EMAIL",
                },
                {
                    template:
                        "<div class='flex items-center text-sm leading-5 font-normal text-gray-600 action-div'>#: course_name #</div>",
                    field: "course_name",
                    title: "COURSE",
                },
                {
                    template:
                        "<div class='flex items-center text-sm leading-5 font-normal text-gray-600 action-div'>#: course_attempt #</div>",
                    field: "course_attempt",
                    title: "COURSE ATTEMPT",
                },
                {
                    template:
                        "<div class='flex items-center text-sm leading-5 font-normal text-gray-600 action-div'>#: start_date #</div>",
                    field: "start_date",
                    title: "START DATE",
                },
                {
                    template:
                        "<div class='flex items-center text-sm leading-5 font-normal text-gray-600 action-div'>#: finish_date #</div>",
                    field: "finish_date",
                    title: "FINISH DATE",
                },
                {
                    template:
                        "<div class='flex items-center text-sm leading-5 font-normal text-gray-600 action-div'>#: status #</div>",
                    field: "status",
                    title: "STATUS",
                },
                {
                    template:
                        "<div class='flex items-center text-sm leading-5 font-normal text-gray-600 action-div'>#: subject_group #</div>",
                    field: "subject_group",
                    title: "SUBJECT FAILED LIST",
                },
                {
                    template:
                        "<div class='flex items-center text-sm leading-5 font-normal text-gray-600 action-div'>#: unit_group #</div>",
                    field: "unit_group",
                    title: "UNIT FAILED",
                },
            ],
            noRecords: noRecordTemplate(),
        });
        customGridHtml("#reportLetterList");
    }

    function onChange(e) {
        let selectedStudent = [];
        let studentIds = [];
        var rows = e.sender.select();
        rows.each(function (e) {
            var grid = $("#reportLetterList").data("kendoGrid");
            var dataItem = grid.dataItem(this);
            studentIds.push(dataItem.id);
        });
        $(document).find("#studentIds").val(studentIds);
    }

    $("body").on("click", ".previewLetter", function () {
        var letter_id = $("#report_letter_id")
            .data("kendoDropDownList")
            .value();
        if (letter_id > 0) {
            ajaxActionV2(
                "api/get-report-letter-details",
                "POST",
                { report_letter_id: letter_id },
                function (response) {
                    $("#letterContent").html(response.data);
                    kendoWindowOpen("#reportDetailsModal");
                },
            );
        }
    });

    $("body").on("click", ".saveReportLetter", function () {
        let dataArr = {
            student_id: $("#studentIds").val(),
            letter_id: $("#report_letter_id").data("kendoDropDownList").value(),
        };
        let serializeArr = $(document)
            .find("#reportLetterForm")
            .find("input[name], select[name], textarea[name]")
            .serializeArray();
        $(serializeArr).each(function (i, field) {
            dataArr[field.name] = field.value;
        });
        ajaxActionV2(
            "api/save-report-letter",
            "POST",
            dataArr,
            function (response) {
                notificationDisplay(response.message, "", response.status);
            },
        );
    });
});
