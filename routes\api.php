<?php

/* timetable print attendance */

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use Stancl\Tenancy\Middleware\InitializeTenancyByDomain;
use Stancl\Tenancy\Middleware\PreventAccessFromCentralDomains;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

Route::get('/user', function (Request $request) {
    return $request->user();
});

// $router->get('priviewFile',     'v2\api\StudentApplicationController@previewDocument');
// $router->get('downloadDocument','v2\api\StudentApplicationController@downloadDocument');

Route::group(['middleware' => [/* InitializeTenancyByDomain::class, */ PreventAccessFromCentralDomains::class]], function () {
    // $router->get('updateStudentRedis','v2\api\StudentApplicationController@updateStudentRedis');
    Route::match(['get'], 'updateStudentRedis', ['as' => 'updateStudentRedis', 'uses' => 'v2\api\StudentApplicationController@updateStudentRedis']);
});

// Route::match(['post'],     'upload-student-profile-pic',             ['as' => 'upload-student-profile-pic',             'uses' => 'v2\api\StudentApiController@uploadStudentProfilePic']);
// Route::match(['post'],     'remove-student-profile-pic',             ['as' => 'remove-student-profile-pic',             'uses' => 'v2\api\StudentApiController@removeStudentProfilePic']);

// Route::match(['get','post'], 'upload-student-documents/{uid}/{cid}/{sid}', ['as' => 'upload-student-documents', 'uses' => 'v2\api\StudentApiController@uploadStudentDocuments']);

Route::group(['middleware' => ['auth:sanctum', 'mail', /* InitializeTenancyByDomain::class, */ PreventAccessFromCentralDomains::class]], function () {
    // $router->get('updateStudentRedis','v2\api\StudentApplicationController@updateStudentRedis');
    // Route::match(['get'], 'updateStudentRedis', ['as' => 'updateStudentRedis', 'uses' => 'v2\api\StudentApplicationController@updateStudentRedis']);
    require_once base_path(path: 'routes/v2/spa_api.php');
    Route::match(['get', 'post'], 'upload-documents', ['as' => 'upload-documents',                       'uses' => 'v2\api\StudentApplicationController@uploadDocuments']);
    Route::match(['get', 'post'], 'upload-usi-document', ['as' => 'upload-usi-document',                    'uses' => 'v2\api\StudentApplicationController@uploadUsiDocument']);
    Route::get('priviewFile', 'v2\api\StudentApplicationController@previewDocument');
    Route::match(['get', 'post'], 'downloadDocument', ['as' => 'downloadDocument',                'uses' => 'v2\api\StudentApplicationController@downloadDocument']);
    Route::match(['get', 'post'], 'upload-student-documents/{uid}/{cid}/{sid}', ['as' => 'upload-student-documents',        'uses' => 'v2\api\StudentApiController@uploadStudentDocuments']);

    Route::post('tasks/ajaxAction', 'Newdev\Api\TaskApiController@ajaxAction');
    Route::post('tasktemplate/ajaxAction', 'Newdev\Api\TasktemplateApiController@ajaxAction');
    Route::post('notification/ajaxAction', 'Newdev\Api\NotificationApiController@ajaxAction');

    Route::match(['get', 'post'], 'failed-attendance-jobs-data', ['as' => 'failed-attendance-jobs-data',             'uses' => 'v2\api\StudentApiController@failedAttendanceJobsData']);
    Route::match(['get', 'post'], 'failed-jobs-run', ['as' => 'failed-jobs-run',                         'uses' => 'v2\api\StudentApiController@failedAttendanceJobsRun']);

    // Constant & Common Data API
    Route::match(['post'], 'get-constant-data', ['as' => 'get-constant-data',                       'uses' => 'v2\api\ConstantApiController@ajaxAction']);
    Route::match(['post'], 'get-country-list', ['as' => 'get-country-list',                        'uses' => 'v2\api\CommonApiController@getCountry']);
    Route::match(['post'], 'get-nationality', ['as' => 'get-nationality',                         'uses' => 'v2\api\CommonApiController@getNationality']);
    Route::match(['get', 'post'], 'upload-file-text-editor', ['as' => 'upload-file-text-editor',                 'uses' => 'v2\api\CommonApiController@uploadDocuments']);

    // Student Orientation API
    Route::match(['post'], 'student-orientation-data', ['as' => 'student-orientation-data',                'uses' => 'v2\api\StudentOrientationApiController@studentData']);
    Route::match(['post'], 'student-orientation-filterby-html', ['as' => 'student-orientation-filterby-html',       'uses' => 'v2\api\StudentOrientationApiController@studentByFilter']);
    Route::match(['post'], 'student-orientation-campus-list', ['as' => 'student-orientation-campus-list',         'uses' => 'v2\api\StudentOrientationApiController@campusList']);
    // Route::match(['post'],          'student-send-mail',                ['as' => 'student-send-mail',                       'uses' => 'v2\api\StudentOrientationApiController@sendMail']);                    //TODO::not used
    Route::match(['post'], 'student-send-orientation-email', ['as' => 'student-send-orientation-email',          'uses' => 'v2\api\StudentOrientationApiController@studentOrientationSendMail']);
    Route::match(['get', 'post'], 'get-college-email-list', ['as' => 'get-college-email-list',                  'uses' => 'v2\api\StudentOrientationApiController@getCollegeEmailList']);
    Route::match(['post'], 'get-mail-template-list', ['as' => 'get-mail-template-list',                  'uses' => 'v2\api\StudentOrientationApiController@templateList']);
    Route::match(['post'], 'get-letter-template-list', ['as' => 'get-letter-template-list',                'uses' => 'v2\api\StudentOrientationApiController@letterTemplateList']);
    Route::match(['post'], 'get-mail-content', ['as' => 'get-mail-content',                        'uses' => 'v2\api\StudentOrientationApiController@mailContent']);
    Route::match(['post'], 'get-letter-content', ['as' => 'get-letter-content',                      'uses' => 'v2\api\StudentOrientationApiController@letterContent']);
    Route::match(['post'], 'get-letter-content-with-tag-value', ['as' => 'get-letter-content-with-tag-value',       'uses' => 'v2\api\StudentOrientationApiController@getLetterContentWithTagValue']);
    Route::match(['post'], 'update-attended-orientation', ['as' => 'update-attended-orientation',             'uses' => 'v2\api\StudentOrientationApiController@updateAttendedOrientation']);
    Route::match(['post'], 'confirm-course-started', ['as' => 'confirm-course-started',                  'uses' => 'v2\api\StudentOrientationApiController@confirmCourseStarted']);
    Route::match(['post'], 'sms-template-list', ['as' => 'sms-template-list-api',                   'uses' => 'v2\api\StudentOrientationApiController@smsTemplateList']);
    Route::match(['post'], 'get-sms-template-contain', ['as' => 'get-sms-template-contain',                'uses' => 'v2\api\StudentOrientationApiController@getSmsTemplateContain']);
    Route::match(['post'], 'student-sms-log', ['as' => 'student-sms-log',                         'uses' => 'v2\api\StudentOrientationApiController@sendStudentSmsLog']);
    Route::match(['post'], 'get-autocomplete', ['as' => 'get-autocomplete',                        'uses' => 'v2\api\StudentOrientationApiController@getAutocomplete']);
    Route::match(['post'], 'orientation-student-name', ['as' => 'orientation-student-name',                'uses' => 'v2\api\StudentOrientationApiController@getOrientationStudent']);
    Route::match(['post'], 'orientation-student-id', ['as' => 'orientation-student-id',                  'uses' => 'v2\api\StudentOrientationApiController@getOrientationStudentId']);
    Route::match(['post'], 'orientation-course-name', ['as' => 'orientation-course-name',                 'uses' => 'v2\api\StudentOrientationApiController@getOrientationCourse']);
    Route::match(['post'], 'get-header-data', ['as' => 'get-header-data',                         'uses' => 'v2\api\StudentOrientationApiController@getHeaderData']);

    // Student API
    Route::match(['post'], 'agent-students-data', ['as' => 'agent-students-data',                     'uses' => 'v2\api\StudentApiController@getAgentStudentData']);
    Route::match(['get', 'post'], 'export-agent-students', ['as' => 'export-agent-students',                   'uses' => 'v2\api\StudentApiController@exportAgentStudentData']);
    Route::match(['post'], 'agent-student-filter-html', ['as' => 'agent-student-filter-html',               'uses' => 'v2\api\StudentApiController@filterAgentStudents']);
    Route::match(['post'], 'student-data', ['as' => 'student-data',                            'uses' => 'v2\api\StudentApiController@getStudentData']);
    Route::match(['post'], 'student-invite-mail', ['as' => 'student-invite-mail',                     'uses' => 'v2\api\StudentApiController@studentInviteMailSend']);
    Route::match(['post'], 'student-data-scout', ['as' => 'student-data-scout',                      'uses' => 'v2\api\StudentApiController@getStudentDataScout']);
    Route::match(['post'], 'student-filter-menu-html', ['as' => 'student-filter-menu-html',                'uses' => 'v2\api\StudentApiController@getSidebarFilter']);
    Route::match(['post'], 'get-courses-list-for-filter', ['as' => 'get-courses-list-for-filter',             'uses' => 'v2\api\StudentApiController@getCoursesListForFilter']);
    Route::match(['post'], 'student-intake-add', ['as' => 'student-intake-add',                      'uses' => 'v2\api\StudentApiController@getIntakesByFilter']);
    Route::match(['post'], 'student-course-list', ['as' => 'student-course-list',                     'uses' => 'v2\api\StudentApiController@getStudentCourseList']);
    Route::match(['post'], 'get-data-for-bulk-email', ['as' => 'get-data-for-bulk-email',                 'uses' => 'v2\api\StudentApiController@getDataForBulkEmail']);
    Route::match(['post'], 'get-data-for-bulk-letter', ['as' => 'get-data-for-bulk-letter',                'uses' => 'v2\api\StudentApiController@getDataForBulkLetter']);
    Route::match(['post'], 'find-student-id-with-course', ['as' => 'find-student-id-with-course',             'uses' => 'v2\api\StudentApiController@findStudentIdWithCourse']);
    Route::match(['post'], 'student-full-name', ['as' => 'student-full-name',                       'uses' => 'v2\api\StudentApiController@getStudentFullName']);
    Route::match(['post'], 'student-issue-letter-generate', ['as' => 'student-issue-letter-generate',           'uses' => 'v2\api\StudentApiController@studentIssueLetterGenerate']);
    Route::match(['post'], 'verify-letter-watermark', ['as' => 'verify-letter-watermark',                 'uses' => 'v2\api\StudentApiController@verifyLetterWatermark']);
    Route::match(['post'], 'student-issue-letter-generate-with-watermark', ['as' => 'student-issue-letter-generate-with-watermark', 'uses' => 'v2\api\StudentApiController@studentIssueLetterGenerateWithWatermark']);
    Route::match(['post'], 'student-issue-letter-download', ['as' => 'student-issue-letter-download',           'uses' => 'v2\api\StudentApiController@studentIssueLetterDownload']);
    Route::match(['post'], 'student-issue-letter-zip', ['as' => 'student-issue-letter-zip',                'uses' => 'v2\api\StudentApiController@studentIssueLetterZip']);
    Route::match(['get'], 'download-file-and-delete', ['as' => 'download-file-and-delete',                'uses' => 'v2\api\StudentApiController@downloadFileAndDelete']);
    Route::match(['get', 'post'], 'student-list-data', ['as' => 'student-list-data',                       'uses' => 'v2\api\StudentApiController@getAllStudentListForDropdown']);
    Route::match(['get', 'post'], 'get-student-services-name', ['as' => 'get-student-services-name',               'uses' => 'v2\api\StudentApiController@getStudentServicesName']);

    // Communication API
    Route::match(['post'], 'communication-student-data', ['as' => 'communication-student-data',              'uses' => 'v2\api\CommunicationApiController@getStudentData']);
    Route::match(['post'], 'communication-staff-data', ['as' => 'communication-staff-data',                'uses' => 'v2\api\CommunicationApiController@getStaffData']);
    Route::match(['post'], 'communication-news-reminder-data', ['as' => 'communication-news-reminder-data',        'uses' => 'v2\api\CommunicationApiController@getNewsReminderData']);
    Route::match(['post'], 'staff-mail-history-data', ['as' => 'staff-mail-history-data',                 'uses' => 'v2\api\CommunicationApiController@getStaffMailHisory']);
    Route::match(['post'], 'student-mail-history-data', ['as' => 'student-mail-history-data',               'uses' => 'v2\api\CommunicationApiController@getStudentMailHisory']);
    Route::match(['post'], 'student-sms-history-data', ['as' => 'student-sms-history-data',                'uses' => 'v2\api\CommunicationApiController@getStudentSmsHisory']);
    Route::match(['post'], 'get-student-recent-batches', ['as' => 'get-student-recent-batches',              'uses' => 'v2\api\CommunicationApiController@studentrecentBatches']);
    Route::match(['post'], 'get-student-all-batches', ['as' => 'get-student-all-batches',                 'uses' => 'v2\api\CommunicationApiController@studentallBatches']);
    Route::match(['post'], 'get-staff-recent-roles', ['as' => 'get-staff-recent-roles',                  'uses' => 'v2\api\CommunicationApiController@staffrecentRoles']);
    Route::match(['post'], 'get-staff-all-roles', ['as' => 'get-staff-all-roles',                     'uses' => 'v2\api\CommunicationApiController@staffallRoles']);
    Route::match(['post'], 'get-staff-position', ['as' => 'get-staff-position',                      'uses' => 'v2\api\CommunicationApiController@staffPosition']);

    Route::match(['post'], 'communication-assign-news-reminder-data', ['as' => 'communication-assign-news-reminder-data', 'uses' => 'v2\api\CommunicationApiController@getAssignNewsReminderData']);
    Route::match(['post'], 'communication-campus-list', ['as' => 'communication-campus-list',               'uses' => 'v2\api\CommunicationApiController@getCampusList']);
    Route::match(['post'], 'communication-course-type-data', ['as' => 'communication-course-type-data',          'uses' => 'v2\api\CommunicationApiController@getCourseTypeList']); // same
    Route::match(['post'], 'communication-semester-data', ['as' => 'communication-semester-data',             'uses' => 'v2\api\CommunicationApiController@getSemesterList']);
    Route::match(['post'], 'communication-term-data', ['as' => 'communication-term-data',                 'uses' => 'v2\api\CommunicationApiController@getTermList']);  // same
    Route::match(['post'], 'communication-subject-data', ['as' => 'communication-subject-data',              'uses' => 'v2\api\CommunicationApiController@getSubjectList']); // same
    Route::match(['post'], 'communication-class-batch-data', ['as' => 'communication-class-batch-data',          'uses' => 'v2\api\CommunicationApiController@getClassBatchList']);
    Route::match(['post'], 'communication-course-type-data', ['as' => 'communication-course-type-data',          'uses' => 'v2\api\CommunicationApiController@getCourseTypeList']);
    Route::match(['post'], 'communication-course-data', ['as' => 'communication-course-data',               'uses' => 'v2\api\CommunicationApiController@getCourseList']); // same
    Route::match(['post'], 'get-course-status', ['as' => 'get-course-status',                       'uses' => 'v2\api\CommunicationApiController@getCourseStatusList']);
    Route::match(['post'], 'communication-role-data', ['as' => 'communication-role-data',                 'uses' => 'v2\api\CommunicationApiController@getRoleList']);
    Route::match(['post'], 'communication-user-data', ['as' => 'communication-user-data',                 'uses' => 'v2\api\CommunicationApiController@getUserList']);
    Route::match(['post'], 'communication-role-list', ['as' => 'communication-role-list',                 'uses' => 'v2\api\CommunicationApiController@getRoleListData']);

    Route::match(['post'], 'add-news-reminder', ['as' => 'add-news-reminder',                       'uses' => 'v2\api\CommunicationApiController@addNewsReminderData']);
    Route::match(['post'], 'edit-news-reminder', ['as' => 'edit-news-reminder',                      'uses' => 'v2\api\CommunicationApiController@editNewsReminderData']);
    Route::match(['post'], 'update-news-reminder', ['as' => 'update-news-reminder',                    'uses' => 'v2\api\CommunicationApiController@updateNewsReminderData']);
    Route::match(['post'], 'delete-news-reminder-data', ['as' => 'delete-news-reminder-data',               'uses' => 'v2\api\CommunicationApiController@deleteNewsReminderData']);

    Route::match(['post'], 'send-sms-communication', ['as' => 'send-sms-communication',                  'uses' => 'v2\api\CommunicationApiController@sendSMS']);
    Route::match(['post'], 'student-send-email-communication', ['as' => 'student-send-email-communication',        'uses' => 'v2\api\StudentProfileCommonApiController@studentSendEmailWithQueue']);
    Route::match(['post'], 'staff-send-email-communication', ['as' => 'staff-send-email-communication',          'uses' => 'v2\api\CommunicationApiController@staffCommunicationSendMail']);

    // Route::match(['post'],          'user-audit-log',                   ['as' => 'user-audit-log',                          'uses' => 'v2\api\StudentApiController@getUserAuditLog']);
    Route::match(['post'], 'get-user-audit-log-data', ['as' => 'get-user-audit-log-data',                 'uses' => 'v2\api\StudentApiController@getUserAuditLog']);

    // Student Profile API
    Route::match(['post'], 'student-profile-data', ['as' => 'student-profile-data',                    'uses' => 'v2\api\StudentApiController@studentProfileData']);
    Route::match(['post'], 'get-visa-status', ['as' => 'get-visa-status',                         'uses' => 'v2\api\StudentApiController@getVisaStatusData']);
    Route::match(['post'], 'update-student-details', ['as' => 'update-student-details',                  'uses' => 'v2\api\StudentApiController@updateStudentDetails']);
    Route::match(['post'], 'update-additional-students', ['as' => 'update-additional-students',              'uses' => 'v2\api\StudentApiController@updateAdditionalDetails']);
    Route::match(['post'], 'student-result-data', ['as' => 'student-result-data',                     'uses' => 'v2\api\StudentApiController@studentResultData']);    // TODO:: not used
    Route::match(['post'], 'student-result-data-update', ['as' => 'student-result-data-update',              'uses' => 'v2\api\StudentResultTabApiController@updateStudentResultData']);
    Route::match(['post'], 'student-result-data-delete', ['as' => 'student-result-data-delete',              'uses' => 'v2\api\StudentApiController@studentResultDataDelete']);
    Route::match(['post'], 'get-current-course-summary', ['as' => 'get-current-course-summary',              'uses' => 'v2\api\StudentApiController@getCurrentCourseSummary']);
    Route::match(['post'], 'get-stud-subject-enroll-history', ['as' => 'get-stud-subject-enroll-history',         'uses' => 'v2\api\StudentApiController@getStudentSubjectEnrolmentHistory']);
    Route::match(['post'], 'verify-student-usi-number', ['as' => 'verify-student-usi-number',               'uses' => 'v2\api\StudentApiController@verifyStudentUsiNumber']);

    // Student Profile Enroll Subject
    Route::match(['post'], 'verify-higher-ed-course-type', ['as' => 'verify-higher-ed-course-type',            'uses' => 'v2\api\StudentApiController@verifyCourseTypeHigherEd']);
    Route::match(['post'], 'get-subjects-for-enroll', ['as' => 'get-subjects-for-enroll',                 'uses' => 'v2\api\StudentApiController@getSubjectsForEnroll']);
    Route::match(['post'], 'get-sub-units-for-enroll', ['as' => 'get-sub-units-for-enroll',                'uses' => 'v2\api\StudentApiController@getSubjectUnitForEnroll']);

    Route::match(['post'], 'student-unit-data', ['as' => 'student-unit-data',                       'uses' => 'v2\api\StudentApiController@studentUnitData']);
    Route::match(['post'], 'student-timetable-data', ['as' => 'student-timetable-data',                  'uses' => 'v2\api\StudentApiController@studentTimetableData']);

    Route::match(['post'], 'remove-student-profile-pic', ['as' => 'remove-student-profile-pic',              'uses' => 'v2\api\StudentApiController@removeStudentProfilePic']);
    Route::match(['post'], 'get-student-document-data', ['as' => 'get-student-document-data',               'uses' => 'v2\api\StudentApiController@getStudentDocumentData']);
    Route::match(['post'], 'get-student-breadcrumb-data', ['as' => 'get-student-breadcrumb-data',               'uses' => 'v2\api\StudentApiController@getStudentBreadcrumb']);
    Route::match(['post'], 'add-student-document-data', ['as' => 'add-student-document-data',               'uses' => 'v2\api\StudentApiController@addStudentDocumentDirectory']);
    Route::match(['post'], 'update-student-document-data', ['as' => 'update-student-document-data',            'uses' => 'v2\api\StudentApiController@updateStudentDocumentDirectory']);
    Route::match(['post'], 'remove-student-document-data', ['as' => 'remove-student-document-data',            'uses' => 'v2\api\StudentApiController@removeStudentDocumentDirectory']);

    Route::match(['post'], 'get-student-course-dropdown', ['as' => 'get-student-course-dropdown',             'uses' => 'v2\api\StudentApiController@getStudentCourseDropdown']);
    Route::match(['post'], 'student-course-detail', ['as' => 'student-course-detail',                   'uses' => 'v2\api\StudentApiController@getStudentCourseDetail']);
    Route::match(['post'], 'get-student-payment-details', ['as' => 'get-student-payment-details',             'uses' => 'v2\api\StudentApiController@getStudentPaymentDetails']);
    Route::match(['post'], 'get-student-activity-log', ['as' => 'get-student-activity-log',                'uses' => 'v2\api\StudentApiController@getStudentActivityLog']);
    Route::match(['get', 'post'], 'generate-stud-enroll-subject-pdf', ['as' => 'generate-stud-enroll-subject-pdf',        'uses' => 'v2\api\StudentApiController@generateVatStudentEnrollSubjectPdf']);
    Route::match(['post'], 'get-collage-enrolment-fees', ['as' => 'get-collage-enrolment-fees',              'uses' => 'v2\api\StudentApiController@getCollageEnrolmentFees']);
    Route::match(['post'], 'get-course-templateList', ['as' => 'get-course-templateList',                 'uses' => 'v2\api\StudentApiController@getCourseTemplateList']);
    Route::match(['post'], 'get-result-calculation-method', ['as' => 'get-result-calculation-method',           'uses' => 'v2\api\StudentApiController@getResultCalculationMethod']);
    Route::match(['post'], 'check-certificate-is-download', ['as' => 'check-certificate-is-download',           'uses' => 'v2\api\StudentApiController@checkCertificateIsDownload']);
    Route::match(['post'], 'check-certificate-requirements', ['as' => 'check-certificate-requirements',           'uses' => 'v2\api\StudentApiController@checkCertificateRequirements']);
    Route::match(['post'], 'check-certificate-requirements-multiple-student', ['as' => 'check-certificate-requirements-multiple-student',           'uses' => 'v2\api\StudentApiController@checkMultipleStudentCertificateRequirements']);
    Route::match(['post'], 'check-course-unit-result', ['as' => 'check-course-unit-result',           'uses' => 'v2\api\StudentApiController@checkCourseUnitResult']);
    Route::match(['get', 'post'], 'generate-content-to-pdf', ['as' => 'generate-content-to-pdf',                 'uses' => 'v2\api\StudentApiController@generateContentToPdf']);
    Route::match(['get', 'post'], 'get-letter-parameter-list', ['as' => 'get-letter-parameter-list',               'uses' => 'v2\api\StudentApiController@getLetterParameterList']);
    Route::match(['get', 'post'], 'get-replace-parameter-content', ['as' => 'get-replace-parameter-content',           'uses' => 'v2\api\StudentApiController@getReplaceParameterContent']);

    Route::match(['post'], 'get-course-finish-date', ['as' => 'get-course-finish-date',                  'uses' => 'v2\api\StudentCourseApiController@getCourseFinishDate']);
    Route::match(['post'], 'get-coe-history-list', ['as' => 'get-coe-history-list',                    'uses' => 'v2\api\StudentCourseApiController@getCoeHistoryList']);
    Route::match(['post'], 'get-course-extension-history', ['as' => 'get-course-extension-history',               'uses' => 'v2\api\StudentCourseApiController@getCourseExtensionHistory']);
    Route::match(['post'], 'extend-course-due-date', ['as' => 'extend-course-due-date',                     'uses' => 'v2\api\StudentCourseApiController@extendCourseDueDate']);
    Route::match(['post'], 'calculate-course-finish-date', ['as' => 'calculate-course-finish-date',               'uses' => 'v2\api\StudentCourseApiController@calculateCourseFinishDate']);
    Route::match(['post'], 'save-training-plan-data', ['as' => 'save-training-plan-data',                 'uses' => 'v2\api\StudentCourseApiController@saveTrainingPlanData']);
    Route::match(['post'], 'update-training-plan-data', ['as' => 'update-training-plan-data',               'uses' => 'v2\api\StudentCourseApiController@updateTrainingPlanData']);
    Route::match(['post'], 'delete-training-plan-data', ['as' => 'delete-training-plan-data',               'uses' => 'v2\api\StudentCourseApiController@deleteTrainingPlanData']);
    Route::match(['post'], 'get-training-plan-data', ['as' => 'get-training-plan-data',                  'uses' => 'v2\api\StudentCourseApiController@getTrainingPlanData']);
    Route::match(['post'], 'get-student-training-plan-list', ['as' => 'get-student-training-plan-list',          'uses' => 'v2\api\StudentCourseApiController@getStudentTrainingPlanList']);
    Route::match(['post'], 'delete-offer-fee-schedule', ['as' => 'delete-offer-fee-schedule',               'uses' => 'v2\api\StudentCourseApiController@deleteOfferFeeSchedule']);

    Route::match(['post'], 'get-employer-name-list', ['as' => 'get-employer-name-list',                  'uses' => 'v2\api\StudentApiController@getEmployerNameList']);
    Route::match(['post'], 'get-contract-code', ['as' => 'get-contract-code',                       'uses' => 'v2\api\StudentApiController@getContractCode']);
    Route::match(['post'], 'get-student-venue-list', ['as' => 'get-student-venue-list',                  'uses' => 'v2\api\StudentApiController@getVenueList']);
    Route::match(['post'], 'get-course-site-list', ['as' => 'get-course-site-list',                    'uses' => 'v2\api\StudentApiController@getCourseSiteList']);

    // Student Profile Payment
    Route::match(['get', 'post'], 'get-import-for-offer-data', ['as' => 'get-import-for-offer-data',               'uses' => 'v2\api\StudentPaymentApiControllerV2@getImportDataForOfferManage']);
    Route::match(['post'], 'save-import-from-offer-data', ['as' => 'save-import-from-offer-data',             'uses' => 'v2\api\StudentPaymentApiControllerV2@importDataFromOfferManage']);

    Route::match(['post'], 'get-all-payment-data', ['as' => 'get-all-payment-data',                    'uses' => 'v2\api\StudentPaymentApiController@getAllPaymentsData']);
    Route::match(['post'], 'get-initial-payment-data', ['as' => 'get-initial-payment-data',                'uses' => 'v2\api\StudentPaymentApiController@getInitialPaymentData']);
    Route::match(['post'], 'get-payment-schedule-data', ['as' => 'get-payment-schedule-data',               'uses' => 'v2\api\StudentPaymentApiController@getPaymentScheduleData']);
    Route::match(['post'], 'get-payment-mode-list', ['as' => 'get-payment-mode-list',                   'uses' => 'v2\api\StudentPaymentApiController@getPaymentModeList']);
    Route::match(['post'], 'get-agent-commission-data', ['as' => 'get-agent-commission-data',               'uses' => 'v2\api\StudentPaymentApiController@getAgentCommissionData']);
    Route::match(['post'], 'get-agent-bonus-data', ['as' => 'get-agent-bonus-data',                    'uses' => 'v2\api\StudentPaymentApiController@getAgentBonusData']);
    Route::match(['post'], 'get-payment-refund-data', ['as' => 'get-payment-refund-data',                 'uses' => 'v2\api\StudentPaymentApiController@getPaymentRefundData']);
    Route::match(['post'], 'get-student-scholarship-data', ['as' => 'get-student-scholarship-data',            'uses' => 'v2\api\StudentPaymentApiController@getStudentScholarshipData']);
    Route::match(['post'], 'get-record-payment-details', ['as' => 'get-record-payment-details',              'uses' => 'v2\api\StudentPaymentApiController@getRecordPaymentDetails']);
    Route::match(['post'], 'save-record-payment-details', ['as' => 'save-record-payment-details',             'uses' => 'v2\api\StudentPaymentApiController@saveRecordPaymentDetails']);
    Route::match(['post'], 'get-miscellaneous-payment-data', ['as' => 'get-miscellaneous-payment-data',          'uses' => 'v2\api\StudentPaymentApiController@getMiscellaneousPaymentData']);
    Route::match(['post'], 'get-miscellaneous-payment-form-data', ['as' => 'get-miscellaneous-payment-form-data',   'uses' => 'v2\api\StudentPaymentApiController@getMiscellaneousPaymentFromData']);
    Route::match(['post'], 'send-custom-mail-with-attachment', ['as' => 'send-custom-mail-with-attachment',        'uses' => 'v2\api\StudentPaymentApiController@sendMiscellaneousTransactionEmail']);
    Route::match(['post'], 'get-student-agent-list', ['as' => 'get-student-agent-list',                  'uses' => 'v2\api\StudentPaymentApiController@getStudentAgentList']);
    Route::match(['post'], 'get-payment-transfer-data', ['as' => 'get-payment-transfer-data',               'uses' => 'v2\api\StudentPaymentApiController@getPaymentTransferList']);
    Route::match(['post'], 'get-selected-course-invoice', ['as' => 'get-selected-course-invoice',             'uses' => 'v2\api\StudentPaymentApiController@getSelectedCourseInvoice']);
    Route::match(['post'], 'get-selected-transaction-details', ['as' => 'get-selected-transaction-details',        'uses' => 'v2\api\StudentPaymentApiController@getSelectedTransactionDetails']);

    Route::match(['post'], 'get-payment-statement-data', ['as' => 'get-payment-statement-data',              'uses' => 'v2\api\StudentPaymentApiController@getPaymentStatementData']);
    Route::match(['post'], 'get-service-payment-data', ['as' => 'get-service-payment-data',                'uses' => 'v2\api\StudentPaymentApiController@getServicePaymentData']);
    Route::match(['post'], 'get-service-payment-form-data', ['as' => 'get-service-payment-form-data',           'uses' => 'v2\api\StudentPaymentApiController@getServicePaymentFormData']);
    Route::match(['post'], 'save-service-payment-data', ['as' => 'save-service-payment-data',               'uses' => 'v2\api\StudentPaymentApiController@addServicePaymentData']);
    Route::match(['post'], 'save-record-service-payment', ['as' => 'save-record-service-payment',             'uses' => 'v2\api\StudentPaymentApiController@saveRecordServicePayment']);
    Route::match(['post'], 'get-student-upfront-details', ['as' => 'get-student-upfront-details',             'uses' => 'v2\api\StudentPaymentApiController@getStudentUpfrontDetails']);
    Route::match(['post'], 'save-student-pay-upfront-details', ['as' => 'save-student-pay-upfront-details',        'uses' => 'v2\api\StudentPaymentApiController@saveStudentPayUpfrontDetails']);
    Route::match(['post'], 'get-paid-payment-data', ['as' => 'get-paid-payment-data',                   'uses' => 'v2\api\StudentPaymentApiController@getPaidPaymentData']);
    Route::match(['post'], 'add-agent-commission-info-details', ['as' => 'add-agent-commission-info-details',       'uses' => 'v2\api\StudentPaymentApiController@addAgentCommissionInfoDetails']);
    Route::match(['post'], 'get-agent-commission', ['as' => 'get-agent-commission',                    'uses' => 'v2\api\StudentPaymentApiController@getAgentCommission']);
    Route::match(['post'], 'modify-agent-commission-data', ['as' => 'modify-agent-commission-data',            'uses' => 'v2\api\StudentPaymentApiController@modifyAgentCommissionData']);
    Route::match(['post'], 'save-agent-commission', ['as' => 'save-agent-commission',                   'uses' => 'v2\api\StudentPaymentApiController@saveAgentCommission']);
    Route::match(['post'], 'approve-agent-commission', ['as' => 'approve-agent-commission',                'uses' => 'v2\api\StudentPaymentApiController@approveAgentCommission']);
    Route::match(['post'], 'save-student-scholarship-details', ['as' => 'save-student-scholarship-details',        'uses' => 'v2\api\StudentPaymentApiController@saveStudentScholarshipDetails']);
    Route::match(['post'], 'update-student-scholarship-detail', ['as' => 'update-student-scholarship-detail',       'uses' => 'v2\api\StudentPaymentApiController@updateStudentScholarshipDetails']);
    Route::match(['post'], 'save-invoice-credit-details', ['as' => 'save-invoice-credit-details',             'uses' => 'v2\api\StudentPaymentApiController@saveInvoiceCreditDetails']);
    Route::match(['post'], 'delete-student-scholarships', ['as' => 'delete-student-scholarships',             'uses' => 'v2\api\StudentPaymentApiController@deleteStudentScholarship']);
    Route::match(['post'], 'transfer-student-scholarship', ['as' => 'transfer-student-scholarship',            'uses' => 'v2\api\StudentPaymentApiController@saveTransferScholarshipData']);
    Route::match(['post'], 'delete-payment-schedule', ['as' => 'delete-payment-schedule',                 'uses' => 'v2\api\StudentPaymentApiController@deletePaymentSchedule']);
    Route::match(['post'], 'bulk-delete-payment-items', ['as' => 'bulk-delete-payment-items',               'uses' => 'v2\api\StudentPaymentApiController@bulkDeletePaymentItems']);

    if (config('features.xero')) {
        Route::match(['post'], 'check-sync-status', ['as' => 'check-sync-status',                       'uses' => 'v2\api\StudentPaymentApiController@checkSyncStatus']);
        Route::match(['post'], 'sync-bulk-payments-from-xero', ['as' => 'sync-bulk-payments-from-xero',            'uses' => 'v2\api\StudentPaymentApiController@syncBulkPaymentsFromXero']);
        Route::match(['post'], 'sync-payment-schedule', ['as' => 'sync-payment-schedule',                   'uses' => 'v2\api\StudentPaymentApiController@syncPaymentSchedule']);
        Route::match(['post'], 'sync-payment-schedule-from-xero', ['as' => 'sync-payment-schedule-from-xero',         'uses' => 'v2\api\StudentPaymentApiController@syncPaymentScheduleFromXero']);
        Route::match(['post'], 'resync-payment-transaction', ['as' => 'resync-payment-transaction',              'uses' => 'v2\api\StudentPaymentApiController@resyncPaymentTransaction']);
        Route::match(['post'], 'resync-all-payment-transaction', ['as' => 'resync-all-payment-transaction',          'uses' => 'v2\api\StudentPaymentApiController@resyncAllPaymentTransaction']);
        Route::match(['post'], 'get-sync-log-data', ['as' => 'get-sync-log-data',                       'uses' => 'v2\api\StudentPaymentApiController@getSyncLogData']);
        Route::match(['post'], 'get-tab-sync-log-data', ['as' => 'get-tab-sync-log-data',                   'uses' => 'v2\api\StudentPaymentApiController@getTabSyncLogData']);

        Route::match(['post'], 'get-outstanding-balance-from-xero', ['as' => 'get-outstanding-balance-from-xero',       'uses' => 'v2\api\StudentPaymentApiController@getOutStandingAmountFromXero']);
        Route::match(['post'], 'get-unallocated-credit-from-xero', ['as' => 'get-unallocated-credit-from-xero',        'uses' => 'v2\api\StudentPaymentApiController@getUnallocatedCreditAmountFromXero']);
        Route::match(['post'], 'sync-miscellaneous-payment', ['as' => 'sync-miscellaneous-payment',              'uses' => 'v2\api\StudentPaymentApiController@syncMiscellaneousPayment']);
        Route::match(['post'], 'sync-miscellaneous-payment-from-xero', ['as' => 'sync-miscellaneous-payment-from-xero',    'uses' => 'v2\api\StudentPaymentApiController@syncMiscellaneousPaymentFromXero']);
        Route::match(['post'], 'sync-service-payment', ['as' => 'sync-service-payment',                    'uses' => 'v2\api\StudentPaymentApiController@syncServicePayment']);
        Route::match(['post'], 'sync-service-payment-from-xero', ['as' => 'sync-service-payment-from-xero',          'uses' => 'v2\api\StudentPaymentApiController@syncServicePaymentFromXero']);
        Route::match(['post'], 'sync-agent-commission', ['as' => 'sync-agent-commission',                   'uses' => 'v2\api\StudentPaymentApiController@syncAgentCommission']);
        Route::match(['post'], 'sync-agent-commission-from-xero', ['as' => 'sync-agent-commission-from-xero',         'uses' => 'v2\api\StudentPaymentApiController@syncAgentCommissionFromXero']);
        Route::match(['post'], 'create-po-for-agent-commission', ['as' => 'create-po-for-agent-commission',          'uses' => 'v2\api\StudentPaymentApiController@createPOForAgentCommission']);

        Route::match(['post'], 'sync-student-scholarship-payment', ['as' => 'sync-student-scholarship-payment',        'uses' => 'v2\api\StudentPaymentApiController@syncStudentScholarshipPayment']);
        Route::match(['post'], 'sync-student-scholarship-payment-from-xero', ['as' => 'sync-student-scholarship-payment-from-xero', 'uses' => 'v2\api\StudentPaymentApiController@syncStudentScholarshipPaymentFromXero']);
    }

    if (config('features.moodle')) {
        Route::match(['post'], 'unit-sync-with-moodle', ['as' => 'unit-sync-with-moodle',                   'uses' => 'v2\api\StudentApiController@unitSyncWithMoodle']);
        Route::match(['post'], 'assessment-sync-with-moodle', ['as' => 'assessment-sync-with-moodle',             'uses' => 'v2\api\StudentApiController@assessmentSyncWithMoodle']);
        Route::match(['post'], 'enroll-student-subject-sync-with-moodle', ['as' => 'enroll-student-subject-sync-with-moodle', 'uses' => 'v2\api\StudentApiController@enrollStudentSubjectSyncWithMoodle']);
        Route::match(['post'], 'unit-grade-sync-from-moodle', ['as' => 'unit-grade-sync-from-moodle',             'uses' => 'v2\api\StudentApiController@unitGradeSyncFromMoodle']);
    }

    Route::match(['post'], 'get-xero-failed-payments-data', ['as' => 'get-xero-failed-payments-data',           'uses' => 'v2\api\StudentPaymentApiController@getXeroFailedPaymentsData']);
    Route::match(['post'], 'get-student-service-payment-edit', ['as' => 'get-student-service-payment-edit',        'uses' => 'v2\api\StudentPaymentApiController@getStudentServicePaymentEdit']);
    Route::match(['post'], 'get-student-service-payment-data', ['as' => 'get-student-service-payment-data',        'uses' => 'v2\api\StudentPaymentApiController@getStudentServicePaymentData']);
    Route::match(['post'], 'get-payment-transaction-details', ['as' => 'get-payment-transaction-details',         'uses' => 'v2\api\StudentPaymentApiController@getPaymentTransactionDetails']);
    Route::match(['post'], 'get-payment-transaction-data', ['as' => 'get-payment-transaction-data',            'uses' => 'v2\api\StudentPaymentApiController@getPaymentTransactionData']);
    Route::match(['post'], 'delete-payment-transaction', ['as' => 'delete-payment-transaction',              'uses' => 'v2\api\StudentPaymentApiController@deletePaymentTransaction']);
    Route::match(['post'], 'save-edit-payment-transaction', ['as' => 'save-edit-payment-transaction',           'uses' => 'v2\api\StudentPaymentApiController@saveEditPaymentTransaction']);
    Route::match(['post'], 'save-payment-refund-details', ['as' => 'save-payment-refund-details',             'uses' => 'v2\api\StudentPaymentApiController@savePaymentRefundDetails']);
    Route::match(['post'], 'save-payment-transaction-reverse', ['as' => 'save-payment-transaction-reverse',        'uses' => 'v2\api\StudentPaymentApiController@savePaymentTransactionReverse']);
    Route::match(['post'], 'delete-payment-refund-history', ['as' => 'delete-payment-refund-history',           'uses' => 'v2\api\StudentPaymentApiController@deletePaymentRefundHistory']);
    Route::match(['post'], 'revert-payment-history', ['as' => 'revert-payment-history',                  'uses' => 'v2\api\StudentPaymentApiController@revertBackPaymentHistory']);

    Route::match(['post'], 'save-refund-payment-additional-service-information', ['as' => 'save-refund-payment-additional-service-information', 'uses' => 'v2\api\StudentPaymentApiController@saveRefundPaymentAdditionalServiceInformation']);
    Route::match(['post'], 'delete-transaction-additional-payment-service', ['as' => 'delete-transaction-additional-payment-service',      'uses' => 'v2\api\StudentPaymentApiController@deleteTransactionAdditionalPaymentService']);

    // Onboard Setup
    Route::match(['get', 'post'], 'onboard-setup/ajaxAction', ['as' => 'ajaxAction',                              'uses' => 'v2\api\OnboardApiController@ajaxAction']);
    Route::match(['get', 'post'], 'get-states', ['as' => 'get-states',                              'uses' => 'v2\api\OnboardApiController@stateData']);
    Route::match(['get', 'post'], 'get-section', ['as' => 'get-section',                             'uses' => 'v2\api\OnboardApiController@getSection']);
    Route::match(['get', 'post'], 'get-country', ['as' => 'get-country',                             'uses' => 'v2\api\OnboardApiController@getCountry']);
    Route::match(['get', 'post'], 'get-training-location', ['as' => 'get-training-location',                   'uses' => 'v2\api\OnboardApiController@getTrainingLocation']);   // TODO:: not used
    Route::match(['get', 'post'], 'get-venue-location', ['as' => 'get-venue-location',                      'uses' => 'v2\api\OnboardApiController@getVenueLocation']);
    Route::match(['get', 'post'], 'get-campus-list', ['as' => 'get-campus-list',                         'uses' => 'v2\api\OnboardApiController@getCampusList']);
    Route::match(['get', 'post'], 'get-venue-list', ['as' => 'get-venue-list',                          'uses' => 'v2\api\OnboardApiController@getVenueList']);
    Route::match(['get', 'post'], 'get-section-type', ['as' => 'get-section-type',                        'uses' => 'v2\api\OnboardApiController@sectionType']);
    Route::match(['get', 'post'], 'get-section-course-type', ['as' => 'get-section-course-type',                 'uses' => 'v2\api\OnboardApiController@sectionCourseType']);
    Route::match(['get', 'post'], 'get-section-venue', ['as' => 'get-section-venue',                       'uses' => 'v2\api\OnboardApiController@sectionVenueList']);
    Route::match(['get', 'post'], 'get-section-data', ['as' => 'get-section-data',                        'uses' => 'v2\api\OnboardApiController@sectionData']);
    Route::match(['get', 'post'], 'offer-documents-data', ['as' => 'offer-documents-data',                    'uses' => 'v2\api\OnboardApiController@OfferLetterData']);
    Route::match(['get', 'post'], 'update-section-status', ['as' => 'update-section-status',                   'uses' => 'v2\api\OnboardApiController@updateIsDefault']);
    Route::match(['get', 'post'], 'agent-documents-data', ['as' => 'agent-documents-data',                    'uses' => 'v2\api\OnboardApiController@agentDocumentsData']);
    Route::match(['get', 'post'], 'offer-tracking-data', ['as' => 'offer-tracking-data',                     'uses' => 'v2\api\OnboardApiController@offerTrackingData']);
    Route::match(['get', 'post'], 'custom-checklist-data', ['as' => 'custom-checklist-data',                   'uses' => 'v2\api\OnboardApiController@customChecklistData']);
    Route::match(['get', 'post'], 'venue-data', ['as' => 'venue-data',                              'uses' => 'v2\api\OnboardApiController@venueData']);
    Route::match(['get', 'post'], 'room-data', ['as' => 'room-data',                               'uses' => 'v2\api\OnboardApiController@roomData']);
    Route::match(['get', 'post'], 'get-offer-document', ['as' => 'get-offer-document',                      'uses' => 'v2\api\OnboardApiController@getOfferDocument']);
    Route::match(['get', 'post'], 'delete-offer-documents-data', ['as' => 'delete-offer-documents-data',             'uses' => 'v2\api\OnboardApiController@deleteOfferLetter']);
    Route::match(['get', 'post'], 'get-agent-document', ['as' => 'get-agent-document',                      'uses' => 'v2\api\OnboardApiController@getAgentDocument']);
    Route::match(['get', 'post'], 'delete-agent-documents-data', ['as' => 'delete-agent-documents-data',             'uses' => 'v2\api\OnboardApiController@deleteAgentDocument']);
    Route::match(['get', 'post'], 'get-offer-tracking-status', ['as' => 'get-offer-tracking-status',               'uses' => 'v2\api\OnboardApiController@getOfferTracking']);
    Route::match(['get', 'post'], 'delete-offer-tracking', ['as' => 'delete-offer-tracking',                   'uses' => 'v2\api\OnboardApiController@deleteOfferTracking']);
    Route::match(['get', 'post'], 'get-custom-checklist', ['as' => 'get-custom-checklist',                    'uses' => 'v2\api\OnboardApiController@getCustomChecklist']);
    Route::match(['get', 'post'], 'delete-custom-checklist', ['as' => 'delete-custom-checklist',                 'uses' => 'v2\api\OnboardApiController@deleteCustomChecklist']);
    Route::match(['get', 'post'], 'get-college-details', ['as' => 'get-college-details',                     'uses' => 'v2\api\OnboardApiController@getCollegeDetail']);
    Route::match(['get', 'post'], 'upload-college-logo', ['as' => 'upload-college-logo',                     'uses' => 'v2\api\OnboardApiController@uploadCollegeLogo']);
    Route::match(['get', 'post'], 'upload-signature-logo', ['as' => 'upload-signature-logo',                   'uses' => 'v2\api\OnboardApiController@uploadSignatureLogo']);
    Route::match(['get', 'post'], 'upload-dean-signature', ['as' => 'upload-dean-signature',                   'uses' => 'v2\api\OnboardApiController@uploadDeanSignature']);
    Route::match(['get', 'post'], 'upload-admission-manager-signature', ['as' => 'upload-admission-manager-signature',                   'uses' => 'v2\api\OnboardApiController@uploadAdmissionManagerSignature']);
    Route::match(['get', 'post'], 'upload-student-support-signature', ['as' => 'upload-student-support-signature',                   'uses' => 'v2\api\OnboardApiController@uploadStudentSupportSignature']);
    Route::match(['get', 'post'], 'remove-signature-logo', ['as' => 'remove-signature-logo',                   'uses' => 'v2\api\OnboardApiController@removeSignatureLogo']);
    Route::match(['get', 'post'], 'remove-college-logo', ['as' => 'remove-college-logo',                     'uses' => 'v2\api\OnboardApiController@removeCollegeLogo']);
    Route::match(['get', 'post'], 'get-venue-details', ['as' => 'get-venue-details',                       'uses' => 'v2\api\OnboardApiController@getVenueDetail']);
    Route::match(['get', 'post'], 'get-room-details', ['as' => 'get-room-details',                        'uses' => 'v2\api\OnboardApiController@getRoomDetail']);
    Route::match(['get', 'post'], 'delete-venue-details', ['as' => 'delete-venue-details',                    'uses' => 'v2\api\OnboardApiController@deleteVenueDetails']);
    Route::match(['get', 'post'], 'delete-venue-address', ['as' => 'delete-venue-address',                    'uses' => 'v2\api\OnboardApiController@deleteVenueAddress']);
    Route::match(['get', 'post'], 'delete-room-details', ['as' => 'delete-room-details',                     'uses' => 'v2\api\OnboardApiController@deleteRoomDetails']);
    Route::match(['get', 'post'], 'update-room-status', ['as' => 'update-room-status',                      'uses' => 'v2\api\OnboardApiController@updateRoomStatus']);
    Route::match(['get', 'post'], 'update-venue-status', ['as' => 'update-venue-status',                     'uses' => 'v2\api\OnboardApiController@updateVenueStatus']);
    Route::match(['get', 'post'], 'view-campus-details', ['as' => 'view-campus-details',                     'uses' => 'v2\api\OnboardApiController@getCampusDeatils']);
    Route::match(['get', 'post'], 'get-campus-data', ['as' => 'get-campus-data',                         'uses' => 'v2\api\OnboardApiController@getCampusDataForEdit']);
    Route::match(['get', 'post'], 'update-campus-data', ['as' => 'update-campus-data',                      'uses' => 'v2\api\OnboardApiController@updateCampusData']);
    Route::match(['get', 'post'], 'delete-campus-details', ['as' => 'delete-campus-details',                   'uses' => 'v2\api\OnboardApiController@deleteCampusDetails']);
    Route::match(['get', 'post'], 'get-course-type', ['as' => 'get-course-type',                         'uses' => 'v2\api\OnboardApiController@getCourseType']);
    Route::match(['get', 'post'], 'update-course-type-status', ['as' => 'update-course-type-status',               'uses' => 'v2\api\OnboardApiController@updateCourseTypeStatus']);
    Route::match(['get', 'post'], 'get-country-data', ['as' => 'get-country-data',                        'uses' => 'v2\api\OnboardApiController@getCountryData']);
    Route::match(['get', 'post'], 'get-country-details', ['as' => 'get-country-details',                     'uses' => 'v2\api\OnboardApiController@getCountryDetails']);
    Route::match(['get', 'post'], 'gte-document-data', ['as' => 'gte-document-data',                       'uses' => 'v2\api\OnboardApiController@gteDocumentData']);

    Route::match(['get', 'post'], 'get-gte-document-details', ['as' => 'get-gte-document-details',                'uses' => 'v2\api\OnboardApiController@getGteDocumentDetails']);
    Route::match(['get', 'post'], 'get-added-services-fee-data', ['as' => 'get-added-services-fee-data',             'uses' => 'v2\api\OnboardApiController@gteAddedServicesFeeData']);

    Route::match(['get', 'post'], 'offer-label-data', ['as' => 'offer-label-data',                        'uses' => 'v2\api\OfferLabelApiController@offerLabelData']);
    Route::match(['get', 'post'], 'save-offer-label-data', ['as' => 'save-offer-label-data',                   'uses' => 'v2\api\OfferLabelApiController@saveOfferLabelData']);
    Route::match(['get', 'post'], 'get-offer-label-details', ['as' => 'get-offer-label-details',                 'uses' => 'v2\api\OfferLabelApiController@getOfferLabelDetails']);
    Route::match(['get', 'post'], 'delete-offer-label', ['as' => 'delete-offer-label',                      'uses' => 'v2\api\OfferLabelApiController@deleteOfferLabel']);

    Route::match(['get', 'post'], 'get-services-name', ['as' => 'get-services-name',                       'uses' => 'v2\api\OnboardApiController@getServicesName']);
    Route::match(['get', 'post'], 'get-services-fee-details', ['as' => 'get-services-fee-details',                'uses' => 'v2\api\OnboardApiController@getServicesFeeDetails']);
    Route::match(['get', 'post'], 'remove-services-fee', ['as' => 'remove-services-fee',                     'uses' => 'v2\api\OnboardApiController@removeServicesFee']);       // TODO:: note used
    Route::match(['get', 'post'], 'competency-result-grade-data', ['as' => 'competency-result-grade-data',            'uses' => 'v2\api\OnboardApiController@competencyGradeData']);
    Route::match(['get', 'post'], 'get-grading-type-name', ['as' => 'get-grading-type-name',                   'uses' => 'v2\api\OnboardApiController@getGradingTypeName']);
    Route::match(['get', 'post'], 'get-result-grade-details', ['as' => 'get-result-grade-details',                'uses' => 'v2\api\OnboardApiController@getResultGradeDetails']);
    Route::match(['get', 'post'], 'delete-competency-result-grade', ['as' => 'delete-competency-result-grade',          'uses' => 'v2\api\OnboardApiController@deleteResultGrade']);

    Route::match(['get', 'post'], 'get-oshc-info-data', ['as' => 'get-oshc-info-data',                      'uses' => 'v2\api\OnboardApiController@getOSHCInfoData']);
    Route::match(['get', 'post'], 'get-oshc-provider-name', ['as' => 'get-oshc-provider-name',                  'uses' => 'v2\api\OnboardApiController@getOSHCProviderName']);
    Route::match(['get', 'post'], 'get-oshc-duration', ['as' => 'get-oshc-duration',                       'uses' => 'v2\api\OnboardApiController@getOSHCDuration']);
    Route::match(['get', 'post'], 'update-oshc-status', ['as' => 'update-oshc-status',                      'uses' => 'v2\api\OnboardApiController@updateOSHCstatus']);
    Route::match(['get', 'post'], 'failed-jobs-data', ['as' => 'failed-jobs-data',                        'uses' => 'v2\api\OnboardApiController@failedJobsData']);
    Route::match(['get', 'post'], 'upload-latter-setting', ['as' => 'upload-latter-setting',                   'uses' => 'v2\api\OnboardApiController@uploadLatterSetting']);

    Route::match(['get', 'post'], 'get-language-data', ['as' => 'get-language-data',                       'uses' => 'v2\api\OnboardApiController@getLanguageData']);
    Route::match(['get', 'post'], 'get-language-details', ['as' => 'get-language-details',                    'uses' => 'v2\api\OnboardApiController@getLanguageDetails']);
    Route::match(['get', 'post'], 'get-agent-status-data', ['as' => 'get-agent-status-data',                   'uses' => 'v2\api\OnboardApiController@getAgentStatusData']);
    Route::match(['get', 'post'], 'get-agent-status-details', ['as' => 'get-agent-status-details',                'uses' => 'v2\api\OnboardApiController@getAgentStatusDetails']);

    Route::match(['get', 'post'], 'get-intervention-strategy-data', ['as' => 'get-intervention-strategy-data',          'uses' => 'v2\api\OnboardApiController@getInterventionStrategyData']);
    Route::match(['get', 'post'], 'get-intervention-type', ['as' => 'get-intervention-type',                   'uses' => 'v2\api\OnboardApiController@getInterventionType']);
    Route::match(['get', 'post'], 'get-certificate-id-format-data', ['as' => 'get-certificate-id-format-data',          'uses' => 'v2\api\OnboardApiController@getCertificateIdFormateData']);
    Route::match(['get', 'post'], 'get-certificate-id-format-details', ['as' => 'get-certificate-id-format-details',       'uses' => 'v2\api\OnboardApiController@getCertificateIdFormateDetails']);
    Route::match(['get', 'post'], 'get-assessment-due-date-details', ['as' => 'get-assessment-due-date-details',         'uses' => 'v2\api\OnboardApiController@getAssessmentDueDateDetails']);
    Route::match(['get', 'post'], 'get-invoice-setting-data', ['as' => 'get-invoice-setting-data',                'uses' => 'v2\api\OnboardApiController@getInvoiceSettingData']);
    Route::match(['get', 'post'], 'set-invoice-setting-data', ['as' => 'set-invoice-setting-data',                'uses' => 'v2\api\OnboardApiController@setInvoiceSettingData']);

    Route::match(['get', 'post'], 'get-schedule-type-data', ['as' => 'get-schedule-type-data',                'uses' => 'v2\api\OnboardApiController@getScheduleTypeData']);
    Route::match(['get', 'post'], 'set-schedule-type-data', ['as' => 'set-schedule-type-data',                'uses' => 'v2\api\OnboardApiController@setScheduleTypeData']);

    Route::match(['get', 'post'], 'get-notification-template-details', ['as' => 'get-notification-template-details',       'uses' => 'v2\api\OnboardApiController@getNotificationTemplateDetails']);
    Route::match(['get', 'post'], 'get-agent-template-name', ['as' => 'get-agent-template-name',                 'uses' => 'v2\api\OnboardApiController@getAgentTemplateName']);
    Route::match(['get', 'post'], 'get-letter-details', ['as' => 'get-letter-details',                      'uses' => 'v2\api\OnboardApiController@getLetterDetails']);
    Route::match(['get', 'post'], 'get-template-details', ['as' => 'get-template-details',                    'uses' => 'v2\api\OnboardApiController@getTemplateDetails']);

    Route::match(['get', 'post'], 'get-letter-default-parameter', ['as' => 'get-letter-default-parameter',            'uses' => 'v2\api\OnboardApiController@getLetterDefaultParameter']);
    Route::match(['get', 'post'], 'get-email-default-parameter', ['as' => 'get-email-default-parameter',             'uses' => 'v2\api\OnboardApiController@getEmailDefaultParameter']);
    Route::match(['get', 'post'], 'get-global-queue', ['as' => 'get-global-queue',                        'uses' => 'v2\api\OnboardApiController@getGlobalQueue']);
    Route::match(['get', 'post'], 'get-progress-global-queue', ['as' => 'get-progress-global-queue',               'uses' => 'v2\api\OnboardApiController@getProgressGlobalQueue']);
    Route::match(['get', 'post'], 'get-queue-details', ['as' => 'get-queue-details',                       'uses' => 'v2\api\OnboardApiController@getQueueDetails']);
    Route::match(['get', 'post'], 'delete-record-data', ['as' => 'delete-record-data',                       'uses' => 'v2\api\OnboardApiController@deleteRecordData']);

    // onboard setting
    Route::match(['get', 'post'], 'contract-code-data', ['as' => 'contract-code-data',                      'uses' => 'v2\api\OnboardSettingApiController@getContractCodeData']);
    Route::match(['get', 'post'], 'contract-funding-source-data', ['as' => 'contract-funding-source-data',            'uses' => 'v2\api\OnboardSettingApiController@getContractFundingSourceData']);
    Route::match(['get', 'post'], 'course-site-data', ['as' => 'course-site-data',                        'uses' => 'v2\api\OnboardSettingApiController@getCourseSiteData']);
    Route::match(['get', 'post'], 'elearning-link-data', ['as' => 'elearning-link-data',                     'uses' => 'v2\api\OnboardSettingApiController@getElearningLinkData']);
    Route::match(['get', 'post'], 'failed-email-data', ['as' => 'failed-email-data',                       'uses' => 'v2\api\OnboardSettingApiController@failedEmailData']);
    Route::match(['get', 'post'], 'failed-email-detail', ['as' => 'failed-email-detail',                     'uses' => 'v2\api\OnboardSettingApiController@failedEmailDetail']);
    Route::match(['get', 'post'], 'manage-reports-data', ['as' => 'manage-reports-data',                     'uses' => 'v2\api\OnboardSettingApiController@manageReportsData']);
    Route::match(['get', 'post'], 'bank-info-data', ['as' => 'bank-info-data',                          'uses' => 'v2\api\OnboardSettingApiController@bankInfoData']);
    Route::match(['get', 'post'], 'student-report-letter-data', ['as' => 'student-report-letter-data',              'uses' => 'v2\api\OnboardSettingApiController@studentReportLetterData']);
    Route::match(['get', 'post'], 'save-contract-code', ['as' => 'save-contract-code',                      'uses' => 'v2\api\OnboardSettingApiController@saveContractCode']);
    Route::match(['get', 'post'], 'get-contract-code-details', ['as' => 'get-contract-code-details',               'uses' => 'v2\api\OnboardSettingApiController@getContractCodeDetails']);
    Route::match(['get', 'post'], 'update-contract-code', ['as' => 'update-contract-code',                    'uses' => 'v2\api\OnboardSettingApiController@updateContractCode']);
    Route::match(['get', 'post'], 'save-contract-funding-source', ['as' => 'save-contract-funding-source',            'uses' => 'v2\api\OnboardSettingApiController@saveContractFundingSource']);
    Route::match(['get', 'post'], 'get-contract-funding-source-details', ['as' => 'get-contract-funding-source-details',    'uses' => 'v2\api\OnboardSettingApiController@getContractFundingSourceDetails']);
    Route::match(['get', 'post'], 'update-contract-funding-source', ['as' => 'update-contract-funding-source',          'uses' => 'v2\api\OnboardSettingApiController@updateContractFundingSource']);
    Route::match(['get', 'post'], 'save-course-site', ['as' => 'save-course-site',                        'uses' => 'v2\api\OnboardSettingApiController@saveCourseSite']);
    Route::match(['get', 'post'], 'get-course-site-details', ['as' => 'get-course-site-details',                 'uses' => 'v2\api\OnboardSettingApiController@getCourseSiteDetails']);
    Route::match(['get', 'post'], 'update-course-site', ['as' => 'update-course-site',                      'uses' => 'v2\api\OnboardSettingApiController@updateCourseSite']);

    Route::match(['get', 'post'], 'save-elearning-link', ['as' => 'save-elearning-link',                     'uses' => 'v2\api\OnboardSettingApiController@saveElearningLink']);
    Route::match(['get', 'post'], 'save-bank-info', ['as' => 'save-bank-info',                          'uses' => 'v2\api\OnboardSettingApiController@saveBankInfo']);
    Route::match(['get', 'post'], 'delete-contract-code', ['as' => 'delete-contract-code-api',                'uses' => 'v2\api\OnboardSettingApiController@deleteContractCode']);
    Route::match(['get', 'post'], 'delete-contract-funding-source', ['as' => 'delete-contract-funding-source',          'uses' => 'v2\api\OnboardSettingApiController@deleteContractFundingSource']);
    Route::match(['get', 'post'], 'delete-course-site', ['as' => 'delete-course-site-api',                  'uses' => 'v2\api\OnboardSettingApiController@deleteCourseSite']);
    Route::match(['get', 'post'], 'delete-elearning-link', ['as' => 'delete-elearning-link-api',               'uses' => 'v2\api\OnboardSettingApiController@deleteElearningLink']);
    Route::match(['get', 'post'], 'delete-bank-info', ['as' => 'delete-bank-info',                        'uses' => 'v2\api\OnboardSettingApiController@deleteBankInfo']);
    Route::match(['get', 'post'], 'get-bank-info', ['as' => 'get-bank-info',                           'uses' => 'v2\api\OnboardSettingApiController@getBankInfo']);
    Route::match(['get', 'post'], 'get-report-name', ['as' => 'get-report-name',                         'uses' => 'v2\api\OnboardSettingApiController@getReportName']);
    Route::match(['get', 'post'], 'get-course-by-type', ['as' => 'get-course-by-type',                      'uses' => 'v2\api\OnboardSettingApiController@getCourseByType']);
    Route::match(['get', 'post'], 'get-course-year', ['as' => 'get-course-year',                         'uses' => 'v2\api\OnboardSettingApiController@getCourseYear']);
    Route::match(['get', 'post'], 'get-course-intake-date', ['as' => 'get-course-intake-date',                  'uses' => 'v2\api\OnboardSettingApiController@getCourseIntakeDate']);
    Route::match(['get', 'post'], 'get-course-semester', ['as' => 'get-course-semester',                     'uses' => 'v2\api\OnboardSettingApiController@getCourseSemester']);
    Route::match(['get', 'post'], 'get-course-term', ['as' => 'get-course-term',                         'uses' => 'v2\api\OnboardSettingApiController@getCourseTerm']);
    Route::match(['get', 'post'], 'get-course-subject', ['as' => 'get-course-subject',                      'uses' => 'v2\api\OnboardSettingApiController@getCourseSubject']);
    Route::match(['get', 'post'], 'get-course-batch', ['as' => 'get-course-batch',                        'uses' => 'v2\api\OnboardSettingApiController@getCourseBatch']);
    Route::match(['get', 'post'], 'get-course-class', ['as' => 'get-course-class',                        'uses' => 'v2\api\OnboardSettingApiController@getCourseClass']);
    Route::match(['get', 'post'], 'get-report-letter-list', ['as' => 'get-report-letter-list',                  'uses' => 'v2\api\OnboardSettingApiController@getReportLetterList']);
    Route::match(['get', 'post'], 'get-report-letter-details', ['as' => 'get-report-letter-details',               'uses' => 'v2\api\OnboardSettingApiController@getReportLetterDetails']);
    Route::match(['get', 'post'], 'save-report-letter', ['as' => 'save-report-letter',                      'uses' => 'v2\api\OnboardSettingApiController@saveReportLetter']);
    Route::match(['get', 'post'], 'manage-reports/ajaxAction', ['as' => 'manage-reports-ajaxAction-api',           'uses' => 'v2\api\OnboardSettingApiController@ajaxAction']);
    Route::match(['get', 'post'], 'public-holiday-data', ['as' => 'public-holiday-data',                     'uses' => 'v2\api\OnboardSettingApiController@publicHolidayData']);
    Route::match(['get', 'post'], 'save-holiday-details', ['as' => 'save-holiday-details',                    'uses' => 'v2\api\OnboardSettingApiController@saveHolidayDetails']);
    Route::match(['get', 'post'], 'delete-holiday', ['as' => 'delete-holiday-api',                      'uses' => 'v2\api\OnboardSettingApiController@deleteHoliday']);
    Route::match(['get', 'post'], 'course-template-data', ['as' => 'course-template-data',                    'uses' => 'v2\api\OnboardSettingApiController@courseTemplateData']);
    Route::match(['get', 'post'], 'save-course-template', ['as' => 'save-course-template',                    'uses' => 'v2\api\OnboardSettingApiController@saveCourseTemplate']);
    Route::match(['get', 'post'], 'get-sms-template-data', ['as' => 'get-sms-template-data',                   'uses' => 'v2\api\OnboardSettingApiController@getSmsTemplateData']);
    Route::match(['get', 'post'], 'update-report-data', ['as' => 'update-report-data',                      'uses' => 'v2\api\OnboardSettingApiController@updateReportData']);
    Route::match(['get', 'post'], 'save-sms-template', ['as' => 'save-sms-template',                       'uses' => 'v2\api\OnboardSettingApiController@saveSmsTemplate']);
    Route::match(['get', 'post'], 'get-email-template-data', ['as' => 'get-email-template-data',                 'uses' => 'v2\api\OnboardSettingApiController@getEmailTemplateData']);
    Route::match(['get', 'post'], 'get-email-title-template-data', ['as' => 'get-email-title-template-data',           'uses' => 'v2\api\OnboardSettingApiController@getEmailTemplateTitleData']);
    Route::match(['get', 'post'], 'delete-course-template', ['as' => 'delete-course-template-api',              'uses' => 'v2\api\OnboardSettingApiController@deleteCourseTemplate']);
    Route::match(['get', 'post'], 'get-letter-template-data', ['as' => 'get-letter-template-data',                'uses' => 'v2\api\OnboardSettingApiController@getLetterTemplateData']);

    Route::match(['get', 'post'], 'uploaded-email-template-documents', ['as' => 'uploaded-email-template-documents',       'uses' => 'v2\api\OnboardSettingApiController@uploadedEmailTemplateDocumentsData']);
    Route::match(['get', 'post'], 'upload-email-template-document', ['as' => 'upload-email-template-document',          'uses' => 'v2\api\OnboardSettingApiController@uploadEmailTemplateDocument']);
    Route::match(['get', 'post'], 'delete-email-template-document', ['as' => 'delete-email-template-document',          'uses' => 'v2\api\OnboardSettingApiController@deleteEmailTemplateDocument']);
    Route::match(['get', 'post'], 'save-email-template', ['as' => 'save-email-template',                     'uses' => 'v2\api\OnboardSettingApiController@saveEmailTemplate']);
    Route::match(['get', 'post'], 'save-letter-template', ['as' => 'save-letter-template',                    'uses' => 'v2\api\OnboardSettingApiController@saveLetterTemplate']);
    Route::match(['get', 'post'], 'uploaded-letter-template-documents', ['as' => 'uploaded-letter-template-documents',      'uses' => 'v2\api\OnboardSettingApiController@uploadedLetterTemplateDocumentsData']);
    Route::match(['get', 'post'], 'delete-letter-template-document', ['as' => 'delete-letter-template-document',         'uses' => 'v2\api\OnboardSettingApiController@deleteLetterTemplateDocument']);
    Route::match(['get', 'post'], 'upload-letter-template-document', ['as' => 'upload-letter-template-document',         'uses' => 'v2\api\OnboardSettingApiController@uploadLetterTemplateDocument']);
    Route::match(['get', 'post'], 'upload-user-profile-picture', ['as' => 'upload-user-profile-picture',             'uses' => 'v2\api\OnboardSettingApiController@uploadUserProfilePicture']);
    Route::match(['get', 'post'], 'update-profile-data', ['as' => 'update-profile-data',                     'uses' => 'v2\api\OnboardSettingApiController@updateProfileData']);
    Route::match(['get', 'post'], 'update-user-password', ['as' => 'update-user-password',                    'uses' => 'v2\api\OnboardSettingApiController@updateUserPassword']);

    Route::match(['get', 'post'], 'get-courses-upfront-fee-list', ['as' => 'get-courses-upfront-fee-list',            'uses' => 'v2\api\OnboardSettingApiController@getCoursesUpfrontFeeList']);
    Route::match(['get', 'post'], 'get-courses-list', ['as' => 'get-courses-list',                        'uses' => 'v2\api\OnboardSettingApiController@getFullCoursesList']);
    Route::match(['get', 'post'], 'get-course-fee-detail', ['as' => 'get-course-fee-detail',                   'uses' => 'v2\api\OnboardSettingApiController@getCourseFee']);
    Route::match(['get', 'post'], 'save-course-upfront-fee', ['as' => 'save-course-upfront-fee',                 'uses' => 'v2\api\OnboardSettingApiController@saveCourseUpfrontFee']);
    Route::match(['get', 'post'], 'get-upFront-fee-detail-for-edit', ['as' => 'get-upFront-fee-detail-for-edit',         'uses' => 'v2\api\OnboardSettingApiController@getUpFrontFeeDetailForEdit']);
    Route::match(['get', 'post'], 'edit-course-upfront-fee', ['as' => 'edit-course-upfront-fee',                 'uses' => 'v2\api\OnboardSettingApiController@editCourseUpfrontFee']);
    Route::match(['get', 'post'], 'delete-upfront-fee-data', ['as' => 'delete-upfront-fee-data',                 'uses' => 'v2\api\OnboardSettingApiController@deleteUpfrontFee']);

    Route::match(['get', 'post'], 'get-elicos-discount-week-list', ['as' => 'get-elicos-discount-week-list',           'uses' => 'v2\api\OnboardSettingApiController@getElicosDiscountWeekList']);
    Route::match(['get', 'post'], 'save-elicos-discount-week', ['as' => 'save-elicos-discount-week',               'uses' => 'v2\api\OnboardSettingApiController@saveElicosDiscountWeek']);
    Route::match(['get', 'post'], 'get-elicos-discount-week', ['as' => 'get-elicos-discount-week',                'uses' => 'v2\api\OnboardSettingApiController@getElicosDiscountWeek']);
    Route::match(['get', 'post'], 'edit-elicos-discount-week', ['as' => 'edit-elicos-discount-week',               'uses' => 'v2\api\OnboardSettingApiController@editElicosDiscountWeek']);
    Route::match(['get', 'post'], 'delete-elicos-discount-week', ['as' => 'delete-elicos-discount-week',             'uses' => 'v2\api\OnboardSettingApiController@deleteElicosDiscountWeek']);

    Route::match(['get', 'post'], 'get-course-promotion-price-list', ['as' => 'get-course-promotion-price-list',         'uses' => 'v2\api\OnboardSettingApiController@getCoursePromotionPriceList']);
    Route::match(['get', 'post'], 'save-course-promotion-price', ['as' => 'save-course-promotion-price',             'uses' => 'v2\api\OnboardSettingApiController@saveCoursePromotionPrice']);
    Route::match(['get', 'post'], 'get-course-promotion-price', ['as' => 'get-course-promotion-price',              'uses' => 'v2\api\OnboardSettingApiController@getCoursePromotionPrice']);
    Route::match(['get', 'post'], 'edit-course-promotion-price', ['as' => 'edit-course-promotion-price',             'uses' => 'v2\api\OnboardSettingApiController@editCoursePromotionPrice']);
    Route::match(['get', 'post'], 'delete-course-promotion-price', ['as' => 'delete-course-promotion-price',           'uses' => 'v2\api\OnboardSettingApiController@deleteCoursePromotionPrice']);

    Route::match(['get', 'post'], 'get-campus-name-list', ['as' => 'get-campus-name-list',                    'uses' => 'v2\api\OnboardSettingApiController@getCampusNameList']);
    Route::match(['get', 'post'], 'get-course-type-list', ['as' => 'get-course-type-list',                    'uses' => 'v2\api\OnboardSettingApiController@getCourseTypeList']);
    Route::match(['get', 'post'], 'get-course-list-by-type', ['as' => 'get-course-list-by-type',                 'uses' => 'v2\api\OnboardSettingApiController@getCourseListByType']);
    Route::match(['get', 'post'], 'get-course-intake-year', ['as' => 'get-course-intake-year',                  'uses' => 'v2\api\OnboardSettingApiController@getCourseIntakeYear']);
    Route::match(['get', 'post'], 'get-all-date-from-year', ['as' => 'get-all-date-from-year',                  'uses' => 'v2\api\OnboardSettingApiController@getAllDateFromYear']);
    Route::match(['get', 'post'], 'save-courses-intake-date-add', ['as' => 'save-courses-intake-date-add',            'uses' => 'v2\api\OnboardSettingApiController@saveCoursesIntakeDateAdd']);
    Route::match(['get', 'post'], 'get-courses-intake-date-list', ['as' => 'get-courses-intake-date-list',            'uses' => 'v2\api\OnboardSettingApiController@getCoursesIntakeList']);
    Route::match(['get', 'post'], 'delete-course-intake-date', ['as' => 'delete-course-intake-date',               'uses' => 'v2\api\OnboardSettingApiController@deleteCoursesInkaeDate']);

    Route::match(['get', 'post'], 'get-teams-conditions', ['as' => 'get-teams-conditions',                      'uses' => 'v2\api\OnboardApiController@getTeamsConditions']);
    Route::match(['get', 'post'], 'save-teams-conditions', ['as' => 'save-teams-conditions',                      'uses' => 'v2\api\OnboardApiController@saveTeamsConditions']);
    Route::match(['get', 'post'], 'get-privacy-policy', ['as' => 'get-privacy-policy',                      'uses' => 'v2\api\OnboardApiController@getPrivacyPolicy']);
    Route::match(['get', 'post'], 'save-privacy-policy', ['as' => 'save-privacy-policy',                      'uses' => 'v2\api\OnboardApiController@savePrivacyPolicy']);

    // Student GTE Process
    Route::match(['post'], 'offer-manage-data', ['as' => 'offer-manage-data',                        'uses' => 'v2\api\StudentGteProcessApiController@offerManageData']);
    Route::match(['post'], 'offer-manage-filter-html', ['as' => 'offer-manage-filter-html',                 'uses' => 'v2\api\StudentGteProcessApiController@filterOfferManage']);
    Route::match(['post'], 'uploaded-offer-letter-data', ['as' => 'uploaded-offer-letter-data',               'uses' => 'v2\api\StudentGteProcessApiController@uploadedOfferLetterData']);
    Route::match(['post'], 'previous-offer-letter-data', ['as' => 'previous-offer-letter-data',               'uses' => 'v2\api\StudentGteProcessApiController@previousOfferLetterData']);
    Route::match(['post'], 'uploaded-gte-documents-data', ['as' => 'uploaded-gte-documents-data',              'uses' => 'v2\api\StudentGteProcessApiController@uploadedGteDocumentsData']);
    Route::match(['post'], 'previous-gte-documents-data', ['as' => 'previous-gte-documents-data',              'uses' => 'v2\api\StudentGteProcessApiController@previousGteDocumentsData']);
    Route::match(['post'], 'uploaded-gte-payment-data', ['as' => 'uploaded-gte-payment-data',                'uses' => 'v2\api\StudentGteProcessApiController@uploadedPaymentData']);
    Route::match(['post'], 'previous-gte-payment-data', ['as' => 'previous-gte-payment-data',                'uses' => 'v2\api\StudentGteProcessApiController@previousPaymentData']);
    Route::match(['post'], 'uploaded-gte-enrollment-data', ['as' => 'uploaded-gte-enrollment-data',             'uses' => 'v2\api\StudentGteProcessApiController@uploadedConfirmationEnrollData']);
    Route::match(['post'], 'previous-gte-enrollment-data', ['as' => 'previous-gte-enrollment-data',             'uses' => 'v2\api\StudentGteProcessApiController@previousConfirmationEnrollData']);
    Route::match(['post'], 'uploaded-gte-visa-data', ['as' => 'uploaded-gte-visa-data',                   'uses' => 'v2\api\StudentGteProcessApiController@uploadedVisaData']);
    Route::match(['post'], 'previous-gte-visa-data', ['as' => 'previous-gte-visa-data',                   'uses' => 'v2\api\StudentGteProcessApiController@previousVisaData']);
    Route::match(['post'], 'approve-offer-letter', ['as' => 'approve-offer-letter',                     'uses' => 'v2\api\StudentGteProcessApiController@approveOfferLetter']);
    Route::match(['post'], 'approve-gte-document', ['as' => 'approve-gte-document',                     'uses' => 'v2\api\StudentGteProcessApiController@approveGteDocument']);
    Route::match(['post'], 'approve-gte-payment', ['as' => 'approve-gte-payment',                      'uses' => 'v2\api\StudentGteProcessApiController@approveGtePayment']);
    Route::match(['post'], 'approve-gte-enrollment', ['as' => 'approve-gte-enrollment',                   'uses' => 'v2\api\StudentGteProcessApiController@approveGteEnrollment']);
    Route::match(['post'], 'approve-gte-visa', ['as' => 'approve-gte-visa',                         'uses' => 'v2\api\StudentGteProcessApiController@approveGteVisa']);
    Route::match(['post'], 'reject-offer-letter', ['as' => 'reject-offer-letter',                      'uses' => 'v2\api\StudentGteProcessApiController@rejectOfferLetter']);
    Route::match(['post'], 'reject-gte-document', ['as' => 'reject-gte-document',                      'uses' => 'v2\api\StudentGteProcessApiController@rejectGteDocument']);
    Route::match(['post'], 'reject-gte-payment', ['as' => 'reject-gte-payment',                       'uses' => 'v2\api\StudentGteProcessApiController@rejectGtePayment']);
    Route::match(['post'], 'reject-gte-enrollment', ['as' => 'reject-gte-enrollment',                    'uses' => 'v2\api\StudentGteProcessApiController@rejectGteEnrollment']);
    Route::match(['post'], 'reject-gte-visa', ['as' => 'reject-gte-visa',                          'uses' => 'v2\api\StudentGteProcessApiController@rejectGteVisa']);
    Route::match(['post'], 'upload-signed-offer-letter', ['as' => 'upload-signed-offer-letter',               'uses' => 'v2\api\StudentGteProcessApiController@uploadSignedOfferLetter']);
    Route::match(['post'], 'upload-gte-document', ['as' => 'upload-gte-document',                      'uses' => 'v2\api\StudentGteProcessApiController@uploadGtedocument']);
    Route::match(['post'], 'upload-payment-document', ['as' => 'upload-payment-document',                  'uses' => 'v2\api\StudentGteProcessApiController@uploadPaymentDocument']);
    Route::match(['post'], 'upload-confirmation-enrollment', ['as' => 'upload-confirmation-enrollment',           'uses' => 'v2\api\StudentGteProcessApiController@uploadConfirmationEnrollment']);
    Route::match(['post'], 'upload-visa-document', ['as' => 'upload-visa-document',                     'uses' => 'v2\api\StudentGteProcessApiController@uploadVisaDocument']);
    Route::match(['post'], 'get-gte-documents-list', ['as' => 'get-gte-documents-list',                   'uses' => 'v2\api\StudentGteProcessApiController@getGtedocumentList']);
    Route::match(['post'], 'request-gte-document-mail', ['as' => 'request-gte-document-mail',                'uses' => 'v2\api\StudentGteProcessApiController@requestGteDocumentMail']);
    Route::match(['post'], 'mark-stage-as-complete', ['as' => 'mark-stage-as-complete',                   'uses' => 'v2\api\StudentGteProcessApiController@markStageAsComplete']);
    Route::match(['post'], 'save-gte-comments', ['as' => 'save-gte-comments',                        'uses' => 'v2\api\StudentGteProcessApiController@saveGteComments']);
    Route::match(['post'], 'get-section-wise-comments', ['as' => 'get-section-wise-comments',                'uses' => 'v2\api\StudentGteProcessApiController@getSectionWiseComments']);
    Route::match(['post'], 'visa-stepper-data', ['as' => 'visa-stepper-data',                        'uses' => 'v2\api\StudentGteProcessApiController@getVisaStepperData']);
    Route::match(['post'], 'remove-gte-document', ['as' => 'remove-gte-document',                      'uses' => 'v2\api\StudentGteProcessApiController@removeGteDocument']);
    Route::match(['post'], 'reset-gte-document', ['as' => 'reset-gte-document',                       'uses' => 'v2\api\StudentGteProcessApiController@resetGteDocument']);
    Route::match(['post'], 'agent-agency-name', ['as' => 'agent-agency-name',                        'uses' => 'v2\api\StudentGteProcessApiController@getAgentAgencyName']);
    // Route::match(['post'],          'upload-signed-offer-letter',       ['as' => 'upload-signed-offer-letter',               'uses' => 'v2\api\StudentGteProcessApiController@uploadSignedOfferLetter']);

    // VPMS
    Route::match(['post'], 'placement-provider-data', ['as' => 'placement-provider-data',                 'uses' => 'v2\api\VpmsPlacementProviderApiController@getPlacementProviderData']);
    Route::match(['post'], 'save-placement-provider', ['as' => 'save-placement-provider',                 'uses' => 'v2\api\VpmsPlacementProviderApiController@savePlacementProviderData']);
    Route::match(['post'], 'placement-provider/ajaxAction', ['as' => 'placement-provider-ajaxAction',           'uses' => 'v2\api\VpmsPlacementProviderApiController@ajaxAction']);
    Route::match(['post'], 'get-placement-provider', ['as' => 'get-placement-provider',                  'uses' => 'v2\api\VpmsPlacementProviderApiController@getPlacementProvider']);
    Route::match(['post'], 'delete-placement-provider', ['as' => 'delete-placement-provider',               'uses' => 'v2\api\VpmsPlacementProviderApiController@deletePlacementProvider']);
    Route::match(['post'], 'get-vpms-provider-name', ['as' => 'get-vpms-provider-name',                  'uses' => 'v2\api\VpmsPlacementProviderApiController@getVpmsProviderName']);
    Route::match(['post'], 'student-profile-vpms-data', ['as' => 'student-profile-vpms-data',               'uses' => 'v2\api\VpmsPlacementProviderApiController@getStudentsVpmsData']);
    Route::match(['post'], 'students-placement-data', ['as' => 'students-placement-data',                 'uses' => 'v2\api\VpmsPlacementProviderApiController@studentPlacementData']);
    Route::match(['post'], 'student-placement-filter-html', ['as' => 'student-placement-filter-html',           'uses' => 'v2\api\VpmsPlacementProviderApiController@filterStudentsPlacements']);
    Route::match(['post'], 'dynamic-vpms-dates', ['as' => 'dynamic-vpms-dates',                      'uses' => 'v2\api\VpmsPlacementProviderApiController@dynamicVpmsDates']);
    Route::match(['post'], 'provider-students-data', ['as' => 'provider-students-data',                  'uses' => 'v2\api\VpmsPlacementProviderApiController@providerStudentsData']);
    Route::match(['post'], 'get-student-placement-details', ['as' => 'get-student-placement-details',           'uses' => 'v2\api\VpmsPlacementProviderApiController@getStudentPlacementDetails']);
    Route::match(['post'], 'set-provider-code', ['as' => 'set-provider-code',                       'uses' => 'v2\api\VpmsPlacementProviderApiController@setProviderCode']);

    // Enrollment Fees
    Route::match(['post'], 'enrollment-fees-data', ['as' => 'enrollment-fees-data',                    'uses' => 'v2\api\CollegeEnrollmentFeesApiController@getEnrollmentFeesData']);
    Route::match(['post'], 'enrollment-fees/ajaxAction', ['as' => 'enrollment-fees-ajaxAction',              'uses' => 'v2\api\CollegeEnrollmentFeesApiController@ajaxAction']);
    Route::match(['post'], 'get-enrollment-fees-details', ['as' => 'get-enrollment-fees-details',             'uses' => 'v2\api\CollegeEnrollmentFeesApiController@getEnrollmentFeesDetails']);
    Route::match(['post'], 'remove-enrollment-fees', ['as' => 'remove-enrollment-fees',                  'uses' => 'v2\api\CollegeEnrollmentFeesApiController@removeEnrollmentFees']);
    Route::match(['post'], 'get-course-name', ['as' => 'get-course-name',                         'uses' => 'v2\api\CollegeEnrollmentFeesApiController@getCourseName']);
    Route::match(['post'], 'get-course-type-name', ['as' => 'get-course-type-name',                    'uses' => 'v2\api\OnboardApiController@getCourseTypeName']);

    Route::match(['post'], 'get-student-id-formate-data', ['as' => 'get-student-id-formate-data',             'uses' => 'v2\api\OnboardApiController@getStudentIdFormateData']);
    Route::match(['post'], 'save-student-id-formate', ['as' => 'save-student-id-formate',                 'uses' => 'v2\api\OnboardApiController@saveStudentIdFormate']);

    // Global Search
    Route::match(['get', 'post'], 'global-search', ['as' => 'global-search',                           'uses' => 'v2\api\GlobalSearchController@globalSearch']);
    Route::match(['get', 'post'], 'add-recent-search', ['as' => 'add-recent-search',                       'uses' => 'v2\api\GlobalSearchController@addRecentSearch']);
    Route::match(['get', 'post'], 'get-recent-search', ['as' => 'get-recent-search',                       'uses' => 'v2\api\GlobalSearchController@getRecentSearch']);
    Route::match(['get', 'post'], 'search', ['as' => 'search',                                  'uses' => 'v2\api\GlobalSearchController@search']);

    // Timetable
    Route::match(['post'], 'timetable-attendance-data', ['as' => 'timetable-attendance-data',               'uses' => 'v2\api\TimeTableApiController@attendanceData']);
    Route::match(['post'], 'get-time-table-dashboard', ['as' => 'get-time-table-dashboard',                'uses' => 'v2\api\TimeTableApiController@getTimetableDashboard']);
    Route::match(['post'], 'timetable-teacher-filter', ['as' => 'timetable-teacher-filter',                'uses' => 'v2\api\TimeTableApiController@getTeacherFilter']);
    Route::match(['post'], 'get-calender-type', ['as' => 'get-calender-type',                       'uses' => 'v2\api\TimeTableApiController@getCalenderType']);
    Route::match(['post'], 'get-year-from-calender-type', ['as' => 'get-year-from-calender-type',             'uses' => 'v2\api\TimeTableApiController@getYearFromCalenderType']);
    Route::match(['post'], 'get-subject-list', ['as' => 'get-subject-list',                        'uses' => 'v2\api\TimeTableApiController@getSubjectList']);
    Route::match(['post'], 'view-all-timetable-data', ['as' => 'view-all-timetable-data',                 'uses' => 'v2\api\TimeTableApiController@viewAllTimetableData']);
    Route::match(['post'], 'timetable-default-data', ['as' => 'timetable-default-data',                  'uses' => 'v2\api\TimeTableApiController@timetableDefaultData']);
    Route::match(['post'], 'timetable-data/ajaxAction', ['as' => 'timetable-data-ajaxAction',               'uses' => 'v2\api\TimeTableApiController@ajaxAction']);
    Route::match(['post'], 'get-timetable-semester', ['as' => 'get-timetable-semester',                  'uses' => 'v2\api\TimeTableApiController@getTimetableSemester']);
    Route::match(['post'], 'get-default-times', ['as' => 'get-default-times',                       'uses' => 'v2\api\TimeTableApiController@getDefaultTimes']);
    Route::match(['post'], 'week-start-by-term', ['as' => 'week-start-by-term',                      'uses' => 'v2\api\TimeTableApiController@weekStartByTerm']);
    Route::match(['post'], 'week-end-by-term', ['as' => 'week-end-by-term',                        'uses' => 'v2\api\TimeTableApiController@weekEndByTerm']);
    Route::match(['post'], 'timetable-attendance-filterby-html', ['as' => 'timetable-attendance-filterby-html',      'uses' => 'v2\api\TimeTableApiController@attendanceFilterHtmlData']);  // TODO::not-used
    Route::match(['post'], 'calendar-view-filterby-html', ['as' => 'calendar-view-filterby-html',             'uses' => 'v2\api\TimeTableApiController@calendarViewHtmlData']);
    Route::match(['post'], 'view-timetable-filterby-html', ['as' => 'view-timetable-filterby-html',            'uses' => 'v2\api\TimeTableApiController@viewTimetableFilterHtmlData']);
    Route::match(['post'], 'get-course-subject-list', ['as' => 'get-course-subject-list',                 'uses' => 'v2\api\TimeTableApiController@getCourseSubjectList']);
    Route::match(['post'], 'get-break-time-range', ['as' => 'get-break-time-range',                    'uses' => 'v2\api\TimeTableApiController@getBreakTimeRange']);
    Route::match(['post'], 'replacement-teacher-list', ['as' => 'replacement-teacher-list',                'uses' => 'v2\api\TimeTableApiController@replacementTeacherList']);
    Route::match(['post'], 'replacement-teacher-year', ['as' => 'replacement-teacher-year',                'uses' => 'v2\api\TimeTableApiController@replacementTeacherYear']);
    Route::match(['post'], 'replacement-teacher-calenderType', ['as' => 'replacement-teacher-calenderType',        'uses' => 'v2\api\TimeTableApiController@replacementTeacherCalenderType']);
    Route::match(['post'], 'replacement-teacher-semester', ['as' => 'replacement-teacher-semester',            'uses' => 'v2\api\TimeTableApiController@replacementTeacherSemester']);
    Route::match(['post'], 'replacement-teacher-term', ['as' => 'replacement-teacher-term',                'uses' => 'v2\api\TimeTableApiController@replacementTeacherTerm']);
    Route::match(['post'], 'replacement-teacher-subject', ['as' => 'replacement-teacher-subject',             'uses' => 'v2\api\TimeTableApiController@replacementTeacherSubject']);
    Route::match(['post'], 'replacement-new-teacher-list', ['as' => 'replacement-new-teacher-list',            'uses' => 'v2\api\TimeTableApiController@replacementNewTeacherList']);
    Route::match(['post'], 'replacement-teacher-batch', ['as' => 'replacement-teacher-batch',               'uses' => 'v2\api\TimeTableApiController@replacementTeacherBatch']);
    Route::match(['post'], 'replacement-teacher-classroom', ['as' => 'replacement-teacher-classroom',           'uses' => 'v2\api\TimeTableApiController@replacementTeacherClassRoom']);
    Route::match(['post'], 'show-teachers-by-subject', ['as' => 'show-teachers-by-subject',                'uses' => 'v2\api\TimeTableApiController@showTeacherBySubject']);
    Route::match(['post'], 'get-timetable-status-data', ['as' => 'get-timetable-status-data',               'uses' => 'v2\api\TimeTableApiController@getTimetableStatusData']);
    Route::match(['post'], 'get-timetable-batch', ['as' => 'get-timetable-batch',                     'uses' => 'v2\api\TimeTableApiController@getTimetableBatch']);
    Route::match(['post'], 'get-assessor-list', ['as' => 'get-assessor-list',                       'uses' => 'v2\api\TimeTableApiController@getAssessorList']);
    Route::match(['post'], 'delete-timetable-data', ['as' => 'delete-timetable-data',                   'uses' => 'v2\api\TimeTableApiController@deleteTimetableData']);
    Route::match(['post'], 'delete-default-timetable-data', ['as' => 'delete-default-timetable-data',           'uses' => 'v2\api\TimeTableApiController@deleteDefaultTimetableData']);
    Route::match(['post'], 'timetable-campus-list', ['as' => 'timetable-campus-list',                   'uses' => 'v2\api\TimeTableApiController@timetableCampusList']);
    Route::match(['post'], 'trainer-full-name', ['as' => 'trainer-full-name',                       'uses' => 'v2\api\TimeTableApiController@trainerFullName']);

    // All Time table Filter
    Route::match(['post'], 'view-all-timetable-batch', ['as' => 'view-all-timetable-batch',                'uses' => 'v2\api\TimeTableApiController@viewAllTimeTableBatch']);
    Route::match(['post'], 'view-all-timetable-unit', ['as' => 'view-all-timetable-unit',                 'uses' => 'v2\api\TimeTableApiController@viewAllTimeTableUnit']);
    Route::match(['post'], 'view-all-timetable-location', ['as' => 'view-all-timetable-location',             'uses' => 'v2\api\TimeTableApiController@viewAllTimeTableLocation']);

    Route::match(['post'], 'print-attendance-campus', ['as' => 'print-attendance-campus',                 'uses' => 'v2\api\TimeTableApiController@printAttendanceCampus']);
    Route::match(['post'], 'print-attendance-semester', ['as' => 'print-attendance-semester',               'uses' => 'v2\api\TimeTableApiController@printAttendanceSemester']);
    Route::match(['post'], 'print-attendance-subject', ['as' => 'print-attendance-subject',                'uses' => 'v2\api\TimeTableApiController@printAttendanceSubject']);
    Route::match(['post'], 'print-attendance-batch', ['as' => 'print-attendance-batch',                  'uses' => 'v2\api\TimeTableApiController@printAttendanceBatch']);
    Route::match(['post'], 'print-attendance-classroom', ['as' => 'print-attendance-classroom',              'uses' => 'v2\api\TimeTableApiController@printAttendanceClassRoom']);
    Route::match(['post'], 'print-attendance-start-week', ['as' => 'print-attendance-start-week',             'uses' => 'v2\api\TimeTableApiController@printAttendanceStartWeek']);
    Route::match(['post'], 'print-attendance-end-week', ['as' => 'print-attendance-end-week',               'uses' => 'v2\api\TimeTableApiController@printAttendanceEndWeek']);
    Route::match(['post'], 'print-attendance-course-type', ['as' => 'print-attendance-course-type',            'uses' => 'v2\api\TimeTableApiController@printAttendanceCourseType']);
    Route::match(['post'], 'print-attendance-trainer', ['as' => 'print-attendance-trainer',                'uses' => 'v2\api\TimeTableApiController@printAttendanceTrainer']);

    // TCSI
    Route::match(['post'], 'tcsi-report-data', ['as' => 'tcsi-report-data',                        'uses' => 'v2\api\TcsiApiController@getReportData']);
    Route::match(['post'], 'tcsi-submission-type-data', ['as' => 'tcsi-submission-type-data',               'uses' => 'v2\api\TcsiApiController@getSubmissionTypeData']);
    Route::match(['post'], 'export-tcsi-report-data', ['as' => 'export-tcsi-report-data',                 'uses' => 'v2\api\TcsiApiController@exportTcsiReportData']);

    // Summary Tab
    //  Route::match(['post'],          'get-xero-data-for-summary',            ['as' => 'get-xero-data-for-summary',               'uses' => 'v2\api\StudentSummaryTabApiController@getXeroDataForSummary']);
    Route::match(['post'], 'get-student-summary-tab', ['as' => 'get-student-summary-tab',                 'uses' => 'v2\api\StudentSummaryTabApiController@studentSummaryData']);
    Route::match(['post'], 'get-student-activity-log-new', ['as' => 'get-student-activity-log-new',            'uses' => 'v2\api\StudentSummaryTabApiController@getStudentActivityLogNew']);
    Route::match(['post'], 'get-student-activity-log-with-flag', ['as' => 'get-student-activity-log-with-flag',     'uses' => 'v2\api\StudentSummaryTabApiController@getActivityLogForTab']);
    Route::match(['post'], 'delete-note-data', ['as' => 'delete-note-data',                        'uses' => 'v2\api\StudentSummaryTabApiController@deleteActivityNoteFromTimeLine']);
    Route::match(['post'], 'edit-activity-note', ['as' => 'edit-activity-note',                      'uses' => 'v2\api\StudentSummaryTabApiController@editActivityNote']);
    Route::match(['post'], 'add-new-activity-note-timeline', ['as' => 'add-new-activity-note-timeline',          'uses' => 'v2\api\StudentSummaryTabApiController@addNewActivityNoteFromTimeLine']);

    Route::match(['post'], 'get-assessment-details', ['as' => 'get-assessment-details',                  'uses' => 'v2\api\StudentResultTabApiController@getAssessmentDetails']);
    Route::match(['post'], 'update-assessment-details', ['as' => 'update-assessment-details',               'uses' => 'v2\api\StudentResultTabApiController@updateAssessmentDetails']);

    // Result Tab
    Route::match(['get', 'post'], 'get-result-tab-data', ['as' => 'get-result-tab-data',                         'uses' => 'v2\api\StudentResultTabApiController@getResultTabData']);
    Route::match(['post'], 'student-result-unit-data', ['as' => 'student-result-unit-data',                    'uses' => 'v2\api\StudentResultTabApiController@studentResultUnitData']);
    Route::match(['post'], 'student-status-unit-data', ['as' => 'student-status-unit-data',                    'uses' => 'v2\api\StudentResultTabApiController@studentStatusUnitData']);
    Route::match(['post'], 'check-previous-course-found', ['as' => 'check-previous-course-found',                 'uses' => 'v2\api\StudentResultTabApiController@checkPreviousCourseFound']);
    Route::match(['post'], 'add-unit-on-course-status-change', ['as' => 'add-unit-on-course-status-change',            'uses' => 'v2\api\StudentResultTabApiController@addUnitOnCourseStatusChange']);
    Route::match(['post'], 'student-result-semester-data', ['as' => 'student-result-semester-data',                'uses' => 'v2\api\StudentResultTabApiController@studentResultSemesterData']);
    Route::match(['post'], 'update-student-result-semester-data', ['as' => 'update-student-result-semester-data',         'uses' => 'v2\api\StudentResultTabApiController@updateStudentResultSemester']);
    Route::match(['post'], 'delete-student-result-data-semester', ['as' => 'delete-student-result-data-semester',         'uses' => 'v2\api\StudentResultTabApiController@deleteStudentResultSemester']);
    Route::match(['post'], 'save-tcsi-student-unit-enrollment-form', ['as' => 'save-tcsi-student-unit-enrollment-form',      'uses' => 'v2\api\StudentResultTabApiController@saveTcsiStudentUnitEnrollmentForm']);
    Route::match(['post'], 'get-tcsi-student-unit-enrollment-data', ['as' => 'get-tcsi-student-unit-enrollment-data',       'uses' => 'v2\api\StudentResultTabApiController@getTcsiStudentUnitEnrollment']);
    Route::match(['post'], 'get-tcsi-student-unit-enroll-form-list', ['as' => 'get-tcsi-student-unit-enroll-form-list',      'uses' => 'v2\api\StudentResultTabApiController@getTcsiStudentUnitEnrollmentFormDropdownList']);
    Route::match(['post'], 'get-student-subject-outcome', ['as' => 'get-student-subject-outcome',                 'uses' => 'v2\api\StudentResultTabApiController@getStudentSubjectOutcomeAndAvetmissData']);
    Route::match(['post'], 'get-student-outcome-form-dropdown-list', ['as' => 'get-student-outcome-form-dropdown-list',      'uses' => 'v2\api\StudentResultTabApiController@getStudentOutcomeFormDropdownList']);
    Route::match(['post'], 'get-transfer-to-another-course-form-dropdown-list', ['as' => 'get-transfer-to-another-course-form-dropdown-list',      'uses' => 'v2\api\StudentResultTabApiController@getTransferToAnotherCourseFormDropdownList']);
    Route::match(['post'], 'get-student-course-info', ['as' => 'get-student-course-info',                 'uses' => 'v2\api\StudentResultTabApiController@getStudentCourseInfo']);
    Route::match(['post'], 'save-subject-outcome-form', ['as' => 'save-subject-outcome-form',                   'uses' => 'v2\api\StudentResultTabApiController@saveSubjectOutcomeForm']);
    Route::match(['post'], 'transfer-result-to-another-course', ['as' => 'transfer-result-to-another-course',                   'uses' => 'v2\api\StudentResultTabApiController@transferResultToAnotherCourse']);
    Route::match(['post'], 'get-student-unit-outcome', ['as' => 'get-student-unit-outcome',                    'uses' => 'v2\api\StudentResultTabApiController@getStudentUnitOutcome']);
    Route::match(['post'], 'get-unit-outcome-form-dropdown-list', ['as' => 'get-unit-outcome-form-dropdown-list',         'uses' => 'v2\api\StudentResultTabApiController@getUnitOutcomeFormDropdownList']);
    Route::match(['post'], 'save-unit-outcome-form', ['as' => 'save-unit-outcome-form',                      'uses' => 'v2\api\StudentResultTabApiController@saveUnitOutcomeForm']);
    Route::match(['post'], 'get-student-avetmiss', ['as' => 'get-student-avetmiss',                        'uses' => 'v2\api\StudentResultTabApiController@getStudentSubjectOutcomeAndAvetmissData']);
    Route::match(['post'], 'get-avetmiss-form-dropdown-list', ['as' => 'get-avetmiss-form-dropdown-list',              'uses' => 'v2\api\StudentResultTabApiController@getAvetmissFormDropdownList']);
    Route::match(['post'], 'save-avetmiss-form', ['as' => 'save-avetmiss-form',                          'uses' => 'v2\api\StudentResultTabApiController@saveAvitmessForm']);
    Route::match(['post'], 'get-selected-unit-subject-data', ['as' => 'get-selected-unit-subject-data',              'uses' => 'v2\api\StudentResultTabApiController@getSelectedUnitSubject']);
    Route::match(['post'], 'get-student-details-data', ['as' => 'get-student-details-data',                    'uses' => 'v2\api\StudentResultTabApiController@getStudentDetails']);
    Route::match(['post'], 'get-predominant-delivery-mode', ['as' => 'get-predominant-delivery-mode',               'uses' => 'v2\api\StudentResultTabApiController@getPredominantDeliveryMode']);
    Route::match(['post'], 'get-batch-date-info', ['as' => 'get-batch-date-info',                         'uses' => 'v2\api\StudentResultTabApiController@getBatchDateInfo']);

    Route::match(['post'], 'save-highered-unit-outCome-form', ['as' => 'save-highered-unit-outCome-form',             'uses' => 'v2\api\StudentResultTabApiController@saveHigherEdUnitOutComeForm']);

    // Common Student Profile
    Route::match(['post'], 'add-new-activity-note', ['as' => 'add-new-activity-note',                   'uses' => 'v2\api\StudentProfileCommonApiController@addNewActivityNote']);
    Route::match(['get', 'post'], 'update-student-course-status', ['as' => 'update-student-course-status',            'uses' => 'v2\api\StudentProfileCommonApiController@updateStudentCourseStatus']);
    Route::match(['get', 'post'], 'student-status-history-list', ['as' => 'student-status-history-list',             'uses' => 'v2\api\StudentProfileCommonApiController@getStatusHistoryData']);
    Route::match(['post'], 'get-student-courses', ['as' => 'get-student-courses',                     'uses' => 'v2\api\StudentProfileCommonApiController@studentCoursesData']);
    Route::match(['post'], 'get-offer-id-list', ['as' => 'get-offer-id-list',                       'uses' => 'v2\api\StudentProfileCommonApiController@getOfferIdList']);
    Route::match(['post'], 'get-course-list', ['as' => 'get-course-list',                         'uses' => 'v2\api\StudentProfileCommonApiController@getStudentCourseList']);
    Route::match(['post'], 'get-campus-course-list', ['as' => 'get-campus-course-list',                  'uses' => 'v2\api\StudentProfileCommonApiController@getStudentCourseCampusWiseList']);
    Route::match(['post'], 'get-all-student-course-list', ['as' => 'get-all-student-course-list',             'uses' => 'v2\api\StudentProfileCommonApiController@getAllStudentCoursesList']);
    Route::match(['post'], 'get-all-campus-list', ['as' => 'get-all-campus-list',                     'uses' => 'v2\api\StudentProfileCommonApiController@getCollegeCampusList']);
    Route::match(['post'], 'get-result-cal-method', ['as' => 'get-result-cal-method',                   'uses' => 'v2\api\StudentProfileCommonApiController@getResultCalMethod']);
    Route::match(['post'], 'get-agent-list', ['as' => 'get-agent-list',                          'uses' => 'v2\api\StudentProfileCommonApiController@getAgentList']);
    Route::match(['post'], 'get-course-wise-template-list', ['as' => 'get-course-wise-template-list',           'uses' => 'v2\api\StudentProfileCommonApiController@getCourseWiseTemplateList']);

    Route::match(['get', 'post'], 'save-enroll-student-course', ['as' => 'save-enroll-student-course',              'uses' => 'v2\api\StudentProfileCommonApiController@insertEnrollStudentCourse']);
    Route::match(['post'], 'student-send-email', ['as' => 'student-send-email',                      'uses' => 'v2\api\StudentProfileCommonApiController@studentSendEmail']);
    Route::match(['post'], 'student-send-email-v2', ['as' => 'student-send-email-v2',                   'uses' => 'v2\api\StudentProfileCommonApiController@studentSendEmailWithQueue']);
    Route::match(['post'], 'student-add-sms-details', ['as' => 'student-add-sms-details',                 'uses' => 'v2\api\StudentProfileCommonApiController@addSmsDetails']);
    Route::match(['post'], 'student-issue-letter-email', ['as' => 'student-issue-letter-email',              'uses' => 'v2\api\StudentProfileCommonApiController@studentIssueLetterEmail']);
    Route::match(['post'], 'get-student-details', ['as' => 'get-student-details',                     'uses' => 'v2\api\StudentProfileCommonApiController@getStudentDetail']);
    Route::match(['post'], 'calculate-course-end-date', ['as' => 'calculate-course-end-date',               'uses' => 'v2\api\StudentProfileCommonApiController@calculateCourseEndDate']);
    Route::match(['post'], 'upload-student-profile-pic', ['as' => 'upload-student-profile-pic',              'uses' => 'v2\api\StudentProfileCommonApiController@uploadStudentProfilePic']);
    Route::match(['post'], 'get-from-email-id', ['as' => 'get-from-email-id',                       'uses' => 'v2\api\StudentProfileCommonApiController@getFromEmailId']);

    // Course Tab
    Route::match(['post'], 'get-certificate-issue-data', ['as' => 'get-certificate-issue-data',              'uses' => 'v2\api\StudentCourseApiController@getCertificateIssueList']);
    Route::match(['post'], 'get-enroll-subject-data', ['as' => 'get-enroll-subject-data',                 'uses' => 'v2\api\StudentCourseApiController@getEnrollSubjectList']);
    Route::match(['post'], 'get-enroll-subject-semester', ['as' => 'get-enroll-subject-semester',             'uses' => 'v2\api\StudentCourseApiController@getEnrollSubSemester']);
    Route::match(['post'], 'get-enroll-venue', ['as' => 'get-enroll-venue',                        'uses' => 'v2\api\StudentCourseApiController@getEnrollVenue']);
    Route::match(['post'], 'get-enrolled-course', ['as' => 'get-enrolled-course',                     'uses' => 'v2\api\StudentCourseApiController@getEnrolledCourse']);
    Route::match(['post'], 'update-result-course', ['as' => 'update-result-course',                    'uses' => 'v2\api\StudentCourseApiController@updateResultCourse']);
    Route::match(['post'], 'get-study-reason', ['as' => 'get-study-reason',                        'uses' => 'v2\api\StudentCourseApiController@getStudyReason']);

    Route::match(['post'], 'save-student-certificate-register', ['as' => 'save-student-certificate-register',       'uses' => 'v2\api\StudentCourseApiController@saveStudentCertificate']);
    Route::match(['post'], 'delete-student-certificate', ['as' => 'delete-student-certificate',              'uses' => 'v2\api\StudentCourseApiController@deleteStudentCertificate']);
    Route::match(['post'], 'upload-student-certificate', ['as' => 'upload-student-certificate',              'uses' => 'v2\api\StudentCourseApiController@uploadStudentCertificate']);
    Route::match(['post'], 'download-student-certificate', ['as' => 'download-student-certificate-api',        'uses' => 'v2\api\StudentCourseApiController@downloadStudentCertificate']);

    Route::match(['post'], 'get-mark-outcome-list', ['as' => 'get-mark-outcome-list',                   'uses' => 'v2\api\StudentCourseApiController@getMarkOutcomeData']);
    Route::match(['post'], 'get-term-data', ['as' => 'get-term-data',                           'uses' => 'v2\api\StudentCourseApiController@getTermList']);
    Route::match(['post'], 'get-term-detail', ['as' => 'get-term-detail',                         'uses' => 'v2\api\StudentCourseApiController@getTermDetails']);
    Route::match(['post'], 'save-student-subject-enrollment', ['as' => 'save-student-subject-enrollment',         'uses' => 'v2\api\StudentCourseApiController@saveStudentSubjectEnrollment']);
    Route::match(['post'], 'get-student-course-details', ['as' => 'get-student-course-details',              'uses' => 'v2\api\StudentCourseApiController@getStudentCourseDetails']);
    Route::match(['post'], 'update-enroll-student-course', ['as' => 'update-enroll-student-course',            'uses' => 'v2\api\StudentCourseApiController@updateEnrollStudentCourse']);
    Route::match(['post'], 'get-contract_schedule_data', ['as' => 'get-contract_schedule_data',              'uses' => 'v2\api\StudentCourseApiController@getContractScheduleData']);

    Route::match(['post'], 'get-oshc-info', ['as' => 'get-oshc-info',                           'uses' => 'v2\api\StudentCourseApiController@getOshcInfo']);
    Route::match(['post'], 'get-oshc-provider', ['as' => 'get-oshc-provider',                       'uses' => 'v2\api\StudentCourseApiController@getOshcProvider']);
    Route::match(['post'], 'get-oshc-type', ['as' => 'get-oshc-type',                           'uses' => 'v2\api\StudentCourseApiController@getOshcType']);
    Route::match(['post'], 'get-oshc-mis-payment-duration', ['as' => 'get-oshc-mis-payment-duration',           'uses' => 'v2\api\StudentCourseApiController@getOshcDuration']);
    Route::match(['post'], 'get-student-oshc-data', ['as' => 'get-student-oshc-data',                   'uses' => 'v2\api\StudentCourseApiController@getStudentOshcData']);
    Route::match(['post'], 'save-student-oshc-service', ['as' => 'save-student-oshc-service',               'uses' => 'v2\api\StudentCourseApiController@saveStudentOshcServiceData']);

    Route::match(['post'], 'get-services-category', ['as' => 'get-services-category',                   'uses' => 'v2\api\StudentCourseApiController@getServicesCategory']);
    Route::match(['post'], 'get-facility-name', ['as' => 'get-facility-name',                       'uses' => 'v2\api\StudentCourseApiController@getFacilityName']);
    Route::match(['post'], 'get-service-provider', ['as' => 'get-service-provider',                    'uses' => 'v2\api\StudentCourseApiController@getServiceProvider']);
    Route::match(['post'], 'get-provider-price', ['as' => 'get-provider-price',                      'uses' => 'v2\api\StudentCourseApiController@getProviderPrice']);
    Route::match(['post'], 'save-student-additional-service', ['as' => 'save-student-additional-service',         'uses' => 'v2\api\StudentCourseApiController@saveStudentAdditionalService']);
    Route::match(['post'], 'get-student-additional-service', ['as' => 'get-student-additional-service',          'uses' => 'v2\api\StudentCourseApiController@getAdditionalServiceData']);
    Route::match(['post'], 'delete-student-additional-service', ['as' => 'delete-student-additional-service',       'uses' => 'v2\api\StudentCourseApiController@deleteAdditionalService']);
    Route::match(['post'], 'get-student-additional-services', ['as' => 'get-student-additional-services',         'uses' => 'v2\api\StudentCourseApiController@getAdditionalServiceList']);

    Route::match(['post'], 'get-fee-schedule-list', ['as' => 'get-fee-schedule-list',                   'uses' => 'v2\api\StudentCourseApiController@getFeeScheduleList']);
    Route::match(['post'], 'get-fee-schedule-details', ['as' => 'get-fee-schedule-details',                'uses' => 'v2\api\StudentCourseApiController@getFeeScheduleDetails']);

    Route::match(['post'], 'get-upfront-fee-schedule', ['as' => 'get-upfront-fee-schedule',                'uses' => 'v2\api\StudentCourseApiController@saveUpfrontFeeSchedule']);
    Route::match(['post'], 'get-student-document', ['as' => 'get-student-document',                    'uses' => 'v2\api\StudentCourseApiController@getStudentOfferChecklist']);
    Route::match(['post'], 'upload-student-document', ['as' => 'upload-student-document',                 'uses' => 'v2\api\StudentCourseApiController@uploadOfferChecklistDocument']);
    Route::match(['post'], 'uploaded-student-doc-data', ['as' => 'uploaded-student-doc-data',               'uses' => 'v2\api\StudentCourseApiController@uploadedOfferChecklistDocumentsData']);

    Route::match(['post'], 'preview-offer-letter', ['as' => 'preview-offer-letter',                    'uses' => 'v2\api\StudentCourseApiController@previewOfferLetter']);
    Route::match(['get'], 'generate-agent-invoice-pdf', ['as' => 'generate-agent-invoice-pdf',              'uses' => 'v2\api\StudentCourseApiController@generateAgentInvoicePdf']);
    Route::match(['get', 'post'], 'get-section-type-list', ['as' => 'get-section-type-list',                   'uses' => 'v2\api\StudentCourseApiController@getSectionTypeList']);
    Route::match(['get', 'post'], 'add-offer-communication-log', ['as' => 'add-offer-communication-log',             'uses' => 'v2\api\StudentCourseApiController@addOfferCommunicationLog']);
    Route::match(['get', 'post'], 'get-offer-id', ['as' => 'get-offer-id',                            'uses' => 'v2\api\StudentCourseApiController@getOfferId']);
    Route::match(['get', 'post'], 'get-offer-communication-log-list', ['as' => 'get-offer-communication-log-list',        'uses' => 'v2\api\StudentCourseApiController@getOfferCommunicationLogList']);
    Route::match(['get', 'post'], 'delete-offer-communication-log', ['as' => 'delete-offer-communication-log',          'uses' => 'v2\api\StudentCourseApiController@deleteOfferCommunicationLog']);
    Route::match(['get', 'post'], 'enable-disable-offer-communication-log', ['as' => 'enable-disable-offer-communication-log', 'uses' => 'v2\api\StudentCourseApiController@enableDisableOfferCommunicationLog']);
    Route::match(['get', 'post'], 'get-course-tab-data', ['as' => 'get-course-tab-data',                     'uses' => 'v2\api\StudentCourseApiController@getCourseTabData']);
    Route::match(['get', 'post'], 'get-certificate-type', ['as' => 'get-certificate-type',                    'uses' => 'v2\api\StudentCourseApiController@getCertificateType']);
    Route::match(['get', 'post'], 'get-student-coe-details', ['as' => 'get-student-coe-details',                 'uses' => 'v2\api\StudentCourseApiController@getStudentCOEDetails']);
    Route::match(['get', 'post'], 'save-student-coe-details', ['as' => 'save-student-coe-details',                'uses' => 'v2\api\StudentCourseApiController@saveStudentCOEDetails']);

    Route::match(['get', 'post'], 'get-offer-schedule-data', ['as' => 'get-offer-schedule-data',                 'uses' => 'v2\api\StudentCourseApiController@getOfferScheduleData']);
    Route::match(['get', 'post'], 'save-offer-schedule-data', ['as' => 'save-offer-schedule-data',                'uses' => 'v2\api\StudentCourseApiController@saveOfferScheduleData']);
    Route::match(['get', 'post'], 'get-courses-list-from-type', ['as' => 'get-courses-list-from-type',                'uses' => 'v2\api\StudentCourseApiController@getCoursesListFromType']);
    Route::match(['get', 'post'], 'get-intake-details-list-from-course', ['as' => 'get-intake-details-list-from-course',                'uses' => 'v2\api\StudentCourseApiController@getIntakeDetails']);
    Route::match(['get', 'post'], 'get-intake-data-list-from-year', ['as' => 'get-intake-data-list-from-year',                'uses' => 'v2\api\StudentCourseApiController@getIntakeDetailsFromYear']);

    Route::match(['get'], 'generate-receipt-additional-payment-service-pdf', ['as' => 'generate-receipt-additional-payment-service-pdf',         'uses' => 'v2\api\StudentPaymentApiController@generateReceiptAdditionalPaymentServicePdf']);
    Route::match(['get'], 'generate-invoices-additional-payment-service-pdf', ['as' => 'generate-invoices-additional-payment-service-pdf',        'uses' => 'v2\api\StudentPaymentApiController@generateInvoicesAdditionalPaymentServicePdf']);
    Route::match(['get'], 'generate-refund-receipt-additional-payment-service-pdf', ['as' => 'generate-refund-receipt-additional-payment-service-pdf',  'uses' => 'v2\api\StudentPaymentApiController@generateRefundReceiptAdditionalPaymentServicePdf']);
    Route::match(['get', 'post'], 'save-reverse-transaction-additional-payment-service', ['as' => 'save-reverse-transaction-additional-payment-service',     'uses' => 'v2\api\StudentPaymentApiController@saveReverseTransactionAdditionalPaymentService']);
    Route::match(['get', 'post'], 'revert-back-transaction-additional-payment-service', ['as' => 'revert-back-transaction-additional-payment-service',      'uses' => 'v2\api\StudentPaymentApiController@revertBackTransactionAdditionalPaymentService']);
    Route::match(['get', 'post'], 'generate-miscellaneous-payment-refund-receipt-pdf', ['as' => 'generate-miscellaneous-payment-refund-receipt-pdf',       'uses' => 'v2\api\StudentPaymentApiController@generateMiscellaneousPaymentRefundPdf']);
    Route::match(['get', 'post'], 'delete-payment-invoice-credit-detail', ['as' => 'delete-payment-invoice-credit-detail',                    'uses' => 'v2\api\StudentPaymentApiController@deleteInvoiceCreditData']);

    Route::match(['get'], 'generate-agent-pro-invoice-pdf', ['as' => 'generate-agent-pro-invoice-pdf',          'uses' => 'v2\api\StudentPaymentApiController@generateAgentProInvoicePdf']);
    Route::match(['get'], 'generate-schedule-invoice-pdf', ['as' => 'generate-schedule-invoice-pdf',           'uses' => 'v2\api\StudentPaymentApiController@generateStudentScheduleInvoicePdf']);
    Route::match(['get'], 'generate-student-tax-receipt-pdf', ['as' => 'generate-student-tax-receipt-pdf',        'uses' => 'v2\api\StudentPaymentApiController@generateStudentTaxReceiptPdf']);
    Route::match(['get'], 'generate-agent-tax-receipt-pdf', ['as' => 'generate-agent-tax-receipt-pdf',          'uses' => 'v2\api\StudentPaymentApiController@generateAgentTaxReceiptPdf']);
    Route::match(['get'], 'generate-combined-course-invoice-pdf', ['as' => 'generate-combined-course-invoice-pdf',    'uses' => 'v2\api\StudentPaymentApiController@generateCombinedCourseInvoicePdf']);

    // Attendance Tab
    Route::match(['post'], 'get-student-attendance-overview', ['as' => 'get-student-attendance-overview',         'uses' => 'v2\api\StudentAttendanceApiController@getAttendanceOverviewData']);
    Route::match(['post'], 'get-student-attendance-grid-data', ['as' => 'get-student-attendance-grid-data',        'uses' => 'v2\api\StudentAttendanceApiController@getAttendanceGridData']);
    Route::match(['post'], 'get-student-attendance-schedule-data', ['as' => 'get-student-attendance-schedule-data',    'uses' => 'v2\api\StudentAttendanceApiController@getAttendanceScheduleData']);
    Route::match(['post'], 'get-student-attendance-batch-data', ['as' => 'get-student-attendance-batch-data',       'uses' => 'v2\api\StudentAttendanceApiController@getAttendanceBatchData']);
    Route::match(['post'], 'get-student-attendance-dashboard-data', ['as' => 'get-student-attendance-dashboard-data',   'uses' => 'v2\api\StudentAttendanceApiController@getStudentAttendanceDashboardData']);
    Route::match(['post'], 'get-student-attendance-filter-data', ['as' => 'get-student-attendance-filter-data',      'uses' => 'v2\api\StudentAttendanceApiController@getStudentAttendanceFilterData']);

    // Student Payment V2
    Route::match(['post'], 'get-invoice-number', ['as' => 'get-invoice-number',                      'uses' => 'v2\api\StudentPaymentApiControllerV2@getInvoiceNumber']);
    Route::match(['post'], 'add-new-payment-schedule-details', ['as' => 'add-new-payment-schedule-details',        'uses' => 'v2\api\StudentPaymentApiController@generateNewSchedule']);
    Route::match(['post'], 'get-agent-commission-list', ['as' => 'get-agent-commission-list',               'uses' => 'v2\api\StudentPaymentApiControllerV2@getAgentCommission']);
    Route::match(['post'], 'get-payment-schedule-details', ['as' => 'get-payment-schedule-details',            'uses' => 'v2\api\StudentPaymentApiController@getModifyPaymentSchedule']);
    Route::match(['post'], 'get-payment-invoice-credit-detail', ['as' => 'get-payment-invoice-credit-detail',       'uses' => 'v2\api\StudentPaymentApiControllerV2@getPaymentInvoiceCreditDetail']);
    Route::match(['post'], 'view-payment-schedule-details', ['as' => 'view-payment-schedule-details',           'uses' => 'v2\api\StudentPaymentApiControllerV2@viewStudentSchedulePaymentSchedule']);
    Route::match(['post'], 'get-installment-number', ['as' => 'get-installment-number',                  'uses' => 'v2\api\StudentPaymentApiControllerV2@getInstallmentNumber']);

    Route::match(['post'], 'add-miscellaneous-request-details', ['as' => 'add-miscellaneous-request-details',       'uses' => 'v2\api\StudentPaymentApiController@addMiscellaneousRequestDetails']);
    Route::match(['post'], 'edit-miscellaneous-request-details', ['as' => 'edit-miscellaneous-request-details',      'uses' => 'v2\api\StudentPaymentApiController@editMiscellaneousRequestDetails']);
    Route::match(['post'], 'save-generate-payment-schedule', ['as' => 'save-generate-payment-schedule',          'uses' => 'v2\api\StudentPaymentApiControllerV2@saveGeneratePaymentSchedule']);
    Route::match(['post'], 'delete-miscellaneous-transaction', ['as' => 'delete-miscellaneous-transaction',        'uses' => 'v2\api\StudentPaymentApiControllerV2@deleteMiscellaneousTransactionDetail']);
    Route::match(['post'], 'save-delete-miscellaneous-transaction', ['as' => 'save-delete-miscellaneous-transaction',   'uses' => 'v2\api\StudentPaymentApiController@saveDeleteMiscellaneousTransaction']);

    Route::match(['post'], 'delete-installment-transaction', ['as' => 'delete-installment-transaction',          'uses' => 'v2\api\StudentPaymentApiControllerV2@deleteInstallmentTransactionDetail']);

    Route::match(['post'], 'get-generated-upfront-fee-schedule', ['as' => 'get-generated-upfront-fee-schedule',      'uses' => 'v2\api\StudentPaymentApiControllerV2@generateUpfrontFeeSchedule']);
    Route::match(['post'], 'get-agent-commission-rate', ['as' => 'get-agent-commission-rate',               'uses' => 'v2\api\StudentPaymentApiControllerV2@getAgentCommissionRate']);
    Route::match(['post'], 'get-refund-payment-data', ['as' => 'get-refund-payment-data',                 'uses' => 'v2\api\StudentPaymentApiControllerV2@getRefundPayment']);
    Route::match(['post'], 'save-refund-payment-request', ['as' => 'save-refund-payment-request',             'uses' => 'v2\api\StudentPaymentApiControllerV2@saveRefundPaymentRequest']);
    Route::match(['post'], 'save-reverse-transaction-request', ['as' => 'save-reverse-transaction-request',        'uses' => 'v2\api\StudentPaymentApiController@saveMiscellaneousPaymentReverseTransactionPaymentRequest']);
    Route::match(['post'], 'get-recommended-commission-rate', ['as' => 'get-recommended-commission-rate',         'uses' => 'v2\api\StudentPaymentApiController@getRecommendedCommissionRate']);

    Route::match(['get', 'post'], 'save-miscellaneous-payment', ['as' => 'save-miscellaneous-payment',              'uses' => 'v2\api\StudentPaymentApiController@saveMiscellaneousPayment']);
    Route::match(['get', 'post'], 'miscellaneous-payment-receipt-pdf', ['as' => 'miscellaneous-payment-receipt-pdf',       'uses' => 'v2\api\StudentPaymentApiControllerV2@miscellaneousPaymentReceiptPdf']);
    Route::match(['get', 'post'], 'miscellaneous-payment-invoice-pdf', ['as' => 'miscellaneous-payment-invoice-pdf',       'uses' => 'v2\api\StudentPaymentApiControllerV2@miscellaneousPaymentInvoicePdf']);
    Route::match(['post'], 'get-generated-upfront-fee-schedule', ['as' => 'get-generated-upfront-fee-schedule',      'uses' => 'v2\api\StudentPaymentApiControllerV2@generateUpfrontFeeSchedule']);
    Route::match(['post'], 'save-edit-payment-schedule', ['as' => 'save-edit-payment-schedule',              'uses' => 'v2\api\StudentPaymentApiController@editStudentPaymentSchedule']);

    Route::match(['post'], 'check-exist-payment-schedule', ['as' => 'check-exist-payment-schedule',            'uses' => 'v2\api\StudentPaymentApiControllerV2@checkExistPaymentSchedule']);
    Route::match(['post'], 'get-generate-payment-schedule-form-data', ['as' => 'get-generate-payment-schedule-form-data', 'uses' => 'v2\api\StudentPaymentApiController@getGeneratePaymentScheduleFormData']);
    Route::match(['post'], 'isvalid-remaining-payment-schedule', ['as' => 'isvalid-remaining-payment-schedule',      'uses' => 'v2\api\StudentPaymentApiControllerV2@checkIsValidForPaymentSchedule']);     // TODO::GNG-1994
    Route::match(['post'], 'get-generated-payment-schedule-data', ['as' => 'get-generated-payment-schedule-data',     'uses' => 'v2\api\StudentPaymentApiControllerV2@getGeneratedPaymentScheduleData']);     // TODO::GNG-1994
    Route::match(['post'], 'save-regenerate-payment-schedule', ['as' => 'save-regenerate-payment-schedule',        'uses' => 'v2\api\StudentPaymentApiControllerV2@saveReGeneratePaymentScheduleData']);   // TODO::GNG-1994

    Route::match(['post'], 'detail-for-common-div-generate-payment-schedule', ['as' => 'detail-for-common-div-generate-payment-schedule',     'uses' => 'v2\api\StudentPaymentApiControllerV2@getDetailForCommonDivGeneratePaymentSchedule']);
    Route::match(['post'], 'save-revert-back-transaction-miscellaneous-payment', ['as' => 'save-revert-back-transaction-miscellaneous-payment',  'uses' => 'v2\api\StudentPaymentApiController@saveRevertBackTransactionMiscellaneousPayment']);
    Route::match(['get', 'post'], 'get-miscellaneous-payment-transaction-data', ['as' => 'get-miscellaneous-payment-transaction-data',          'uses' => 'v2\api\StudentPaymentApiController@getMiscellaneousPaymentTransactionData']);
    Route::match(['get', 'post'], 'get-service-payment-transaction-data', ['as' => 'get-service-payment-transaction-data',                'uses' => 'v2\api\StudentPaymentApiController@getServicePaymentTransactionData']);

    Route::match(['post'], 'get-upfront-fee-form-data', ['as' => 'get-upfront-fee-form-data',           'uses' => 'v2\api\StudentPaymentApiController@getUpfrontFeeFormData']);

    // Student Profile More Actions
    Route::match(['post'], 'save-student-sanction', ['as' => 'save-student-sanction',               'uses' => 'v2\api\StudentProfileMoreActionApiController@saveStudentSanction']);
    Route::match(['post'], 'get-student-sanction-list', ['as' => 'get-student-sanction-list',           'uses' => 'v2\api\StudentProfileMoreActionApiController@getStudentSanctionList']);
    Route::match(['post'], 'get-student-sanction-data', ['as' => 'get-student-sanction-data',           'uses' => 'v2\api\StudentProfileMoreActionApiController@getStudentSanctionDetail']);
    Route::match(['post'], 'update-student-sanction', ['as' => 'update-student-sanction',             'uses' => 'v2\api\StudentProfileMoreActionApiController@updateStudentSanction']);
    Route::match(['post'], 'student-sanction-delete', ['as' => 'student-sanction-delete',             'uses' => 'v2\api\StudentProfileMoreActionApiController@deleteStudentSanction']);

    Route::match(['post'], 'save-exit-interview', ['as' => 'save-exit-interview',                 'uses' => 'v2\api\StudentProfileMoreActionApiController@saveExitInterview']);
    Route::match(['post'], 'get-student-exit-interview-list', ['as' => 'get-student-exit-interview-list',     'uses' => 'v2\api\StudentProfileMoreActionApiController@getStudentExitInterviewList']);
    Route::match(['post'], 'get-student-exit-interview-detail', ['as' => 'get-student-exit-interview-detail',   'uses' => 'v2\api\StudentProfileMoreActionApiController@getExitInterview']);
    Route::match(['post'], 'update-exit-interview', ['as' => 'update-exit-interview',               'uses' => 'v2\api\StudentProfileMoreActionApiController@updateExitInterview']);

    Route::match(['post'], 'delete-student-exit-interview', ['as' => 'delete-student-exit-interview',       'uses' => 'v2\api\StudentProfileMoreActionApiController@deleteStudentExitInterview']);

    Route::match(['post'], 'get-defer-student-list', ['as' => 'get-defer-student-list',              'uses' => 'v2\api\StudentProfileMoreActionApiController@getDeferStudentList']);
    Route::match(['post'], 'get-defer-student-detail', ['as' => 'get-defer-student-detail',            'uses' => 'v2\api\StudentProfileMoreActionApiController@getDeferStudentDetail']);
    Route::match(['post'], 'delete-defer-student', ['as' => 'delete-defer-student',                'uses' => 'v2\api\StudentProfileMoreActionApiController@deleteDeferStudent']);
    Route::match(['post'], 'update-defer-student', ['as' => 'update-defer-student',                'uses' => 'v2\api\StudentProfileMoreActionApiController@updateDeferStudent']);
    Route::match(['get', 'post'], 'print-defer-student/{courseDeferId}', ['as' => 'print-defer-student',                 'uses' => 'v2\api\StudentProfileMoreActionApiController@printDeferStudent']);

    Route::match(['get', 'post'], 'get-student-card/{studentId}', ['as' => 'get-student-card',                    'uses' => 'v2\api\StudentProfileMoreActionApiController@getStudentCard']);
    Route::match(['get', 'post'], 'send-student-password-reset-link', ['as' => 'send-student-password-reset-link',    'uses' => 'v2\api\StudentProfileMoreActionApiController@studentResetPasswordEmail']);
    Route::match(['get', 'post'], 'get-reason-for-student-course-defer', ['as' => 'get-reason-for-student-course-defer', 'uses' => 'v2\api\StudentProfileMoreActionApiController@getDeferReasonForStudent']);
    Route::match(['get', 'post'], 'save-defer-reason-student', ['as' => 'save-defer-reason-student',           'uses' => 'v2\api\StudentProfileMoreActionApiController@saveDeferReasonForStudent']);
    Route::match(['get', 'post'], 'send-reactivation-email-student', ['as' => 'send-reactivation-email-student',     'uses' => 'v2\api\StudentProfileMoreActionApiController@studentReActivationEmail']);

    Route::match(['get', 'post'], 'get-tcsi-student-data', ['as' => 'get-tcsi-student-data',               'uses' => 'v2\api\StudentProfileMoreActionApiController@getTcsiStudentDetail']);
    Route::match(['get', 'post'], 'save-tcsi-student-details', ['as' => 'save-tcsi-student-details',           'uses' => 'v2\api\StudentProfileMoreActionApiController@saveStudentTcsiDetails']);
    Route::match(['get', 'post'], 'upload-file-email-text-editor', ['as' => 'upload-file-email-text-editor',       'uses' => 'v2\api\CommonApiController@uploadEmailTextareaDocuments']);

    Route::match(['get', 'post'], 'get-tcsi-student-course-info', ['as' => 'get-tcsi-student-course-info',        'uses' => 'v2\api\StudentProfileMoreActionApiController@getStudentCourseInformationFormData']);
    Route::match(['get', 'post'], 'save-tcsi-student-course-info', ['as' => 'save-tcsi-student-course-info',       'uses' => 'v2\api\StudentProfileMoreActionApiController@saveStudentCourseInformation']);

    Route::match(['get', 'post'], 'get-tcsi-disability-info', ['as' => 'get-tcsi-disability-info',            'uses' => 'v2\api\StudentProfileMoreActionApiController@getDisabilityInformationFormData']);
    Route::match(['get', 'post'], 'save-tcsi-disability-info', ['as' => 'save-tcsi-disability-info',           'uses' => 'v2\api\StudentProfileMoreActionApiController@saveDisabilityInformationDetails']);

    Route::match(['get', 'post'], 'get-sa-help-data', ['as' => 'get-sa-help-data',                    'uses' => 'v2\api\StudentProfileMoreActionApiController@getSaHelpFormData']);
    Route::match(['get', 'post'], 'save-student-sa-help-details', ['as' => 'save-student-sa-help-details',        'uses' => 'v2\api\StudentProfileMoreActionApiController@saveSaHelpData']);

    Route::match(['get', 'post'], 'get-os-help-data', ['as' => 'get-os-help-data',                    'uses' => 'v2\api\StudentProfileMoreActionApiController@getOsHelpFormData']);
    Route::match(['get', 'post'], 'save-student-os-help-details', ['as' => 'save-student-os-help-details',        'uses' => 'v2\api\StudentProfileMoreActionApiController@saveOsHelpData']);

    Route::match(['get', 'post'], 'get-student-course-information-for-os-help', ['as' => 'get-student-course-information-for-os-help',        'uses' => 'v2\api\StudentProfileMoreActionApiController@getOShelpInformationFromCourseId']);
    Route::match(['get', 'post'], 'get-student-course-information-from-course-id', ['as' => 'get-student-course-information-from-course-id',     'uses' => 'v2\api\StudentProfileMoreActionApiController@getStudentCourseInformationFromCourseId']);
    Route::match(['get', 'post'], 'get-narrow-type-list-data', ['as' => 'get-narrow-type-list-data',           'uses' => 'v2\api\StudentProfileMoreActionApiController@getNarrowTypeList']);
    Route::match(['get', 'post'], 'get-sub-narrow-type-list-data', ['as' => 'get-sub-narrow-type-list-data',       'uses' => 'v2\api\StudentProfileMoreActionApiController@getSubNarrowTypeList']);

    // TCSI Credit Offer
    Route::match(['get', 'post'], 'get-student-tcsi-credit-offers', ['as' => 'get-student-tcsi-credit-offers',      'uses' => 'v2\api\StudentProfileMoreActionApiController@getTcsiCreditOfferData']);
    Route::match(['get', 'post'], 'get-tcsi-credit-offer-info', ['as' => 'get-tcsi-credit-offer-info',          'uses' => 'v2\api\StudentProfileMoreActionApiController@getTcsiCreditOfferInfo']);
    Route::match(['get', 'post'], 'save-tcsi-credit-offer-data', ['as' => 'save-tcsi-credit-offer-data',         'uses' => 'v2\api\StudentProfileMoreActionApiController@saveTcsiCreditOfferData']);
    Route::match(['get', 'post'], 'update-tcsi-credit-offer-data', ['as' => 'update-tcsi-credit-offer-data',       'uses' => 'v2\api\StudentProfileMoreActionApiController@updateTcsiCreditOfferData']);
    Route::match(['get', 'post'], 'delete-tcsi-credit-offer-data', ['as' => 'delete-tcsi-credit-offer-data',       'uses' => 'v2\api\StudentProfileMoreActionApiController@deleteTcsiCreditOfferData']);

    Route::match(['get', 'post'], 'upload-avetmiss-document', ['as' => 'upload-avetmiss-document',         'uses' => 'v2\api\AvetmissImportApiController@uploadAvetmissDocument']);
    Route::match(['get', 'post'], 'upload-timetable-file', ['as' => 'upload-timetable-file',         'uses' => 'v2\api\TimetableImportApiController@uploadTimetableFile']);
    // Not used routes
    // Route::match(['post'],          'update-student-details',           ['as' => 'update-student-details',                  'uses' => 'v2\api\StudentProfileCommonApiController@updateStudentDetails']);
    // Route::match(['get', 'post'],   'enrollment-fees',                  ['as' => 'enrollment-fees',                         'uses'  => 'v2\sadmin\CollegeEnrollmentFeesController@enrollmentFees']);
    // Route::match(['post'],          'get-study-reason-list',            ['as' => 'get-study-reason-list',                   'uses' => 'v2\api\CommonApiController@getStudyReasonList']);
    // Route::match(['get','post'],    'delete-addtional-service',         ['as' => 'delete-addtional-service',                'uses' => 'v2\api\StudentApiController@deleteAddtionalService']);
    // Route::match(['post'],          'get-student-details',              ['as' => 'get-student-details',                     'uses' => 'v2\api\StudentApiController@getStudentDetail']);
    // Route::match(['post'],          'upload-student-profile-pic',       ['as' => 'upload-student-profile-pic',              'uses' => 'v2\api\StudentApiController@uploadStudentProfilePic']);
    // Route::match(['post'],          'get-generate-certificate-list',    ['as' => 'get-generate-certificate-list',           'uses' => 'v2\api\StudentApiController@generateCertificateList']);

    // Student Portal
    Route::match(['get', 'post'], 'get-evaluation-data', ['as' => 'get-evaluation-data', 'uses' => 'Spa\Student\EvaluationController@ajaxAction']);

    // Agent
    Route::match(['get', 'post'], 'save-agent-application-data', ['as' => 'save-agent-application-data', 'uses' => 'v2\api\AgentApiController@saveAgentApplication']);
    Route::match(['get', 'post'], 'save-agent-application-documents', ['as' => 'save-agent-application-documents', 'uses' => 'v2\api\AgentApiController@saveAgentApplicationDocuments']);
    Route::match(['get', 'post'], 'save-agent-extra-documents', ['as' => 'save-agent-extra-documents', 'uses' => 'v2\api\AgentApiController@saveAgentApplicationExtraDocuments']);
    Route::match(['get', 'post'], 'remove-agent-application-documents', ['as' => 'remove-agent-application-documents', 'uses' => 'v2\api\AgentApiController@removeAgentApplicationDocument']);
    Route::match(['get', 'post'], 'remove-agent-application-extra-documents', ['as' => 'remove-agent-application-extra-documents', 'uses' => 'v2\api\AgentApiController@removeAgentApplicationExtraDocument']);
    Route::match(['get', 'post'], 'get-email-content', ['as' => 'get-email-content', 'uses' => 'v2\api\AgentApiController@getEmailContent']);
    Route::match(['get', 'post'], 'send-agency-mailing', ['as' => 'send-agency-mailing', 'uses' => 'v2\api\AgentApiController@sendAgencyMailing']);

});

// Agents Routes.
Route::group(['middleware' => ['auth:sanctum', 'mail', /* InitializeTenancyByDomain::class, */ PreventAccessFromCentralDomains::class]], function () {

    // Agent login API
    Route::match(['post'], 'agent-your-students', ['as' => 'agent-your-students',                     'uses' => 'v2\api\agent\AgentPortalApiController@getAgentStudentsData']);
    Route::match(['post'], 'agent-students-filterby-html', ['as' => 'agent-students-filterby-html',            'uses' => 'v2\api\agent\AgentPortalApiController@agentStudentsByFilter']);
    Route::match(['post'], 'agent-payment-history', ['as' => 'agent-payment-history-api',               'uses' => 'v2\api\agent\AgentPortalApiController@getAgentPaymentHistory']);
    Route::match(['post'], 'payment-history-filterby-html', ['as' => 'payment-history-filterby-html',           'uses' => 'v2\api\agent\AgentPortalApiController@paymentHistoryByFilter']);
    Route::match(['post'], 'agent-payment-advice', ['as' => 'agent-payment-advice-api',                'uses' => 'v2\api\agent\AgentPortalApiController@getAgentPaymentAdvice']);
    Route::match(['post'], 'payment-advice-filterby-html', ['as' => 'payment-advice-filterby-html',            'uses' => 'v2\api\agent\AgentPortalApiController@paymentAdviceByFilter']);
    Route::match(['post'], 'agent-commission-data', ['as' => 'agent-commission-data',                   'uses' => 'v2\api\agent\AgentPortalApiController@getAgentCommissionData']);

    // Student GTE process
    Route::match(['post'], 'agent-uploaded-offer-letter-data', ['as' => 'agent-uploaded-offer-letter-data',        'uses' => 'v2\api\StudentGteProcessApiController@uploadedOfferLetterData']);
    Route::match(['post'], 'agent-previous-offer-letter-data', ['as' => 'agent-previous-offer-letter-data',        'uses' => 'v2\api\StudentGteProcessApiController@previousOfferLetterData']);
    Route::match(['post'], 'agent-uploaded-gte-documents-data', ['as' => 'agent-uploaded-gte-documents-data',       'uses' => 'v2\api\StudentGteProcessApiController@uploadedGteDocumentsData']);
    Route::match(['post'], 'agent-previous-gte-documents-data', ['as' => 'agent-previous-gte-documents-data',       'uses' => 'v2\api\StudentGteProcessApiController@previousGteDocumentsData']);
    Route::match(['post'], 'agent-uploaded-gte-payment-data', ['as' => 'agent-uploaded-gte-payment-data',         'uses' => 'v2\api\StudentGteProcessApiController@uploadedPaymentData']);
    Route::match(['post'], 'agent-previous-gte-payment-data', ['as' => 'agent-previous-gte-payment-data',         'uses' => 'v2\api\StudentGteProcessApiController@previousPaymentData']);
    Route::match(['post'], 'agent-uploaded-gte-enrollment-data', ['as' => 'agent-uploaded-gte-enrollment-data',      'uses' => 'v2\api\StudentGteProcessApiController@uploadedConfirmationEnrollData']);
    Route::match(['post'], 'agent-previous-gte-enrollment-data', ['as' => 'agent-previous-gte-enrollment-data',      'uses' => 'v2\api\StudentGteProcessApiController@previousConfirmationEnrollData']);
    Route::match(['post'], 'agent-uploaded-gte-visa-data', ['as' => 'agent-uploaded-gte-visa-data',            'uses' => 'v2\api\StudentGteProcessApiController@uploadedVisaData']);
    Route::match(['post'], 'agent-previous-gte-visa-data', ['as' => 'agent-previous-gte-visa-data',            'uses' => 'v2\api\StudentGteProcessApiController@previousVisaData']);
    Route::match(['post'], 'agent-upload-signed-offer-letter', ['as' => 'agent-upload-signed-offer-letter',        'uses' => 'v2\api\StudentGteProcessApiController@uploadSignedOfferLetter']);
    Route::match(['post'], 'agent-upload-gte-document', ['as' => 'agent-upload-gte-document',               'uses' => 'v2\api\StudentGteProcessApiController@uploadGtedocument']);
    Route::match(['post'], 'agent-upload-payment-document', ['as' => 'agent-upload-payment-document',           'uses' => 'v2\api\StudentGteProcessApiController@uploadPaymentDocument']);
    Route::match(['post'], 'agent-upload-confirmation-enrollment', ['as' => 'agent-upload-confirmation-enrollment',    'uses' => 'v2\api\StudentGteProcessApiController@uploadConfirmationEnrollment']);
    Route::match(['post'], 'agent-upload-visa-document', ['as' => 'agent-upload-visa-document',              'uses' => 'v2\api\StudentGteProcessApiController@uploadVisaDocument']);
    Route::match(['post'], 'agent-get-gte-documents-list', ['as' => 'agent-get-gte-documents-list',            'uses' => 'v2\api\StudentGteProcessApiController@getGtedocumentList']);
    Route::match(['post'], 'update-current-visa-status', ['as' => 'update-current-visa-status',              'uses' => 'v2\api\StudentGteProcessApiController@updateCurrentVisaStatus']);
    Route::match(['post'], 'agent-visa-stepper-data', ['as' => 'agent-visa-stepper-data',                 'uses' => 'v2\api\StudentGteProcessApiController@getVisaStepperData']);
    Route::match(['post'], 'agent-save-gte-comments', ['as' => 'agent-save-gte-comments',                 'uses' => 'v2\api\StudentGteProcessApiController@saveGteComments']);
    Route::match(['post'], 'agent-get-section-wise-comments', ['as' => 'agent-get-section-wise-comments',         'uses' => 'v2\api\StudentGteProcessApiController@getSectionWiseComments']);
    Route::match(['post'], 'agent-remove-gte-document', ['as' => 'agent-remove-gte-document',               'uses' => 'v2\api\StudentGteProcessApiController@removeGteDocument']);

    // New file manager document
    Route::match(['post'], 'get-agent-document-data', ['as' => 'get-agent-document-data',                 'uses' => 'v2\api\agent\AgentPortalApiController@getAgentDocumentData']);
    Route::match(['post'], 'add-agent-document-data', ['as' => 'add-agent-document-data',                 'uses' => 'v2\api\agent\AgentPortalApiController@addAgentDocumentDirectory']);
    Route::match(['post'], 'update-agent-document-data', ['as' => 'update-agent-document-data',              'uses' => 'v2\api\agent\AgentPortalApiController@updateAgentDocumentDirectory']);
    Route::match(['post'], 'remove-agent-document-data', ['as' => 'remove-agent-document-data',              'uses' => 'v2\api\agent\AgentPortalApiController@removeAgentDocumentDirectory']);

    Route::match(['get', 'post'], 'upload-agent-documents/{userId}/{collegeId}/{agentId}', ['as' => 'upload-agent-documents',     'uses' => 'v2\api\agent\AgentPortalApiController@uploadAgentDocuments']);

    Route::match(['post'], 'get-student-address-history', ['as' => 'get-student-address-history',             'uses' => 'v2\api\StudentApiController@getStudentAddressHistory']);

});

/*
Routes for api general actions
*/
require base_path(path: 'routes/v3/general_api.php');
/*
Routes for student import api
*/
require base_path(path: 'routes/v3/student_api.php');
/*
Routes for courses api
*/
require base_path(path: 'routes/v3/courses_api.php');
/*
Routes for Agents api
*/
require base_path(path: 'routes/v3/agent_api.php');
