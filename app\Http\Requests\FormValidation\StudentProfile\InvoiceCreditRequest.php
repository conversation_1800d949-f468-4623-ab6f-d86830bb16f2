<?php

namespace App\Http\Requests\FormValidation\StudentProfile;

use Support\Traits\CommonTrait;
use App\Traits\ResponseTrait;
use Illuminate\Foundation\Http\FormRequest;
class InvoiceCreditRequest extends FormRequest 
{
    use CommonTrait;
    use ResponseTrait;
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'credit_amount'  => 'required|numeric',
            'credit_date'    => 'required|date',
            'remarks'        => 'required', 
        ];
    }

    public function withValidator($validator)
    {
        if (!$validator->fails()) {
            $this->merge([
                'credit_date' => $this->convertDateFormat($this->input('credit_date')),
            ]);
        }
    }
}
