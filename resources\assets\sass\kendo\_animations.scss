.k-animation-container:has(
        > .k-menu-popup.tw-popup,
        > .popup-content.tw-popup,
        > .k-filter-menu.k-popup
    ) {
    animation: popupAnimation 0.15s cubic-bezier(0.4, 0, 0.2, 1);
    transform-origin: top left;
}

.k-animation-container:has(
        > .k-menu-popup.tw-popup.tw-popup--top-right,
        > .popup-content.tw-popup.tw-popup--top-right,
        > .k-tooltip.k-popup.tw-popup--top-right
    ) {
    animation: popupAnimation 0.15s cubic-bezier(0.4, 0, 0.2, 1);
    transform-origin: top right;
}

.k-animation-container:has(> .k-list-container) {
    min-width: 10rem !important;
    animation: popupAnimation 0.15s cubic-bezier(0.4, 0, 0.2, 1);
    transform-origin: top left;
    // margin-top: 0.25rem;
}

.k-animation-container:has(> .k-list-container.tw-width-auto) {
    width: auto !important;
    max-width: 18rem !important;
    min-width: auto !important;
}

.k-animation-container:has(> .k-list-container.tw-fixed-width) {
    width: 18rem !important;
}

.k-animation-container:has(> .k-list-container.tw-full-width) {
    .k-list.k-list-md
        .k-list-content
        .k-list-ul
        .k-list-item.k-selected::after {
        display: none;
    }
}

.k-animation-container:has(> .k-popup.tw-popover__timetable-calendar) {
    left: unset !important;
    right: 2rem !important;
    margin-top: 0.25rem;
}

.tw-popup {
    animation: popupAnimation 0.15s cubic-bezier(0.4, 0, 0.2, 1);
    transform-origin: top left;
    &--tr {
        transform-origin: top right;
    }
}

.tw-slidein-from-right {
    animation: slideFromRight 450ms ease-in-out;
}

.k-animation-container:has(> .k-list-container.tw-width-auto.tw-preset-picker) {
    width: 22.5rem !important;
    max-width: 25rem !important;
    min-width: 22.5rem !important;
}

.k-animation-container:has(> .k-list-container.tw-width-auto.tw-preset-picker) {
    width: auto !important;
    max-width: 25rem !important;
    min-width: auto !important;
}
