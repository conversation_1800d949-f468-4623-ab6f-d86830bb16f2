html {
    scroll-behavior: smooth;
}

html:focus-within {
    scroll-behavior: smooth;
}

.editStudentModal-wrap,
.enrollNewCourseModal-wrap,
.enrollEditCourseModal-wrap,
.offerServiceAddedInfo-wrap {
    scroll-behavior: smooth;
    height: 100vh;
    overflow-y: scroll;
}

.offerServiceAddedInfo-wrap {
    height: calc(100vh - 124px);
    /* Reduce the top title and tabs height */
}

span.k-loading-text {
    text-indent: 0;
    top: 50%;
    left: calc(50% - 1rem);
    z-index: 9999;
}

.right-bar li a.active,
.enroll-course-bar li a.active,
.edit-enroll-course-bar li a.active,
.oshc-bar li a.active {
    /* border-width: 0px 0 0px 4px !important; */
    /* border-left-color: #1890ff !important; */
    position: relative;
}

.right-bar li a.active::after,
.enroll-course-bar li a.active::after,
.edit-enroll-course-bar li a.active::after,
.oshc-bar li a.active::after {
    border-width: 0px 0 0px 4px !important;
    border-left-color: #1890ff !important;
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    width: 4px;
}

.right-bar li a.active p,
.enroll-course-bar li a.active p,
.edit-enroll-course-bar li a.active p,
.oshc-bar li a.active p {
    color: #1890ff !important;
}

table {
    /* font-family: arial, sans-serif; */
    border-collapse: collapse;
    width: 100%;
}

td,
th {
    color: grey;
    text-align: left;
    padding: 8px;
    /* font-weight: 200; */
}

.sortable.open .dropdown-menu.dm-toggle {
    top: inherit !important;
    left: inherit !important;
    display: block;
}

.k-notification {
    border-radius: 8px !important;
    padding: 0px 0px !important;
    border-width: 0px !important;
}

#sendSmsModal,
#sendEmailModal,
#viewStudentCoursesModal,
#editStudentModal,
#addNoteModal,
#enrollSubjectsModal,
#trainingPlanAddModel,
#editTrainingPlanModel,
#addUnitSubjectModal,
#studentCourseStatusUpdateModal,
#studentCourseStatusHistoryModal,
#enrollNewCourseModal,
#generateCertificateModal,
#trainingPlanViewModel,
#enrollEditCourseModal,
#offerServiceAddedInfo,
#addCertificateModal,
#studentTrainingPlanModal,
#studentDocumentListModal,
#viewFeeScheduleModal,
#viewSchedulePaymentInfoModal,
#recordPaymentModal,
#manageRefundHistoryModal,
#studentScholarshipModal,
#addScholarshipInfoModal,
#manageAgentCommissionModal,
#studentServicesInfoModal,
#miscellaneousPaymentModel,
#generatePaymentScheduleModal,
#addNewPaymentScheduleModal,
#studentUpfrontFeeModal,
#editSchedulePaymentInfoModal,
#modifyAgentCommissionModal,
#paymentStatementModal,
#transferPaymentsModal,
#paymentAddNewServicesModal,
#editScholarshipInfoModal,
#addInvoiceCreditModal,
#editPaymentHistoryModal,
#deletePaymentTransactionModal,
#feeRefundDetailModal,
#paymentTransactionReverseModal,
#editRefundHistoryModal,
#miscellaneousSendEmailModal,
#editAdditionalServiceInformationModal,
#refundPaymentAdditionalServiceInformationModal,
#reverseTransactionAdditionalPaymentServiceModal,
#addMiscellaneousPaymentRequestModal,
#refundPaymentModal,
#reverseTransactionModal,
#editTransactionModal,
#addOfferCommunicationModal,
#sendSmsModal,
#sendEmailModal,
#viewStudentCoursesModal,
#editStudentModal,
#addNoteModal,
#enrollSubjectsModal,
#trainingPlanAddModel,
#editTrainingPlanModel,
#offerCommunicationModal,
#addUnitSubjectModal,
#studentCourseExtendModal,
#studentCourseStatusHistoryModal,
#enrollNewCourseModal,
#generateCertificateModal,
#trainingPlanViewModel,
#enrollEditCourseModal,
#offerServiceAddedInfo,
#addCertificateModal,
#studentTrainingPlanModal,
#studentDocumentListModal,
#viewFeeScheduleModal,
#viewSchedulePaymentInfoModal,
#recordPaymentModal,
#manageRefundHistoryModal,
#studentScholarshipModal,
#addScholarshipInfoModal,
#manageAgentCommissionModal,
#studentServicesInfoModal,
#miscellaneousPaymentModel,
#generatePaymentScheduleModal,
#addNewPaymentScheduleModal,
#studentUpfrontFeeModal,
#editSchedulePaymentInfoModal,
#modifyAgentCommissionModal,
#paymentStatementModal,
#transferPaymentsModal,
#paymentAddNewServicesModal,
#editScholarshipInfoModal,
#addInvoiceCreditModal,
#editPaymentHistoryModal,
#deletePaymentTransactionModal,
#feeRefundDetailModal,
#paymentTransactionReverseModal,
#editRefundHistoryModal,
#miscellaneousSendEmailModal,
#editAdditionalServiceInformationModal,
#refundPaymentAdditionalServiceInformationModal,
#refundPaymentModal,
#reverseTransactionAdditionalPaymentServiceModal,
#addMiscellaneousPaymentRequestModal,
#reverseTransactionModal,
#editTransactionModal,
#addOfferCommunicationModal,
#recordMiscTransPaymentModal,
#recordServicePaymentModal,
#viewPaymentInstallmentSummaryTabModal,
#reCreatePaymentScheduleModal,
#addCOENumberModal,
#resultUnitDetailsModal,
#assignVpmsProviderModal,
#editVpmsProviderModal,
#addPlacementProviderModal,
#viewAgentCommissionInfoModal,
#viewMiscellaneousPaymentInfoModal,
#viewStudentServiceInfoModal,
#reportToTCSIModal,
#createFeeScheduleModalId,
#viewStudentScholarshipInfoModal,
#approveAgentCommissionModal,
#assignBatchModal,
#unitOutComeModal,
#unitAvetmissModal,
#bulkApproveAgentCommissionModal,
#bulkDeleteItemModal,
#updateHigherEdUnitOutComeModal {
    padding: 0px;
}

.k-widget * {
    box-sizing: border-box !important;
}

.k-panelbar {
    border-color: transparent !important;
    color: inherit !important;
    background-color: transparent !important;
}

.k-panelbar > .k-item > .k-link,
.k-panelbar > .k-panelbar-header > .k-link {
    color: #bbbffc !important;
    background-color: transparent !important;
    padding: 8px 0px !important;
    cursor: pointer;
}

.k-panelbar > .k-item > .k-link.k-state-focus,
.k-panelbar > .k-item > .k-link.k-state-focused,
.k-panelbar > .k-item > .k-link:focus,
.k-panelbar > .k-panelbar-header > .k-link.k-state-focus,
.k-panelbar > .k-panelbar-header > .k-link.k-state-focused,
.k-panelbar > .k-panelbar-header > .k-link:focus {
    box-shadow: none;
}

#newAdditionalService span.k-widget.k-textarea,
#trainingPlanAddForm span.k-widget.k-textarea,
#editTrainingPlanForm span.k-widget.k-textarea {
    width: 100% !important;
}

/* sms template */
#sendSmsModal textarea,
#sendSmsModal textarea:focus {
    border-color: transparent;
}

/* tabstrip */

#invisible .k-tabstrip {
    background-color: white;
    padding: 0px;
}

#invisible > .gradientbackground {
    padding-bottom: 1px;
}

#invisible > .gradientbackground ~ .k-tabstrip .k-tabstrip-items-wrapper {
    margin-top: -1px;
}

.k-tabstrip-item {
    background: #fff;
}

#resultList > th:nth-child(1) {
    width: 0px;
}

#resultList > td:nth-child(1) {
    width: 0px;
}

.k-grid .k-hierarchy-col {
    width: 0px;
}

.act {
    border-width: 0px 0 0px 4px !important;
    border-left-color: #1890ff !important;
}

.act p {
    color: #1890ff !important;
}

/* .k-tabstrip-items-wrapper .k-item span {
    font-style: normal;
    font-weight: 500;
    font-size: 0.875rem;
    line-height: 24px;
    color: #6B7280
} */

/* tabStrip */
.k-tabstrip-top > .k-tabstrip-items-wrapper .gradientbackground .k-item {
    background: transparent;
}

.k-tabstrip-items-wrapper .gradientbackground .k-item span {
    color: white;
}

.k-tabstrip-items-wrapper .resultViewTabStrip .k-item.k-state-active span {
    color: #1890ff;
}

.k-tabstrip-top > .k-tabstrip-items-wrapper .gradientbackground .k-item {
    border-bottom: 0;
}

.k-tabstrip-top > .k-tabstrip-items-wrapper .gradientbackground .k-item.k-state-active {
    position: relative;
    margin-bottom: 0;
    border-bottom: 0;
}

.k-tabstrip-top > .k-tabstrip-items-wrapper .gradientbackground .k-item {
    /* border-bottom: 4px; */
}

.k-tabstrip-top > .k-tabstrip-items-wrapper .gradientbackground .k-item.k-state-active .k-complete {
    position: absolute;
    bottom: 1px;
    left: 0;
    right: 0;
    top: unset;
    width: 100%;
    height: 4px;
    background-color: white;
    z-index: 1;
    display: block;
}

.k-tabstrip-top > .k-tabstrip-items-wrapper .gradientbackground .k-item.k-state-active {
    border-color: white;
}

/* .k-tabstrip-top
    > .k-tabstrip-items-wrapper
    .resultViewTabStrip
    .k-item.k-state-active
    .k-link {
    border-bottom: 3px solid #1890ff;
} */

#tabStrip .k-tabstrip-items .k-link {
    padding: 0px;
}

#tabStrip .k-tabstrip-items,
#tabStrip ul.gradientbackground {
    padding: 0;
}

#resultViewTabStrip .resultViewTabStrip {
    width: 100%;
    padding: 0;
    border-bottom: 1px solid #e5e7eb;
}

/* Activity Tabstrip */
#activityTabStrip .k-tabstrip-items .k-link,
#activityTabStripActivityLogTab .k-tabstrip-items .k-link {
    padding: 8px 7px;
}

#activityTabStrip .k-tabstrip-items,
#activityTabStripActivityLogTab .k-tabstrip-items {
    padding: 0px 0px 0px 0px;
}

#activityTabStrip .k-tabstrip-items-wrapper .k-item.k-state-active span,
#activityTabStripActivityLogTab .k-tabstrip-items-wrapper .k-item.k-state-active span {
    color: #0050b3;
}

#activityTabStrip .k-tabstrip-items-wrapper .k-item.k-state-active,
#activityTabStripActivityLogTab .k-tabstrip-items-wrapper .k-item.k-state-active {
    background: #bae7ff;
    border-radius: 0.5rem;
}

/*stepper */

.k-stepper .k-step-done .k-step-indicator:hover,
.k-stepper .k-step-current .k-step-indicator:hover,
.k-stepper .k-step-done .k-step-indicator,
.k-stepper .k-step-current .k-step-indicator,
.k-progressbar .k-state-selected {
    border-color: #1890ff;
    color: #fff;
    background-color: #1890ff;
}

.k-step-list-horizontal .k-step-indicator + .k-step-label {
    color: #6b7280;
}

/* the initial position of the next div */
.k-fx-swap.k-fx-start .k-fx-next {
    -webkit-transform: translatex(100%);
    -moz-transform: translatex(100%);
    -ms-transform: translatex(100%);
    transform: translatex(100%);
}

/* the initial position of the next div */
.k-fx-swap.k-fx-end .k-fx-current {
    opacity: 0;
    -webkit-transform: scale(0.9);
    -moz-transform: scale(0.9);
    transform: scale(0.9);
}

/* the initial position of the next div */
.k-fx-swap.k-fx-reverse.k-fx-start .k-fx-next {
    opacity: 0;
    -webkit-transform: scale(0.9);
    -moz-transform: scale(0.9);
    transform: scale(0.9);
}

/* the initial position of the next div */
.k-fx-swap.k-fx-reverse.k-fx-end .k-fx-current {
    opacity: 1;
    -webkit-transform: translatex(100%);
    -moz-transform: translatex(100%);
    transform: translatex(100%);
}

/* ============   NEW CHANGE 09/07/22 =======*/

.k-step-current:hover .k-step-indicator {
    background-color: #1890ff !important;
    background: #1890ff !important;
}

.k-stepper .k-step-done.k-step-hover .k-step-indicator,
.k-stepper .k-step-done:hover .k-step-indicator {
    background-color: #1890ff;
}

.course-list .k-widget.k-dropdown {
    width: 100%;
}

.course-list .k-widget.k-dropdown .k-select {
    display: flex;
    align-items: center;
    justify-content: center;
}

.course-list .k-widget.k-dropdown .k-input {
    color: #374151;
    /* font-weight: 500; */
}

.k-progressbar .k-state-selected {
    border-color: #1890ff !important;
    color: #fff;
    background-color: #1890ff !important;
}

.k-stepper .k-step-done .k-step-indicator {
    border-color: #1890ff !important;
    color: #fff;
    background-color: #1890ff !important;
}

.k-stepper .k-step-current .k-step-indicator {
    border-color: #1890ff;
    color: #fff;
    background-color: #1890ff;
}

.k-tabstrip-content.k-state-focused,
.k-tabstrip-content:focus,
.k-tabstrip > .k-content.k-state-focused,
.k-tabstrip > .k-content:focus {
    outline: none;
}

/* activity timeline  */

.k-timeline-vertical .k-timeline-card .k-card {
    overflow-y: auto;
    max-height: 200px;
}

.k-timeline .k-timeline-circle,
.k-timeline .k-timeline-flag {
    background-color: #1890ff;
}

.k-timeline .k-timeline-date {
    font-weight: 500;
    line-height: 1;
    font-size: 1rem;
    color: #111827;
}

.k-timeline-vertical {
    padding-left: 175px;
}

/* DropDown  */
/* DatePicker Edit Vpms Modal */

#editVpmsProviderModal .k-list-optionlabel.k-state-selected {
    display: none;
}

#editVpmsProviderModal .k-popup .k-list .k-state-hover {
    background-color: #5ca5ff !important;
    cursor: pointer !important;
    color: white !important;
}

#editVpmsProviderModal .k-dropdown-wrap .k-select {
    line-height: 35px !important;
    width: 35px !important;
}

#editVpmsProviderModal .k-widget .k-invalid-msg {
    display: none;
}

#editVpmsProviderModal .k-form-field-wrap .k-datepicker {
    background: #ffffff;
    border: 1px solid #d1d5db;
    box-shadow: 0px 1px 2px rgb(0 0 0 / 5%);
    border-radius: 0.5rem;
    width: 100%;
}

#editVpmsProviderModal .k-picker-wrap .k-input[type='text'] {
    border-radius: 0.5rem;
    height: 36px !important;
}

#editVpmsProviderModal .k-widget.k-dropdown .k-dropdown-wrap.k-invalid {
    border: 1px solid red;
}

.k-form .k-form-field .k-label.k-form-label {
    color: #374151;
    font-size: 14px;
    font-weight: 500;
    margin-bottom: 5px;
}

/* DropDown  */

#assignVpmsProviderModal .k-widget.k-dropdown .k-dropdown-wrap,
.accordion-content .k-widget.k-dropdown .k-dropdown-wrap,
.accordion-contentv2 .k-widget.k-dropdown .k-dropdown-wrap,
#resultUnitList .k-widget.k-dropdown .k-dropdown-wrap {
    padding: 5px 0px;
    line-height: 24px !important;
    background: #ffffff;
    border: 1px solid #d1d5db;
    box-shadow: 0px 1px 2px rgb(0 0 0 / 5%);
    border-radius: 0.5rem;
    width: 100%;
}

#assignVpmsProviderModal .k-list-optionlabel.k-state-selected {
    display: none;
}

#assignVpmsProviderModal .k-popup .k-list .k-state-hover {
    background-color: #5ca5ff !important;
    cursor: pointer !important;
    color: white !important;
}

#assignVpmsProviderModal .k-dropdown-wrap .k-select {
    line-height: 35px !important;
    width: 35px !important;
}

#assignVpmsProviderModal .k-widget .k-invalid-msg {
    display: none;
}

/* DatePicker */

#assignVpmsProviderModal .k-form-field-wrap .k-datepicker {
    background: #ffffff;
    border: 1px solid #d1d5db;
    box-shadow: 0px 1px 2px rgb(0 0 0 / 5%);
    border-radius: 0.5rem;
    width: 100%;
}

#assignVpmsProviderModal .k-picker-wrap .k-input[type='text'] {
    border-radius: 0.5rem;
    height: 36px !important;
}

#assignVpmsProviderModal .k-widget.k-dropdown .k-dropdown-wrap.k-invalid {
    border: 1px solid red;
}

#assignVpmsProviderModal .k-form-layout {
    overflow-y: auto !important;
    max-height: 400px !important;
}

#addPlacementProviderModal .k-textbox,
.k-numerictextbox {
    border-width: 1px;
    border-radius: 0.5rem;
    height: 2.25rem;
    --tw-border-opacity: 1;
    border-color: rgb(209 213 219 / var(--tw-border-opacity));
    box-shadow:
        var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

#addPlacementProviderModal [type='text']:focus,
[type='password']:focus {
    border-color: #2ea2f8 !important;
    box-shadow: none;
    color: #354052 !important;
    outline: 0;
    border-width: 1px;
}

#assignVpmsProviderModal .italicAddress,
#editVpmsProviderModal .italicAddress {
    font-style: italic;
    /* border: none; */
}

#assignVpmsProviderModal .k-state-disabled,
#editVpmsProviderModal .k-state-disabled {
    color: #374151 !important;
    /* opacity: 1; */
}

.k-dialog .k-dialog-titlebar {
    background: #1890ff !important;
}

.k-invalid {
    border: 1px solid red !important;
    border-radius: 8px;
}

/*.k-form-error.k-invalid-msg {*/
/*    display: none;*/
/*}*/
/* drop down Search box */
.k-list-filter > .k-textbox {
    border-radius: 5px !important;
    width: 100% !important;
}

.titlebar-sms-modal .k-window-action {
    opacity: 1;
}

.titlebar-sms-modal .k-window-action .k-i-close {
    color: white;
    font-size: 20px;
    opacity: 1 !important;
}

.k-form-field {
    margin-bottom: 0px;
}

/* .k-fieldselector .k-list .k-item,
.k-list-optionlabel.k-state-focused,
.k-list-optionlabel.k-state-selected,
.k-popup .k-list .k-state-focused,
.k-popup .k-list .k-state-selected,
.k-action-buttons .k-primary {
    background-color: #1890ff;
    cursor: pointer;
    color: white;
    border-radius: 4px;
} */

.k-popup .k-list .k-item {
    line-height: 1.4 !important;
    padding: 0.25rem;
    min-height: 1.8em;
}

.cusInput:focus,
.k-form-field-wrap .k-textbox:focus {
    border: 1px solid #1890ff;
    box-shadow:
        0px -2px 2px 2px rgba(24, 144, 255, 0.1),
        0px 2px 2px 2px rgba(24, 144, 255, 0.1);
}

/* .k-dropdown-wrap.k-state-focused , .k-dropdown-wrap.k-state-hover{
    border: 1px solid #1890FF !important;
} */
.advanceTimetable.active {
    background: #1890ff;
    border-color: #1890ff;
}

.advanceTimetable.active p {
    color: #ffff;
}

#customDatePickerCalendar .k-datepicker,
#customDatePickerRoom .k-datepicker,
#customDatePickerTeacher .k-datepicker {
    border-radius: 0px !important;
    width: 100%;
    box-shadow: none !important;
    caret-color: transparent;
}

.k-i-arrow-60-right::before {
    content: close-quote !important;
    background-image: url(../../img/right-side-calendar-arrow-1.svg);
    background-position: center;
    background-repeat: no-repeat;
}

.k-i-arrow-60-left::before {
    content: close-quote !important;
    background-image: url(../../img/left-side-calendar-arrow-1.svg);
    background-position: center;
    background-repeat: no-repeat;
}

.k-icon.sync-icon::before {
    content: close-quote !important;
    background-image: url(../../img/arrow-sync.svg);
    background-repeat: no-repeat;
}

.k-icon.fail-sync-icon::before {
    content: close-quote !important;
    background-image: url(../../img/arrow-fail-sync.svg);
    background-repeat: no-repeat;
}

.k-icon.not-sync-icon::before {
    content: close-quote !important;
    background-image: url(../../img/arrow-not-sync.svg);
    background-repeat: no-repeat;
}

.k-icon.k-i-calendar {
    margin-top: 4px;
    font-size: 1rem !important;
}

.k-toolbar .k-icon.k-i-calendar {
    margin-top: 0;
}

#studentsFilterPanelbar .k-widget.k-dropdown .k-dropdown-wrap {
    padding: 3px 2px;
    line-height: 24px !important;
    background: #ffffff;
    border: 1px solid #d1d5db;
    box-shadow: 0px 1px 2px rgb(0 0 0 / 5%);
    border-radius: 0.5rem;
    width: 100%;
}

#studentsFilterPanelbar .k-dropdown,
#editStudentModal .k-dropdown,
#addUnitSubjectModal .k-dropdown,
#newAdditionalService .k-dropdown,
#studentCourseStatusUpdateModal .k-dropdown,
#enrollNewCourseModal .k-dropdown,
#enrollEditCourseModal .k-dropdown,
#trainingPlanAddModel .k-dropdown,
#OSHCServices .k-dropdown,
#studentUpfrontFeeModal .k-dropdown,
#editTrainingPlanModel .k-dropdown,
#generateCertificateModal .k-dropdown,
#recordPaymenthtml .k-dropdown,
#addNewPaymentScheduleModal .k-widget.k-dropdown,
#modifyAgentCommissionModal .k-widget.k-dropdown,
#feeRefundDetailModal .k-widget.k-dropdown,
#editRefundHistoryModal .k-widget.k-dropdown,
#offerServiceAddedInfo .k-widget.k-dropdown {
    width: 100%;
}

.k-select > .k-i-arrow-60-down::before {
    content: close-quote !important;
    background-image: url('../../img/arrow-down.svg');
    background-position: center;
    background-repeat: no-repeat;
    /* margin-top: 5px !important; */
    transition: all 300ms ease-in-out;
}

span.k-state-active .k-i-arrow-60-down::before,
button.accordion.is-open .k-i-arrow-60-down::before,
button.accordionResultForTab.is-open .k-i-arrow-60-down::before,
button.accordionDetail.is-open .k-i-arrow-60-down::before {
    /* transform: rotate(180deg);
    -webkit-transform: rotate(180deg);
    -moz-transform: rotate(180deg);
    -ms-transform: rotate(180deg);
    -o-transform: rotate(180deg);
    transform: rotate(180deg); */
    /* background-image: url("../../img/arrow-down.svg"); */
    /* background-position: center;
    background-repeat: no-repeat; */
}

.upload-wrapper .k-upload {
    display: none;
}

/* [type='radio'] {
    color: #1890FF !important;
} */

/* stepper R & D */
.course_list .k-i-check {
    color: #fff !important;
}

.k-popup .k-list .k-state-hover .k-i-check {
    color: #e5e7eb !important;
}

/* activity timeline css */

.container {
    cursor: pointer;
    max-width: 500px;
}

.containerActivityTab {
    cursor: pointer;
}

.timeline {
    position: relative;
    padding-left: 4rem;
    margin: 0 0 0 30px;
    color: gray;
}

.timeline-container {
    position: relative;
    margin-bottom: 20px;
}

.timeline-icon {
    position: absolute;
    left: -80px;
    text-align: center;
    font-size: 2rem;
}

.timeline-body {
    margin-left: -40px !important;
}

.timeline:before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    width: 2px;
    height: 100%;
    background: #e5e7eb;
}

.gradientbackground {
    background: linear-gradient(270deg, #06b6d4 20.35%, #1e93ff 75.64%);
    /* margin-bottom: -3px; */
}

.k-tabstrip-content.k-content.k-state-active {
    padding: 0px;
}

#loom-companion-mv3 {
    display: none;
}

.ck-rounded-corners .ck.ck-editor__top .ck-sticky-panel .ck-toolbar {
    background-color: rgba(249, 250, 251, 1) !important;
    border-top-right-radius: 6px !important;
    border-top-left-radius: 6px !important;
    border-bottom: 1px solid rgba(229, 231, 235, 1) !important;
}

.ck.ck-toolbar {
    border: 1px solid #f8fafc !important;
}

.ck.ck-reset_all * {
    color: #6b7280 !important;
}

.ck.ck-editor__main > .ck-editor__editable:not(.ck-focused) {
    border: 1px solid #f8fafc !important;
}

.ck.ck-editor__editable:not(.ck-editor__nested-editable).ck-focused {
    border: 1px solid #f8fafc !important;
    box-shadow: none !important;
}

.noteActionClass .opacity-0,
.courseHoverAction .opacity-0 {
    opacity: 1 !important;
}

#summaryTimeline.k-timeline-horizontal,
#courseTimeline.k-timeline-horizontal,
#expandedCourseTimeline.k-timeline-horizontal {
    padding: 0px !important;
}

#summaryTimeline .k-timeline-events-list,
#courseTimeline .k-timeline-events-list,
#expandedCourseTimeline .k-timeline-events-list {
    height: 124px !important;
    overflow-y: hidden !important;
}

#enrollNewCourseModal .k-switch-on .k-switch-track {
    border-color: #1274ac;
    color: white;
    background-color: #1274ac;
}

#recentdocul,
#quickNoteTab .container,
#allActivityLog .container,
.overflow-y-auto {
    -ms-overflow-style: none;
    /* Internet Explorer 10+ */
    scrollbar-width: none;
    /* Firefox */
}

#recentdocul::-webkit-scrollbar {
    display: none;
    /* Safari and Chrome */
}

/* Accordion styles */

.accordion-content {
    /* max-height: 0;
    overflow: hidden;
    transition: max-height 0.2s ease-in-out; */
    display: none;
}

.accordion-contentv2 {
    /* max-height: 0;
    overflow: hidden;
    transition: max-height 0.2s ease-in-out; */
    display: none;
}

.k-timeline-horizontal .k-timeline-flag::after {
    background-color: #1890ff !important;
}

.timeLineFont {
    font-family: 'Rubik';
    font-style: normal;
    font-weight: 400;
    font-size: 12px;
    line-height: 16px;
    color: #6b7280;
}

/* Chart */

.circular-chart {
    display: block;
    margin: -7px auto;
    max-width: 100%;
    max-height: 100px;
}

.circle-bg {
    fill: none;
    stroke: #eee;
    stroke-width: 3.8;
}

.circle {
    fill: none;
    stroke-width: 3.8;
    stroke-linecap: round;
    animation: progress 1s ease-out forwards;
}

@keyframes progress {
    0% {
        stroke-dasharray: 0 100;
    }
}

.circular-chart.orange .circle {
    stroke: #ff9f00;
}

.circular-chart.green .circle {
    stroke: #4cc790;
}

.circular-chart.blue .circle {
    stroke: #3c9ee5;
}

.percentage {
    fill: #666;
    font-family: sans-serif;
    font-size: 0.5em;
    text-anchor: middle;
}

/*enroll course switch css*/
.customSwitchButton span.k-switch-handle {
    background-color: white;
}

.customSwitchButton span.k-switch.k-switch-on {
    background-color: #1890ff;
}

.customSwitchButton span.k-switch-danger.k-switch-on {
    background-color: red !important;
    border-color: red !important;
}

.customSwitchButton .k-switch-container {
    border: 2px;
}

.customSwitchButton span.k-switch.k-switch-off {
    background-color: #e5e7eb;
}

.customSwitchButton span.k-switch-label-on,
span.k-switch-label-off {
    display: none;
}

.action-div {
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
}

.accordion-content .k-button-icontext,
.accordion-contentv2 .k-button-icontext,
#resultUnitList .k-button-icontext,
#resultTabUnitList .k-button-icontext {
    font-size: 0px !important;
}

.text-xxs {
    font-size: 10px !important;
    letter-spacing: 1px !important;
}

#sendMailStudentModal,
#sendSmsStudentModal {
    padding: 0px !important;
}

#addUnitSubjectModal .k-listbox {
    width: 100% !important;
    height: 100% !important;
}

#addUnitSubjectModal .k-listbox .k-item {
    background-color: none !important;
    padding: 0 !important;
    border: none !important;
}

#addUnitSubjectModal .k-listbox-toolbar-right .k-list-scroller {
    overflow-y: auto !important;
    max-height: 254px !important;
    border-radius: 0.5rem !important;
}

#addUnitSubjectModal .selectedContent .k-list-scroller {
    overflow-y: auto !important;
    max-height: 305px !important;
    border-radius: 0.5rem !important;
}

#addUnitSubjectModal .k-listbox .k-item.k-state-selected .cursor-pointer {
    background-color: var(--color-gray-200) !important;
}

/* #addUnitSubjectModal .k-listbox .k-item.k-state-selected p {
    color: white !important;
} */

/* #addUnitSubjectModal .k-listbox-toolbar {
    position: absolute;
    margin: -112px 43px;
} */

#addUnitSubjectModal .k-listbox-toolbar .k-reset .k-button-icon {
    border: 1px solid #d1d5db;
    border-radius: 8px;
}

.k-icon.k-i-delete,
.k-icon.k-i-edit {
    flex-shrink: 0;
}

.k-i-delete::before {
    content: close-quote !important;
    background-image: url('../../img/delete-new-gray.svg') !important;
    background-position: center;
    background-repeat: no-repeat;
    background-size: 0.875rem;
}

.k-i-edit::before {
    content: close-quote !important;
    background-image: url('../../img/edit-gray-pencil.svg') !important;
    background-position: center;
    background-repeat: no-repeat;
    background-size: 0.875rem;
}

.k-input.k-focus,
.k-input:focus {
    box-shadow: none !important;
}

.bg-gray-100 {
    background-color: #f1f5f9 !important;
}

#offerServiceAddedInfo {
    overflow-y: hidden !important;
}

.oshc-general-li,
.additional-li {
    overflow-y: auto !important;
    max-height: 100vh;
}

/* #studentServiceTabStrip
    .k-tabstrip-items-wrapper
    .k-tabstrip-items
    .k-item.k-state-active
    .k-link {
    border-bottom: 3px solid #1890ff;
} */

#studentServiceTabStrip .k-tabstrip-items-wrapper {
    padding: 0px 24px;
}

#studentServiceTabStrip .k-tabstrip-items-wrapper .k-item {
    background: transparent;
}

#studentAdditionalServiceGrid .k-button {
    min-width: 28px !important;
    border: none !important;
    box-shadow:
        0px 2px 3px rgba(0, 0, 0, 0.1),
        0px 2px 3px rgba(0, 0, 0, 0.06) !important;
    border-radius: 4px !important;
}

.k-textbox.k-invalid {
    border: 1px solid red !important;
}

/* .k-form-field-wrap .k-textbox {
    border-width: 1px;
    border-radius: 0.5rem;
    height: 38px;
    padding: 4px;
    --tw-border-opacity: 1;
    border-color: rgb(209 213 219 / var(--tw-border-opacity));
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000),
        var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
} */

#additionalServiceReqModal .k-form-layout,
#editAdditionalServiceReqModal .k-form-layout {
    overflow-y: auto;
    max-height: 500px !important;
    /* / padding: 3px; / */
}

#studentDocumentListStepper ol li .k-step-label {
    display: none;
}

#studentDocumentListStepper ol li .k-step-indicator {
    width: 100% !important;
    height: auto;
    margin: 0px !important;
    display: flex !important;
    justify-content: flex-start !important;
    padding: 10px !important;
    border-right: 0px !important;
    border-left: 0px !important;
    border-top: 0px !important;
    border-color: #e5e7eb !important;
}

#studentDocumentListStepper.k-stepper .k-step-indicator {
    border-radius: 0%;
}

#studentDocumentListStepper.k-stepper .k-step-indicator::after {
    border-radius: 0%;
}

#studentDocumentListStepper.k-stepper .k-step-done .k-step-indicator {
    background-color: #fff;
}

#studentDocumentListStepper .k-step-list-vertical .k-step {
    min-height: auto !important;
}

#studentDocumentListStepper .k-widget.k-progressbar.k-progressbar-vertical {
    display: none !important;
}

.k-stepper.studentDocumentListStepper .k-step-current .k-step-indicator {
    background-color: #e6f7ff !important;
}

.k-stepper.studentDocumentListStepper .k-step-indicator {
    color: #1890ff !important;
    background-color: #ffffff !important;
}

.k-stepper.studentDocumentListStepper .k-step-current .k-step-indicator {
    border-color: #e5e7eb;
    color: #1890ff;
    background-color: var(--color-gray-100) !important;
}

.k-stepper.studentDocumentListStepper .k-step-done .k-step-indicator {
    color: #1890ff !important;
}

.k-stepper.studentDocumentListStepper .k-step-last .k-step-indicator {
    border-bottom: 0px !important;
}

#uploaddoc .k-upload {
    width: 100%;
}

#uploaddoc .k-dropzone {
    background-color: #f9fafb;
    height: 200px;
    cursor: pointer;
}

#uploaddoc .k-upload-button {
    position: absolute;
    top: 0;
    left: 0;
    color: transparent;
    border: 0;
    background: transparent;
    cursor: pointer;
    width: 100%;
    height: 100%;
    cursor: pointer;
    z-index: 22;
    border: 2px dashed #d1d5db;
}

#uploaddoc .k-dropzone-hint,
#uploaddoc .k-upload-status-total {
    text-align: center;
    display: block;
    width: 100%;
}

#uploaddoc .k-upload {
    border: 0px;
    border-radius: 0.5rem;
}

#uploaddoc .k-upload-button {
    border-radius: 6px !important;
}

#studentDocumentListModal .k-wizard-horizontal .k-wizard-steps,
#paymentAddNewServicesModal .k-wizard-horizontal .k-wizard-steps,
#generatePaymentScheduleModal .k-wizard-horizontal .k-wizard-steps,
#editAdditionalServiceInformationModal .k-wizard-horizontal .k-wizard-steps {
    margin-top: 0px !important;
}

#studentDocumentListModal .k-wizard-step,
#generatePaymentScheduleModal .k-wizard,
#generatePaymentScheduleModal .k-wizard .k-wizard-step,
#paymentAddNewServicesModal .k-wizard,
#paymentAddNewServicesModal .k-wizard .k-wizard-step,
#editAdditionalServiceInformationModal .k-wizard,
#editAdditionalServiceInformationModal .k-wizard .k-wizard-step {
    padding: 0px !important;
}

#studentDocumentListModal .k-wizard .k-wizard-step.k-state-focused,
.k-wizard .k-wizard-step:focus {
    outline-width: 0px !important;
}

/* filemanager */

.k-filemanager {
    border-color: #e5e7eb !important;
    border-radius: 8px !important;
}

.k-toolbar {
    background-color: #ffffff !important;
}

.k-filemanager-navigation,
.k-filemanager-splitbar-navigation {
    display: none !important;
}

.k-filemanager-toolbar {
    border-top-left-radius: 8px !important;
    border-top-right-radius: 8px !important;
}

.k-filemanager-content .k-listview {
    border-radius: 8px !important;
}

.k-toolbar {
    padding: 16px 24px !important;
}

.k-toolbar .k-button.upload,
.k-toolbar .k-button.newfolder {
    /* color: #FFFFFF  !important;
    font-style: normal  !important;
    font-weight: 500  !important;
    font-size: 14px  !important;
    line-height: 16px  !important;
    padding: 10px 17px  !important;
    border-radius: 8px  !important;
    background-color: #1890FF !important;
    border: 0px solid #D1D5DB  !important; */
}

.k-toolbar .k-group-start {
    border-top-left-radius: 8px !important;
    border-bottom-left-radius: 8px !important;
    /* padding: 17px 16px !important; */
    border: none !important;
}

.k-toolbar .k-group-end {
    border-top-right-radius: 8px !important;
    border-bottom-right-radius: 8px !important;
    /* padding: 17px 16px !important; */
    border: none !important;
}

.k-toolbar .k-button-group {
    background: #e5e7eb !important;
    border-radius: 8px !important;
    padding: 2px 0;
}

.k-toolbar .k-button-group .k-icon {
    padding: 0.875rem;
}

.k-toolbar .k-button-group .k-state-active .k-icon {
    background: #ffffff;
    box-shadow:
        0px 1px 3px rgba(0, 0, 0, 0.1),
        0px 1px 2px rgba(0, 0, 0, 0.06);
    border-radius: 8px;
}

.k-toolbar .k-split-button .k-button {
    border-top-left-radius: 8px !important;
    border-bottom-left-radius: 8px !important;
    padding: 9px 10px !important;
    border: 1px solid #d1d5db !important;
    color: #374151 !important;
    font-style: normal !important;
    font-weight: 500 !important;
    font-size: 12px !important;
}

.k-toolbar .k-split-button .k-button.k-split-button-arrow {
    border-top-right-radius: 8px !important;
    border-bottom-right-radius: 8px !important;
    border-top-left-radius: 0px !important;
    border-bottom-left-radius: 0px !important;
    padding: 9px 8px !important;
    border: 1px solid #d1d5db !important;
    border-left: 0px !important;
}

.k-toolbar .k-split-button:focus {
    box-shadow: none !important;
}

.k-toolbar .k-filemanager-details-toggle {
    color: #6b7280 !important;
    font-weight: 400 !important;
}

.k-toolbar .k-filemanager-search-tool {
    padding: 0 0.5rem !important;
    border: 1px solid #d1d5db !important;
    border-radius: 8px !important;
}

.k-filemanager-breadcrumb .k-breadcrumb-link {
    color: #374151 !important;
    opacity: 1 !important;
}

.k-filemanager-content .k-listview-content > .k-state-selected {
    background-color: #e6f7ff !important;
}

.k-filemanager-preview .k-file-meta .k-file-meta-label,
.k-filemanager-preview .k-file-meta .k-file-meta-value {
    color: #6b7280 !important;
    padding-right: 4px !important;
}

.k-toolbar .k-i-arrow-60-down::before {
    content: close-quote !important;
    background-image: url('../../img/arrow-down.svg');
    background-position: center;
    background-repeat: no-repeat;
    margin: 0 !important;
}

.k-toolbar .k-i-sort-asc-sm::before {
    content: close-quote !important;
    background-image: url('../../img/arrow-asc.svg');
    background-position: center;
    background-repeat: no-repeat;
}

.k-toolbar .k-i-sort-desc-sm::before {
    content: close-quote !important;
    background-image: url('../../img/arrow-desc.svg');
    background-position: center;
    background-repeat: no-repeat;
}

.k-toolbar .k-i-grid-layout::before {
    content: close-quote !important;
    background-image: url('../../img/grid-layout.svg');
    background-position: center;
    background-repeat: no-repeat;
}

.k-toolbar .k-i-grid::before {
    content: close-quote !important;
    background-image: url('../../img/grid-view.svg');
    background-position: center;
    background-repeat: no-repeat;
}

.k-toolbar .k-i-search::before {
    content: close-quote !important;
    background-image: url('../../img/search.svg');
    background-position: center;
    background-repeat: no-repeat;
    background-size: 1rem;
}

.k-i-home::before {
    color: #9ca3af !important;
}

.k-filemanager-preview .k-i-folder::before,
.k-filemanager-view-list .k-i-folder::before {
    content: close-quote !important;
    background-image: url('../../img/foldernew.svg');
    background-position: center;
    background-repeat: no-repeat;
}

.k-filemanager-view-grid .k-i-folder::before {
    content: close-quote !important;
    background-image: url('../../img/mini-folder.svg');
    background-position: center;
    background-repeat: no-repeat;
}

/* .k-filemanager-view-grid .k-grid-content .k-master-row td:first-child {
    display: flex !important;
    align-items: center !important;
} */

/* .k-filemanager-view-grid
    .k-grid-content
    .k-master-row
    td:first-child
    .file-group-icon {
    margin-right: 6px !important;
} */

.k-filemanager-preview .k-i-file-pdf::before,
.k-filemanager-view-list .k-i-file-pdf::before {
    content: close-quote !important;
    background-image: url('../../img/newpdf.svg');
    background-position: center;
    background-repeat: no-repeat;
}

.k-filemanager-view-grid .k-i-file-pdf::before {
    content: close-quote !important;
    background-image: url('../../img/new-minipdf.svg');
    background-position: center;
    background-repeat: no-repeat;
}

.k-filemanager .k-listview-content .k-state-focused {
    box-shadow: none !important;
}

.grid-payment-container .grid-item:nth-child(n + 7) {
    display: none;
}

@media (max-width: 1536px) {
    .grid-payment-container .grid-item:nth-child(n + 5) {
        display: none;
    }
}

/* #agentCommissionTabStrip
    .k-tabstrip-items-wrapper
    .k-tabstrip-items
    .k-item.k-state-active
    .k-link {
    border-bottom: 3px solid #1890ff;
} */

#agentCommissionTabStrip .k-tabstrip-items-wrapper {
    margin-bottom: -3px !important;
}

#agentCommissionTabStrip .k-tabstrip-items-wrapper .k-item {
    background: transparent;
}

#agentCommissionTabStrip .k-tabstrip-items-wrapper .k-item.k-state-active span {
    color: #1890ff !important;
}

.agent-commission-li,
.agent-bonus-li {
    overflow-y: auto !important;
    max-height: 100vh;
}

/*upfrontfee switch css*/

.upfrontSwitchButton span.k-switch-handle {
    background-color: white;
}

.upfrontSwitchButton span.k-switch.k-switch-on {
    background-color: #1890ff;
}

.upfrontSwitchButton .k-switch-container {
    border: 2px;
}

.upfrontSwitchButton span.k-switch.k-switch-off {
    background-color: #e5e7eb;
}

.upfrontSwitchButton span.k-switch-label-on,
span.k-switch-label-off {
    display: none;
}

/* action Tooltip */
.k-widget.k-tooltip.k-popup.k-group.k-reset {
    padding: 0px;
    background-color: white;
}

.k-grid tr:hover td {
    background-color: #f8fafc;
}

.k-input.k-invalid {
    border: none !important;
}

.k-radio-item .k-radio[type='radio']:checked:focus {
    outline: 2px solid rgba(24, 144, 255, 1);
    outline-offset: 2px;
}

.k-radio-item .k-radio[type='radio']:checked {
    border-color: rgba(24, 144, 255, 1) !important;
    border-radius: 0.5rem !important;
    background-image: none !important;
    background-color: rgba(24, 144, 255, 1) !important;
}

.k-radio.k-checked.k-state-focus,
.k-radio:checked:focus {
    box-shadow: none;
}

.k-radio-item .k-radio {
    margin-right: 5px !important;
}

.paymentRefundOverFlow {
    overflow-y: auto !important;
    max-height: 500px !important;
}

.number-only {
    padding: 7px;
    width: 100%;
    border-width: 1px;
    border-radius: 0.5rem;
    height: 2.5rem;
    --tw-border-opacity: 1;
    border-color: rgb(209 213 219 / var(--tw-border-opacity));
    box-shadow:
        var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.float-only {
    padding: 7px;
    width: 100%;
    border-width: 1px;
    border-radius: 0.5rem;
    height: 2.5rem;
    --tw-border-opacity: 1;
    border-color: rgb(209 213 219 / var(--tw-border-opacity));
    box-shadow:
        var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.ck-editor__editable {
    height: 150px !important;
}

.actionButtonHide {
    display: none;
}

.recordPaymentBtn,
.editSchedulePaymentInfoBtn {
    display: none;
}

tr:hover td .recordPaymentBtn,
tr:hover td .editSchedulePaymentInfoBtn {
    display: inline;
}

.viewAllActions span {
    transition: all 300ms ease;
}

.viewAllActions.active span {
    transform: rotateZ(-180deg);
    transition: all 300ms ease;
}

#otherDocumentId {
    height: 100% !important;
    width: 100% !important;
}

/* Strat CSS related to Splitter */
.k-splitbar-horizontal .k-icon {
    top: 10% !important;
}

.k-ghost-splitbar-horizontal,
.k-splitbar-horizontal,
.k-splitbar-horizontal.k-state-focused {
    /* width: 10px !important; */
    background-color: var(--color-gray-100);
}

.k-splitbar.k-state-focus,
.k-splitbar.k-state-focused,
.k-splitbar:focus {
    /* background-color: var(--color-gray-100) !important; */
    /* background-color: rgb(59 130 246) !important; */
}

.k-icon.k-collapse-prev,
.k-icon.k-expand-prev {
    /* border: 1px solid rgb(209 213 219) !important; */
    border-radius: 50% !important;
    /* padding: 6px !important; */
    z-index: 1000 !important;
    position: absolute !important;
    width: 1.25rem;
    height: 1.25rem;
}

.k-icon.k-collapse-prev:hover,
.k-icon.k-expand-prev:hover {
    /* border: 2px solid rgb(59 130 246) !important; */
    /*background-color: rgb(59 130 246) !important;*/
}

.k-splitbar-horizontal-hover:hover > .k-icon.k-resize-handle,
.k-splitbar-horizontal-hover:hover > .k-icon,
.k-resize-handle:hover {
    /* color: white !important; */
}

.k-callout-s {
    bottom: 0px;
}

/* hide scrollbar */
main {
    -ms-overflow-style: none;
    /* Internet Explorer 10+ */
    scrollbar-width: none;
    /* Firefox */
}

main::-webkit-scrollbar {
    display: none;
    /* Safari and Chrome */
}

.sortabled.open .dropdown-menu.dm-toggle {
    top: inherit !important;
    left: inherit !important;
    display: block;
    margin-right: 20px;
    margin-top: 104px;
}

/* //timetable schedular  */

#timetable-month .k-scheduler-toolbar,
.k-scheduler-footer {
    display: none !important;
}

#timetable-month .k-scheduler-header,
#timetable-month .k-scheduler-header-wrap {
    background-color: #f1f5f9 !important;
    font-size: 0.75rem;
    letter-spacing: 0.25px;
    text-transform: uppercase;
}

#timetable-month .k-scheduler-header .k-scheduler-header-wrap th {
    border: none;
    color: #6b7280;
    font-weight: 500 !important;
}

#timetable-month .k-scheduler-times th {
    text-align: center;
    color: #6b7280;
    font-weight: 500 !important;
}

#timetable-month .k-scheduler-dayview .k-scheduler-table .k-middle-row td,
#timetable-month .k-scheduler-weekview .k-scheduler-table .k-middle-row td {
    border-bottom-color: transparent;
}

#timetable-month {
    border: none;
}

.k-scheduler .k-other-month,
.k-scheduler-other-month,
.k-scheduler .k-nonwork-hour {
    background-color: rgb(249 250 251);
}

#timetable-month .k-scheduler-content .k-scheduler-table .k-link {
    color: #6b7280;
    font-weight: 500 !important;
}

#timetable-month .k-scheduler-monthview .k-scheduler-table td {
    text-align: left;
    border-color: #e5e7eb;
}

#timetable-month
    table.k-scheduler-layout.k-scheduler-dayview
    tr:first-child
    td:not([role='gridcell']),
#timetable-month
    table.k-scheduler-layout.k-scheduler-weekview
    tr:first-child
    td:not([role='gridcell']) {
    background-color: #f1f5f9;
    border-color: #f1f5f9;
}

#timetable-month .k-scheduler-weekview .k-scheduler-table td {
    border-color: #e5e7eb;
}

#timetable-month table.k-scheduler-layout.k-scheduler-weekview .k-scheduler-times,
#timetable-month table.k-scheduler-layout.k-scheduler-dayview .k-scheduler-times {
    width: 200px;
}

#timeTableDatePicker {
    text-align: center;
    margin-left: 12px;
}

#studentTimetableView .k-picker-wrap {
    padding-right: 0px;
    padding-bottom: 0px !important;
}

#studentTimetableView .k-picker-wrap .k-select {
    line-height: 2.5em !important;
    border-width: 0 !important;
    border-color: unset !important;
    right: unset !important;
    width: 2rem !important;
}

#studentTimetableView .k-picker-wrap .k-select .k-i-calendar {
    background-image: url(../../img/calendar_v2.svg) !important;
    font-size: 18px !important;
    margin-top: 0px;
}

#studentTimetableView .k-picker-wrap .k-select .k-i-calendar::before {
    content: unset;
}

#timetableData {
    border: #e5e7eb;
}

#timetableData [type='text']:focus {
    --tw-ring-color: #ffffff;
}

#timetable-month .k-scheduler-monthview .k-event,
#timetable-month .k-scheduler-weekview .k-event,
#timetable-month .k-scheduler-dayview .k-event {
    padding: 0;
    background: transparent !important;
}

#timetable-month .k-scheduler-dayview .k-event {
    width: 320px !important;
}

.k-scheduler-monthview .k-scheduler-content table,
.k-scheduler-dayview tbody tr:nth-child(2) .k-scheduler-times table,
.k-scheduler-weekview tbody tr:nth-child(2) .k-scheduler-times table,
.k-scheduler-weekview tbody tr:nth-child(2) {
    border: 1px solid #e5e7eb;
}

.k-scheduler-dayview tbody tr:nth-child(2) td:nth-child(2),
.k-scheduler-weekview .k-scheduler-content table {
    border-top: 1px solid #e5e7eb;
    border-right: 1px solid #e5e7eb;
    border-bottom: 1px solid #e5e7eb;
}

#btnPrevDate .k-i-arrow-60-left::before {
    content: close-quote !important;
    background-image: url('../../img/left-side-calendar-arrow.svg');
    background-position: center;
    background-repeat: no-repeat;
}

#btnNextDate .k-i-arrow-60-right::before {
    content: close-quote !important;
    background-image: url('../../img/right-side-calendar-arrow.svg');
    background-position: center;
    background-repeat: no-repeat;
}

.k-tooltip .k-callout {
    display: none;
}

#hideTooltip .k-i-close {
    color: #9ca3af;
}

/* Attendance Switch Start*/
.attdSwitch {
    position: relative;
    display: inline-block;
    overflow: hidden;
}

.attdSwitch input {
    display: none;
}

.attdSlider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgb(191 219 254);
    /*bg-blue-200*/
    -webkit-transition: 0.4s;
    transition: 0.4s;
}

.attdSlider:before {
    position: absolute;
    content: '';
    height: 20px;
    width: 20px;
    left: 5%;
    bottom: 20%;
    background-color: white;
    -webkit-transition: 0.4s;
    transition: 0.4s;
}

input:checked + .attdSlider {
    background-color: rgb(229 231 235);
    /*bg-gray-200*/
}

input:focus + .attdSlider {
    box-shadow: 0 0 1px rgb(229 231 235);
    /*bg-gray-200*/
}

input:checked + .attdSlider:before {
    -webkit-transform: translateX(92px);
    -ms-transform: translateX(92px);
    transform: translateX(92px);
}

.attdOn {
    display: none;
}

.attdOff {
    position: absolute;
    transform: translate(-50%, -50%);
    top: 50%;
    left: 50%;
}

.attdOn {
    position: absolute;
    transform: translate(-50%, -50%);
    top: 50%;
    left: 50px;
}

input:checked + .attdSlider .attdOn {
    display: block;
}

input:checked + .attdSlider .attdOff {
    display: none;
}

.attdSlider.round {
    border-radius: 35px;
}

.attdSlider.round:before {
    border-radius: 50%;
}

/* Attendance Switch End */

.k-scheduler-views.k-button-group {
    padding: 0px !important;
}

.k-scheduler-views .k-state-selected {
    color: white !important;
    background-color: rgb(59 130 246) !important;
    /*box-shadow: inset 0 0 0 2px rgba(0,0,0,.13)*/
}

#attendanceScheduler .k-event {
    overflow: visible !important;
    background: transparent !important;
    color: black;
}

#attendanceScheduler .k-scheduler-header-wrap .k-scheduler-table {
    border-collapse: collapse;
}

#attendanceScheduler .k-toolbar {
    padding: 0px !important;
}

#attendanceScheduler,
#attendanceScheduler .k-scheduler-toolbar,
#attendanceScheduler .k-scheduler-header-wrap .k-scheduler-table tr,
#attendanceScheduler .k-scheduler-header-wrap .k-scheduler-table th {
    border: none;
}

#attendanceScheduler.k-scheduler .k-event {
    border-left-width: 0;
}

.k-picker-wrap {
    background-color: none !important;
    border-radius: 8px;
}

.k-event.k-state-selected {
    color: #000 !important;
    background-color: #fff !important;
    box-shadow: inset 0 0 0 2px rgba(0, 0, 0, 0.13);
}

.header_course_list .k-dropdown-wrap .k-input:before {
    display: block;
}

.accordion.is-open ~ .accordion-content,
.accordion.is-open ~ .accordion-contentv2 {
    max-height: unset;
    min-height: 170px;
    height: 100%;
    transition: all 1000ms ease-in-out;
}

.summary-li-detail .k-splitbar:hover,
.summary-li-detail .k-splitbar.k-hover,
.summary-li-detail .k-splitbar-horizontal-hover,
.summary-li-detail .k-splitbar-vertical-hover {
    background-color: #f1f5f9;
}

.course-list .k-select > .k-i-arrow-60-down::before {
    margin-top: 0 !important;
}

.timeline {
    padding-left: 3.5rem;
}

.timeline:before {
    width: 1px;
    left: -0.5rem;
}

.timeline-body {
    margin-left: -42px !important;
}

/*enroll course switch css*/
.customSwitchButton .k-switch {
    width: 44px;
}

.customSwitchButton .k-switch span.k-switch-handle {
    background-color: white;
    width: 1.25rem;
    height: 1.25rem;
}

.customSwitchButton .k-switch span.k-switch.k-switch-on {
    background-color: #1890ff;
}

.customSwitchButton .k-switch .k-switch-container {
    border: 2px;
}

.customSwitchButton .k-switch span.k-switch.k-switch-off {
    background-color: #e5e7eb;
}

.customSwitchButton .k-switch span.k-switch-label-on,
span.k-switch-label-off {
    display: none;
}

.customSwitchButton .k-switch-on .k-switch-handle {
    left: calc(100% - 1.25rem);
}

.customSwitchButton .k-switch-container {
    padding-block: 2.5px;
}

#fileManager .k-filemanager-view {
    overflow: hidden !important;
}

.moreActionMenu.hidden {
    z-index: 1;
    position: absolute;
    /* display: none; */
}

.moreActionMenu {
    z-index: 9999;
    position: absolute;
    /* display: block; */
    top: calc(100% + 6px);
    right: 0;
    width: 512px;
}

#attendanceScheduler .k-toolbar .k-button-group {
    background: transparent !important;
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0px -1px 0px 0px #e5e7eb inset;
    border-radius: 0 !important;
    padding-block: 0.5rem;
}

#attendanceScheduler .k-toolbar .k-button-group .k-nav-today {
    display: none;
}

#attendanceScheduler .k-toolbar .k-button-group .k-button {
    border-width: 0;
    border-radius: 6px;
    margin-top: 2px;
    height: fit-content;
}

#attendanceScheduler .k-toolbar .k-nav-current {
    position: absolute;
    top: 8px;
    left: 50%;
    transform: translateX(-50%);
}

#issueLetterStudentModal .ck-editor__editable {
    height: 400px !important;
}

/* Tagify css */
.tagify__tag {
    border: 1px solid #ddd !important;
    border-radius: 30px !important;
}

.tagify__tag__removeBtn {
    font: 20px Arial !important;
    color: #9ca3af !important;
}

.tag-div {
    border-radius: 30px !important;
}

.tagify {
    --tag-bg: none;
    --tag-hover: #bae7ff !important;
    --tags-disabled-bg: #fff !important;
    --tags-border-color: #ddd !important;
    --tags-hover-border-color: #ddd !important;
    --tag-remove-btn-bg--hover: none !important;
    --tag-remove-btn-color: black;
    --tag-remove-bg: none !important;
}

.tagify {
    border: none !important;
}

.course-status.bg-gray-100 {
    background-color: var(--color-gray-200) !important;
}

.k-grid tr:not(.k-grid-edit-row) td.k-command-cell > * {
    opacity: 0;
    visibility: hidden;
}

.k-grid tr:hover td.k-command-cell > * {
    opacity: 1;
    visibility: visible;
}

/* Start for GNG-2568 */
#ifoStudCourseImportedDiv,
#ifoStudCourseNotImportedDiv,
#ifoStudCourseWaitForScheduleDiv,
.offerCourseDiv,
.isCustomStartDateDiv,
.ifoImportScheduleCk {
    display: none;
}

#importFromOfferModal,
#ifoPaymentScheduleModal {
    background-color: var(--color-gray-50) !important;
}

.dropdownWithInput {
    padding: 7px;
    /*width: 100%;*/
    /*border-width: 1px;*/
    border-radius: 0.5rem;
    height: 2.25rem;
    --tw-border-opacity: 1;
    border-color: rgb(209 213 219 / var(--tw-border-opacity));
    box-shadow:
        var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

/* End for GNG-2568 */

#activityTabStripActivityLogTab .k-tabstrip-items {
    gap: 0.25rem;
}

#activityTabStripActivityLogTab .k-tabstrip-items .k-link {
    padding: 0.313rem 0.75rem;
}

#viewStudentTCSIModal .k-fieldselector .k-list .k-item,
#viewStudentTCSIModal .k-list-optionlabel.k-state-focused,
#viewStudentTCSIModal .k-list-optionlabel.k-state-selected,
#viewStudentTCSIModal .k-popup .k-list .k-state-focused,
#viewStudentTCSIModal .k-popup .k-list .k-state-selected,
#viewStudentTCSIModal .k-action-buttons .k-primary {
    background-color: #1890ff;
    cursor: pointer;
    color: white;
    border-radius: 4px;
}

#addCourseList .k-form-buttons {
    position: relative;
    z-index: 1;
}

#addOsHelpForm.k-form .k-form-field:nth-child(2),
#addSaHelpForm.k-form .k-form-field:nth-child(2) {
    grid-column: span 8;
    width: calc(50% - 0.5rem);
}

#studentTimetableView .k-picker-wrap {
    border-radius: 0;
}

.header_course_list .k-dropdown-wrap .k-select .k-icon {
    background-color: transparent;
}

#statusForSendEmailModal.k-window-content {
    padding-bottom: 16px !important;
}

.customTextField {
    padding: 7px;
    width: 100%;
    border-width: 1px;
    border-radius: 0.5rem;
    --tw-border-opacity: 1;
    border-color: rgb(209 213 219 / var(--tw-border-opacity));
    box-shadow:
        var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.k-grid tr.inActiveData td {
    background-color: var(--color-red-50) !important;
}

.pac-container.pac-logo.hdpi {
    z-index: 99999;
}

.pac-container {
    z-index: 10005 !important;
}

input.newParameter.error {
    border-color: #ef4444 !important;
}
.ck.ck-balloon-panel.ck-balloon-panel_visible {
    z-index: 999999;
}
