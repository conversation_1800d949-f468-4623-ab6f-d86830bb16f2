var pInitialPayFlag = true;
var pScheduleFlag = true;
var reCreateScheduleDateWarningModalId = '#reCreateScheduleDateWarningModal';
var editScheduleDateWarningModalId = '#editScheduleDateWarningModal';

// TODO:INIT include in payment
function initializeInstallAndPayment() {
    $('#deletePaymentScheduleModal').kendoWindow(
        openCenterWindow('Delete Payment Transaction', 36, 10, 32)
    );
    addModalClassToWindows(['#deletePaymentScheduleModal']);

    $(editScheduleDateWarningModalId).kendoDialog({
        width: '400px',
        title: 'Confirm',
        content: 'You are making a schedule before the course start date. Do you wish to continue?',
        actions: [
            { text: 'No' },
            {
                text: 'Yes',
                primary: true,
                action: function () {
                    updateSchedulePaymentData();
                },
            },
        ],
        animation: defaultOpenAnimation(),
        open: onOpenDeleteDialog(editScheduleDateWarningModalId),
        visible: false,
    });

    $(reCreateScheduleDateWarningModalId).kendoDialog({
        width: '400px',
        title: 'Confirm',
        content: 'You are making a schedule before the course start date. Do you wish to continue?',
        actions: [
            { text: 'No' },
            {
                text: 'Yes',
                primary: true,
                action: function () {
                    saveReCreatePaymentScheduleData();
                },
            },
        ],
        animation: defaultOpenAnimation(),
        open: onOpenDeleteDialog(reCreateScheduleDateWarningModalId),
        visible: false,
    });
}

function PaymentScheduleData() {
    let pScheduleBound = false;
    if (pScheduleFlag) {
        // TODO:FLAG retrive after tab change
        pScheduleFlag = false;
        // pScheduleFlag = true;
        $(pScheduleGridId).kendoGrid({
            dataSource: getDataSourceForInstallmentAndScheduleGrid(
                'get-payment-schedule-data',
                pScheduleGridId
            ),
            pageable: customPageableArr(),
            sortable: true,
            resizable: true,
            //selectable: "row",
            columns: getColumnDataForInstallmentAndScheduleGrid(),
            noRecords: noRecordTemplate(),
            dataBound: function (e) {
                $('.k-link').addClass('tw-sort-icon');
                //onBoundInstallment();
                togglePagination(pScheduleGridId);
                manageRowBindForScheduleGrid(e);
                toggleTabLoader('payments', false);
                pScheduleBound = true;
            },
            change: function (e) {
                var selectedScheduleRows = this.select();
                //let isRowSelected = (isStudentSync == 1 && selectedScheduleRows.length > 0) ? true : false;
                //handleButtonTitleAttr(".bulkSyncPaymentBtn", isRowSelected);
                handleButtonTitleAttr('.bulkDeleteBtn', selectedScheduleRows.length > 0);
            },
        });
        // Set min width for each column
        customGridHtml(pScheduleGridId);
        setColumnMinWidth(pScheduleGridId);
    } else {
        refreshGrid(pScheduleGridId, selectedDataArr, true);
        pScheduleBound = true;
    }

    let intervalId = setInterval(function () {
        if (pScheduleBound) {
            onBoundInstallment();
            clearInterval(intervalId);
        }
    }, 1000);
}

function InitialPaymentData() {
    let pInitialBound = false;
    if (pInitialPayFlag) {
        pInitialPayFlag = false;
        // TODO:FLAG
        // pInitialPayFlag = true;
        $(pInitialPayGridId).kendoGrid({
            dataSource: getDataSourceForInstallmentAndScheduleGrid(
                'get-initial-payment-data',
                pInitialPayGridId
            ),
            pageable: customPageableArr(),
            sortable: true,
            resizable: true,
            //selectable: "row",
            //columns: getColumnDataForInstallmentAndScheduleGrid(),
            columns: getColumnDataForInstallmentAndScheduleGrid(),
            noRecords: noRecordTemplate(),
            dataBound: function (e) {
                $('.k-link').addClass('tw-sort-icon');
                //onBoundPrepayment();
                togglePagination(pInitialPayGridId);
                manageRowBindForScheduleGrid(e);
                pInitialBound = true;
            },
            change: function (e) {
                var selectedPrePaymentRows = this.select();
                //let isRowSelected = (isStudentSync == 1 && selectedPrePaymentRows.length > 0) ? true : false;
                //handleButtonTitleAttr(".bulkSyncPaymentBtn", isRowSelected);
                handleButtonTitleAttr('.bulkDeleteBtn', selectedPrePaymentRows.length > 0);
            },
        });
        // Set min width for each column
        customGridHtml(pInitialPayGridId);
        setColumnMinWidth(pInitialPayGridId);
    } else {
        refreshGrid(pInitialPayGridId, selectedDataArr, true);
        pInitialBound = true;
    }

    let intervalId = setInterval(function () {
        if (pInitialBound) {
            onBoundPrepayment();
            clearInterval(intervalId);
        }
    }, 1000);
}

function getDataSourceForInstallmentAndScheduleGrid(apiURL, closestGridId) {
    // let isXeroConnectVal = false;
    // let isStudentSync = false;
    return {
        type: 'json',
        transport: {
            read: {
                url: site_url + 'api/' + apiURL,
                dataType: 'json',
                type: 'POST',
                data: selectedDataArr,
            },
        },
        requestEnd: function (e) {
            if (e.type === 'read' && e.response) {
                isXeroConnectVal = e.response.data.xeroConnect;
                isStudentSync = e.response.data.isStudentSync;
                manageXeroColumnsForScheduleGrid(e.response.data.xeroConnect, closestGridId);
                manageReScheduleButton(e.response.data.reSchedule);
            }
        },
        schema: {
            data: 'data.data',
            total: 'data.total',
            xeroConnect: 'data.xeroConnect',
            model: {
                id: 'id',
                fields: {
                    invoice_number: { type: 'string' },
                    invoiced_start_date: { type: 'date' },
                    due_date: { type: 'date' },
                    payment_type: { type: 'string' },
                    commission: { type: 'string' },
                    payment_status: { type: 'string' },
                    fee_duration: { type: 'string' },
                    xero_invoice: { type: 'object' },
                },
            },
        },
        pageSize: 10,
        serverPaging: true,
        serverFiltering: true,
        serverSorting: true,
    };
}

function getColumnDataForInstallmentAndScheduleGrid() {
    return [
        {
            selectable: true,
            width: 44,
            minResizableWidth: 44,
            minWidth: 28,
        },
        {
            field: 'formatted_invoice_number',
            title: 'Inv No',
            template:
                "<div class='action-div flex items-center text-sm leading-5 font-normal text-gray-600'>#: formatted_invoice_number #</div>",
            minResizableWidth: 100,
            minWidth: 100,
        },
        {
            field: 'invoiced_start_date',
            title: 'Inv. Start Date',
            format: '{0:dd/MM/yyyy}',
            template:
                "<div class='start_date flex items-center text-13 leading-4 text-gray-600 action-div '> #: kendo.toString(invoiced_start_date, displayDateFormatJS) # </div>",
            minResizableWidth: 100,
            minWidth: 120,
            width: 120,
        },
        {
            field: 'upfront_fee_to_pay',
            title: 'Payable Amount',
            template:
                "<div class='start_date flex items-center text-13 leading-4 text-gray-600 action-div'> #: kendo.toString(upfront_fee_to_pay,'c') # </div>",
            minResizableWidth: 100,
            minWidth: 90,
            width: 120,
        },
        {
            field: 'amount',
            title: 'Amount Due',
            template:
                "<div class='start_date flex items-center text-13 leading-4 text-gray-600 action-div'> #: kendo.toString(amount,'c') # </div>",
            minResizableWidth: 100,
            minWidth: 90,
            width: 120,
        },
        {
            field: 'payment_type',
            title: 'Type',
            template: function (dataItem) {
                let pType = dataItem.payment_type == 'Schedual' ? 'Tuition fee' : 'Prepayment';
                return `<div class='inline-flex items-center justify-center px-3 bg-blue-200 rounded'><span class='text-xs leading-6 text-center text-blue-900'>${pType}</span></div>`;
            },
            //sortable: false,
            filterable: false,
            minResizableWidth: 100,
            minWidth: 80,
            width: 120,
        },
        {
            field: 'due_date',
            title: 'Due Date',
            format: '{0:dd/MM/yyyy}',
            template:
                "<div class='start_date flex items-center text-13 leading-4 text-gray-600 action-div '> #: kendo.toString(due_date, displayDateFormatJS) # </div>",
            minResizableWidth: 100,
            minWidth: 100,
            width: 120,
        },
        {
            field: 'payment_status',
            title: 'Status',
            template: function (dataItem) {
                return manageStatusMode(dataItem.payment_status);
            },
            minResizableWidth: 100,
            minWidth: 80,
            width: 120,
        },
        {
            field: 'fee_duration',
            title: 'Fee Duration',
            template:
                "<div class='action-div flex items-center text-sm leading-5 font-normal text-gray-500'>#: fee_duration #</div>",
            minResizableWidth: 100,
            minWidth: 170,
            width: 190,
        },
        {
            field: 'commission',
            title: 'Agent Commission',
            minResizableWidth: 100,
            minWidth: 200,
            width: 130,
            template: function (dataItem) {
                if (dataItem.commission_value > 0) {
                    return agentCommForScheduleRecord(dataItem);
                } else {
                    return 'N/A';
                }
            },
        },
        {
            field: 'xero_invoice',
            headerTemplate:
                "<a class='k-link text-xs font-normal tracking-wide leading-none text-gray-500' style='cursor: default !important;'>Xero Sync</a>",
            minResizableWidth: 100,
            minWidth: 120,
            width: 120,
            sortable: false,
            template: function (dataItem) {
                return manageXeroStatus(dataItem.xero_invoice, dataItem.is_synced);
            },
        },
        {
            field: 'xero_synced_at',
            headerTemplate:
                "<a class='k-link text-xs font-normal tracking-wide leading-none text-gray-500' style='cursor: default !important;'>Xero Sync Time</a>",
            template:
                "<div class='start_date flex items-center text-sm leading-5 text-gray-500 action-div'> #: (typeof(xero_invoice) != 'undefined' && xero_invoice !== null && xero_invoice.xero_synced_at !== null) ? convertJsDateTimeFormat(xero_invoice.xero_synced_at) : '--' # </div>",
            minResizableWidth: 100,
            minWidth: 120,
            sortable: false,
        },
        {
            // headerTemplate:
            //     "<a class='k-link text-xs font-medium tracking-wide leading-none text-gray-500' style='cursor: default !important;'>Action</a>",
            field: '',
            //title: "ACTIONS",
            filterable: false,
            sortable: false,
            minResizableWidth: 110,
            minWidth: 120,
            width: 120,
            template: function (dataItem) {
                return paymentScheduleManageActionTd(dataItem);
            },
            attributes: {
                class: 'tw-sticky-cell',
            },
            headerAttributes: {
                class: 'tw-sticky-header',
            },
        },
    ];
}

function manageXeroColumnsForScheduleGrid(isXeroConnect, closestGridID) {
    let closestGrid = $(closestGridID).data('kendoGrid');
    if (isXeroConnect == 1) {
        closestGrid.showColumn('xero_invoice');
        closestGrid.showColumn('xero_synced_at');
    } else {
        closestGrid.hideColumn('xero_invoice');
        closestGrid.hideColumn('xero_synced_at');
    }
}

function manageReScheduleButton(isVisible) {
    let installmentTab = $(document).find('.noInstallmentDiv');
    installmentTab.find('.openGenerateFeeScheduleBtn').toggleClass('hidden', isVisible);
    installmentTab.find('.reCreatePaymentScheduleBtn').toggleClass('hidden', !isVisible);
}

function manageRowBindForScheduleGrid(e) {
    $(e.sender.tbody)
        .find('tr')
        .each(function () {
            //TODO::GNG-3022 (Add class for red highlight data as cancelled)
            $(this).toggleClass('inActiveData', !isActiveCourse);

            $(this)
                .find('td:not(:first-child, :last-child)')
                .click(function () {
                    let rowData = e.sender.dataItem(this.parentElement);
                    rowData.xero_connect = isXeroConnectVal;
                    rowData.is_student_sync = isStudentSync;
                    singlePaymentHistoryList(rowData.id);
                    kendoWindowOpen(viewPaymentInfoModalId);
                    viewSchedulePayment(rowData);
                });
        });
}

function viewSchedulePayment(dataItem) {
    dataItem.invoiced_start_date = new Date(dataItem.invoiced_start_date);
    let viewSchedulePaymentInfoTemplate = kendo.template(
        $('#viewSchedulePaymentInfoTemplate').html()
    )(dataItem);
    $(document).find('#viewPaymentScheduleInfo').html(viewSchedulePaymentInfoTemplate);
}

function agentCommForScheduleRecord(dataItem) {
    const { bgColor, payStatus, paidCommission } = getAgentCommissionStatus(dataItem);
    let agentCommVal = kendo.toString(parseFloat(paidCommission), 'c');
    let agentPaidDetailIcon = '';
    if (dataItem.payment_status == 'paid') {
        //onclick="event.stopPropagation();"
        agentPaidDetailIcon = `<div class="items-center justify-start cursor-pointer showStudentAgentCommInfo" 
                                    data-student-agent-commission-id="${dataItem.stud_agent_comm_id}" >
                                    <i class="fa fa-info-circle text-blue-500" aria-hidden="true" title="Associated Agent Details" data-tooltip="Associated Agent Details"></i>
                                </div>`;
    }
    return `<div class="action-div flex items-center space-x-2">
            <div class='items-center text-sm leading-5 font-normal text-gray-600'>${agentCommVal}</div>
            <div class='inline-flex items-center justify-center px-3 bg-${bgColor}-100 rounded-full'>
                <span class='text-xs leading-6 text-center text-${bgColor}-800'>${capitalizeFirstCharacter(payStatus)}</span>
            </div>
            ${agentPaidDetailIcon}
        </div>`;
}

function getAgentCommissionStatus(dataItem) {
    let commission = dataItem.commission ?? 0;
    let paidCommission = dataItem.paid_commission ?? 0;
    let isApproved = dataItem.is_approved ?? 0;

    let bgColor = 'gray';
    let payStatus = 'Unpaid';

    if (isApproved == 0) {
        bgColor = 'red';
        payStatus = 'Not Approved';
    } else if (isApproved == 1 && paidCommission == 0) {
        bgColor = 'green';
        payStatus = 'Approved';
    } else if (isApproved == 1 && paidCommission > 0 && paidCommission >= commission) {
        bgColor = 'green';
        payStatus = 'Paid';
    } else if (isApproved == 1 && paidCommission > 0) {
        bgColor = 'yellow';
        payStatus = 'Partially Paid';
    } else {
        bgColor = 'gray';
        payStatus = 'Unpaid';
    }

    return { bgColor, payStatus, paidCommission };
}

function paymentScheduleManageActionTd(dataItem) {
    let recordPayButton = '';
    let editPayIcon = '';
    let editActionShow = false;
    if (dataItem.payment_status != 'paid') {
        editActionShow = true;
        if (isXeroConnectVal == 0) {
            // recordPayButton = `<button type="button" data-id="${dataItem.id}" class="recordPaymentBtn btn-primary text-xs h-6 px-2"><p class="text-xs leading-5 text-white uppercase truncate">Record Payment</p></button>`;
            recordPayButton = kendo.template($('#recordPaymentButton').html())({
                id: dataItem.id,
            });
            $('.bulkSyncPaymentBtn').hide();
        }

        if (isXeroConnectVal == 1) {
            $('.bulkSyncPaymentBtn').show();

            if (typeof dataItem.xero_invoice != 'undefined' && dataItem.xero_invoice !== null) {
                if (
                    dataItem.xero_invoice.xero_invoice_status == 'DRAFT' ||
                    dataItem.xero_invoice.xero_invoice_status == 'AUTHORISED' ||
                    dataItem.xero_invoice.xero_invoice_status == 'PAID'
                ) {
                    editActionShow = false;
                }
            }
        }
        /*if (isXeroConnectVal === 1 && dataItem?.xero_invoice?.xero_invoice_status !== null) {
            editActionShow = !["DRAFT", "PAID"].includes(dataItem.xero_invoice.xero_invoice_status);
        }*/
    }

    if (dataItem.payment_status == 'partially paid') {
        editActionShow = false;
    }

    if (editActionShow) {
        editPayIcon = `<button type="button" data-id="${dataItem.id}" title="Edit Payment" class="editSchedulePaymentInfoBtn w-6 h-6 tw-btn-action flex items-center text-center glob-tooltip"><span class="k-icon k-i-edit k-icon-edit"></span></button>`;
    }

    return (
        `<div class="tw-action-group flex flex-row justify-end items-center gap-2">
            ` +
        recordPayButton +
        editPayIcon +
        `
            <div class="action-schedule tw-btn-action tw-action--autohide glob-tooltip" data-id="${dataItem.id}" title="More options">
                <span class="k-icon k-i-more-horizontal" style="color: #9CA3AF;"></span>
            </div>
        </div>`
    );
}

function onBoundInstallment() {
    $(document).find('.installmmentLoadingDiv').hide();
    $('.searchInputField').val('');
    setTimeout(function () {
        //$(pScheduleGridId).data("kendoGrid").dataSource.filter([]);
        setTimeout(function () {
            let hasInstallments = $(pScheduleGridId).data('kendoGrid').dataSource.view().length > 0;
            $(document).find('.noInstallmentDiv').toggleClass('hidden', hasInstallments);
            $(document).find('.existInstallmentDiv').toggleClass('hidden', !hasInstallments);
        });
    });
}

function onBoundPrepayment() {
    $(document).find('.prepaymentLoadingDiv').hide();
    $('.searchInputField').val('');
    setTimeout(function () {
        //$(pInitialPayGridId).data("kendoGrid").dataSource.filter([]);
        setTimeout(function () {
            let hasPrepayments =
                $(pInitialPayGridId).data('kendoGrid').dataSource.view().length > 0;
            $(document).find('.noPrepaymentData').toggleClass('hidden', hasPrepayments);
            $(document).find('.prePaymentListDiv').toggleClass('hidden', !hasPrepayments);
        });
    });
}

function singlePaymentHistoryList(primaryID) {
    $(singlePaymentHistoryGridId)
        .html('')
        .kendoGrid({
            dataSource: {
                type: 'json',
                transport: {
                    read: {
                        url: site_url + 'api/view-payment-schedule-details',
                        dataType: 'json',
                        type: 'POST',
                        data: { detailId: primaryID },
                    },
                },
                requestEnd: function (e) {
                    if (e.type === 'read' && e.response) {
                        isXeroConnectVal = e.response.data.xeroConnect;
                        let currentGrid = $(singlePaymentHistoryGridId).data('kendoGrid');
                        if (isXeroConnectVal == 1) {
                            currentGrid.showColumn('xero_synced_at');
                        } else {
                            currentGrid.hideColumn('xero_synced_at');
                        }
                    }
                },
                schema: defaultSchema({
                    transection_no: { type: 'string' },
                    receipt_no: { type: 'string' },
                    payment_date: { type: 'string' },
                    paid_amount: { type: 'number' },
                    deposited_amount: { type: 'integer' },
                    scholarship: { type: 'integer' },
                    payment_mode: { type: 'string' },
                    remarks: { type: 'string' },
                    bank_deposit_date: { type: 'string' },
                }),
                pageSize: 10,
                serverPaging: true,
                serverFiltering: true,
                serverSorting: true,
            },
            pageable: customPageableArr(),
            dataBound: onBoundPaymentScheduleView,
            sortable: false,
            resizable: true,
            selectable: false,
            columns: [
                {
                    field: 'transection_no',
                    title: 'TRANS NO',
                    template:
                        "<div class='action-div flex items-center text-sm leading-5 font-normal text-gray-500'>#: transection_no #</div>",
                    width: 100,
                },
                {
                    field: 'receipt_no',
                    title: 'RECEIPT NO',
                    template:
                        "<div class='action-div flex items-center text-sm leading-5 font-normal text-gray-500'>#: receipt_no #</div>",
                    width: 100,
                },
                {
                    field: 'reversed',
                    title: 'STATUS',
                    template: function (dataItem) {
                        let bgColor = dataItem.reversed == 0 ? 'green' : 'yellow';
                        return (
                            `<div class='inline-flex items-center justify-center px-3 py-1 bg-${bgColor}-100 rounded-full'><span class='text-xs leading-5 text-center text-${bgColor}-800 truncate'>` +
                            (dataItem.reversed == 0 ? 'Paid' : 'Reversed') +
                            `</span></div>`
                        );
                    },
                    width: 100,
                },
                {
                    field: 'payment_date',
                    title: 'PAYMENT DATE',
                    template:
                        "<div class='start_date flex items-center text-sm leading-5 text-gray-500 action-div '> #: payment_date # </div>",
                    width: 100,
                },
                {
                    field: 'paid_amount',
                    title: 'PAID AMOUNT',
                    template:
                        "<div class='action-div flex items-center text-sm leading-5 font-normal text-gray-500'>#:  kendo.toString(paid_amount, 'c') #</div>",
                    width: 100,
                },
                {
                    field: 'deposited_amount',
                    title: 'DEPOSITED AMOUNT',
                    template:
                        "<div class='action-div flex items-center text-sm leading-5 font-normal text-gray-500'>#: kendo.toString(deposited_amount, 'c') #</div>",
                    width: 100,
                },
                {
                    field: 'student_credit',
                    title: 'USED CREDIT',
                    template:
                        "<div class='action-div flex items-center text-sm leading-5 font-normal text-gray-500'>#: kendo.toString(student_credit, 'c') #</div>",
                    width: 100,
                },
                {
                    field: 'scholarship',
                    title: 'USED SCHOLARSHIP',
                    template:
                        "<div class='action-div flex items-center text-sm leading-5 font-normal text-gray-500'>#: kendo.toString(scholarship, 'c') #</div>",
                    width: 100,
                },
                {
                    field: 'payment_mode',
                    title: 'PAY MODE',
                    template:
                        "<div class='action-div flex items-center text-sm leading-5 font-normal text-gray-500'>#: payment_mode #</div>",
                    width: 100,
                },
                {
                    field: 'remarks',
                    title: 'REMARKS',
                    template:
                        "<div class='action-div flex items-center text-sm leading-5 font-normal text-gray-500'>#: ((remarks !=null) ? remarks :'---') #</div>",
                    width: 100,
                },
                {
                    field: 'bank_deposit_date',
                    title: 'BANK DEPOSITED DATE',
                    template:
                        "<div class='action-div flex items-center text-sm leading-5 font-normal text-gray-500'>#: ((bank_deposit_date !=null) ? bank_deposit_date :'---') #</div>",
                    width: 100,
                },
                {
                    field: 'amount_refund',
                    title: 'REFUND AMOUNT',
                    template:
                        "<div class='action-div flex items-center text-sm leading-5 font-normal text-gray-500'>#: kendo.toString(amount_refund, 'c')  #</div>",
                    width: 100,
                },
                {
                    field: 'xero_synced_at',
                    headerTemplate:
                        "<a class='k-link text-xs font-medium tracking-wide leading-none text-gray-500 uppercase' style='cursor: default !important;'>XERO SYNC TIME</a>",
                    sortable: false,
                    //template: "<div class='start_date flex items-center text-sm leading-5 text-gray-500 action-div'> #: (typeof(xero_data) != 'undefined' && xero_data !== null && xero_data.xero_synced_at !== null) ? convertJsDateTimeFormat(xero_data.xero_synced_at) : '--' # </div>",
                    template: function (dataItem) {
                        let paid_at = (() => {
                            try {
                                const xero_paid_data = JSON.parse(dataItem.xero_data || '{}');
                                return xero_paid_data.xero_synced_at
                                    ? convertJsDateTimeFormat(xero_paid_data.xero_synced_at)
                                    : '--';
                            } catch {
                                return '--';
                            }
                        })();
                        return `<div class='start_date flex items-center text-sm leading-5 text-gray-500 action-div'>${paid_at}</div>`;
                    },
                    width: 100,
                },
                {
                    //headerTemplate: "<a class='k-link text-xs font-medium tracking-wide leading-none text-gray-500 uppercase' style='cursor: default !important;'>ACTION</a>",
                    title: 'ACTION',
                    width: 80,
                    template: function (dataItem) {
                        return paymentHistoryManageActionTd(dataItem.id);
                    },
                    filterable: false,
                    sortable: false,
                },
            ],
            noRecords: noRecordTemplate(),
        })
        .data('kendoGrid');
}

function paymentHistoryManageActionTd(id) {
    return `<div class="tw-action tw-action--autohide text-center">
          <a class="tw-btn-action" href="javascript:void(0);" data-id="${id}" aria-label="Expand" tabindex="-1">
            <span class="k-icon k-i-more-horizontal"></span>
          </a>
      </div>`;
}

function onBoundPaymentScheduleView(e) {
    // let hasData = $(singlePaymentHistoryGridId).data('kendoGrid').dataSource.total() > 0;
    // $(document).find('.paymentScheduleViewAllInfoList').toggleClass('hidden', hasData);

    if ($(singlePaymentHistoryGridId).data('kendoGrid').dataSource.total() > 0) {
        $(document).find('.paymentScheduleViewAllInfoList').show();
    } else {
        $(document).find('.paymentScheduleViewAllInfoList').hide();
    }
}

function deleteInstallmentPayment() {
    $('#deleteInstallmentPaymentForm')
        .html('')
        .kendoForm({
            validatable: defaultErrorTemplate(),
            orientation: 'vertical',
            items: [
                {
                    field: 'reason',
                    label: 'Reason to Delete',
                    //label: "<p class='w-64 text-sm font-medium leading-5 text-gray-500>Reason to Delete</p>",
                    validation: { required: { message: 'Enter reason' } },
                },
            ],
            buttonsTemplate:
                '<div class="modal-footer w-full inline-flex space-x-4 items-center justify-end py-5 bottom-0 right-0 fixed border-t bg-white px-6">\n' +
                '<div class="float-right flex space-x-4 items-center justify-end">\n' +
                '<button type="button" class="px-6 py-2 btn-secondary cancelBtn" type="button">\n' +
                '<p type="button" class="text-sm font-medium leading-4 text-gray-700">Cancel</p>\n' +
                '</button>\n' +
                '<button type="button" class="saveDeleteInstallmentForm px-3 py-2 btn-danger">\n' +
                '<p class="text-sm font-medium leading-4 text-white">Confirm Delete</p>\n' +
                '</button>\n' +
                '</div>\n' +
                '</div>',
        });
}

function recreateInstallmentOrPrePayment(formType) {
    let selectedDataArr = {
        college_id: collegeId,
        student_id: studentId,
        student_course_id: selectedStudCourseID,
        form_type: `re-create-${formType}`,
        type_name: capitalizeFirstCharacter(formType),
    };
    ajaxActionV2(
        'api/isvalid-remaining-payment-schedule',
        'POST',
        selectedDataArr,
        function (response) {
            if (response.status == 'error') {
                notificationDisplay(response.message, '', response.status);
                return false;
            }
            getExistingScheduleData(selectedDataArr);
        }
    );
}

function reCreateFeeSchedule(type, typeName = 'Installment') {
    let reCreatePaymentScheduleForm = $(reCreatePayScheduleFormId)
        .html('')
        .kendoForm({
            validatable: defaultErrorTemplate(),
            orientation: 'vertical',
            layout: 'grid',
            type: 'group',
            formData: {
                day_frequency: isHigherEd ? 3 : 1,
                no_of_installment: isHigherEd ? 6 : 1,
                no_of_frequency: isHigherEd ? 6 : 1,
                set_due_days: '0',
            },
            grid: { cols: 6, gutter: 16 },
            items: [
                {
                    field: 'installment_start_date',
                    colSpan: 3,
                    label: isHigherEd ? 'Semester Start Date' : `${typeName} Start Date`,
                    editor: customDateEditor,
                    dateFormat: dateFormatFrontSideJS,
                    validation: { required: true },
                },
                {
                    field: 'remaining_amount',
                    label: `Remaining Amount To ${typeName}`,
                    editor: customNumberInput,
                    colSpan: 3,
                    validation: { required: true },
                    hint: 'Remaining amount from partially paid invoices cannot be rescheduled.',
                    //hint: `Total amount of unpaid ${typeName} and the pending amount of the ${typeName}.`
                },
                {
                    field: 'no_of_installment',
                    editor: 'DropDownList',
                    label: isHigherEd ? 'No of Semester' : 'No of Installment',
                    colSpan: isHigherEd ? 2 : 1,
                    editorOptions: manageEditorOptions('get-installment-number', ''),
                    validation: { required: true },
                },
                {
                    field: 'no_of_frequency',
                    label: isHigherEd ? 'No of month in Semester' : 'Will Pay Every',
                    editor: customNumberInput,
                    colSpan: isHigherEd ? 2 : 1,
                    validation: { required: true },
                },
                {
                    field: 'day_frequency',
                    label: 'For',
                    colSpan: 1,
                    editor: 'DropDownList',
                    editorOptions: manageEditorOptionsWithDefaultOption(
                        'get-constant-data',
                        { action: 'arrPaidDuration' },
                        false
                    ),
                    //validation: { required: true },
                    //validation: { required: () => !isHigherEd },
                    /*validation: {
                        required: function (input) {
                            return isHigherEd === true; // Explicitly check for true
                        }
                    },*/
                },
                {
                    field: 'set_due_days',
                    label: 'Set Due Days',
                    colSpan: 2,
                    editor: customNumberInput,
                    validation: { required: true },
                    hint: 'Days After Invoice start date.',
                },
                {
                    field: 'form_type',
                    label: '',
                    colSpan: 1,
                },
            ],
            /*validateField: function(e) {
                let value = parseFloat(e.value);
                if ((e.field == "remaining_amount" || e.field == "no_of_frequency") && (value <= 0 || isNaN(value))) {
                    e.preventDefault();
                    let errorMsg = (e.field == "remaining_amount")
                        ? "Amount must be greater than 0."
                        : "Frequency value must be greater than 0.";
                    notificationDisplay(errorMsg, "", "error");
                }
            },*/
        });
    reCreatePaymentScheduleForm
        .find('#installment_start_date')
        .addClass('setNewGenerateScheduleData');
    reCreatePaymentScheduleForm.find('#remaining_amount').addClass('setNewGenerateScheduleData');
    reCreatePaymentScheduleForm.find('#no_of_frequency').addClass('setNewGenerateScheduleData');
    reCreatePaymentScheduleForm.find('#no_of_installment').addClass('setNewGenerateScheduleData');
    reCreatePaymentScheduleForm.find('#day_frequency').addClass('setNewGenerateScheduleData');
    reCreatePaymentScheduleForm.find('#set_due_days').addClass('setNewGenerateScheduleData');

    reCreatePaymentScheduleForm.find('#no_of_frequency').val(isHigherEd ? 6 : 1);
    reCreatePaymentScheduleForm.find('#form_type').hide();
    reCreatePaymentScheduleForm.find('#form_type').val(type);

    /*if(isHigherEd){
        reCreatePaymentScheduleForm.find("#day_frequency").data("kendoDropDownList").value(3);
    }*/

    reCreatePaymentScheduleForm.find('#day_frequency').closest('.k-form-field').toggle(!isHigherEd);
}

function getExistingScheduleData(selectedDataArr) {
    kendoWindowOpen(
        reCreatePayScheduleModalId,
        `Recreate ${selectedDataArr.type_name} With Remaining Amount`
    );
    reCreateFeeSchedule(selectedDataArr.form_type, selectedDataArr.type_name);

    setTimeout(function () {
        $(document).find(reCreatePayScheduleModalId).find('.k-form-buttons').hide();
        //$("#newDataAppend").hide();
        $('#newDataAppend').addClass('hidden');
    }, 100);

    ajaxActionV2(
        'api/get-generated-payment-schedule-data',
        'POST',
        selectedDataArr,
        function (response) {
            var responseArr = response.data;
            //$(document).find('.upfront_fee_paid').text(kendo.toString(responseArr.upfront_fee_pay, "c"))
            let commonDiv = kendo.template($('#commonHeadForReCreatePaymentScheduleDiv').html())(
                responseArr
            );
            $(document).find('.commonHeadForReCreatePaymentSchedule').html(commonDiv);

            $(reCreatePayScheduleFormId)
                .find('#remaining_amount')
                .val(responseArr.remaining_amount);
            $(reCreatePayScheduleFormId)
                .find('#new_installment_amount')
                .val(responseArr.installment_amount);
            $(reCreatePayScheduleFormId)
                .find('#no_of_installment')
                .data('kendoDropDownList')
                .value(responseArr.remaining_installment);
            $(reCreatePayScheduleFormId)
                .find('#day_frequency')
                .data('kendoDropDownList')
                .value(responseArr.paid_duration_day);
            $(reCreatePayScheduleFormId).find('#set_due_days').val(responseArr.invoice_due_days);

            $(reCreatePayScheduleModalId)
                .find('.remaining_installment_amount')
                .text(responseArr.installment_amount);
            $(reCreatePayScheduleModalId)
                .find('.number_of_remaining_installment')
                .text(responseArr.remaining_installment);
            $(reCreatePayScheduleModalId)
                .find('.remaining_frequency_for_installment')
                .text(responseArr.installment_type);
            $(reCreatePayScheduleModalId)
                .find('#existing_applied_commission')
                .val(responseArr.commission_value);
            $(reCreatePayScheduleModalId).find('#existing_gst').val(responseArr.gst);
            $(reCreatePayScheduleModalId)
                .find('#total_remaining_amount')
                .val(responseArr.remaining_amount);
            $(reCreatePayScheduleModalId).find('#check_start_date').val(responseArr.start_date);
            $(reCreatePayScheduleModalId).find('#check_finish_date').val(responseArr.finish_date);
        }
    );
}

function installmentsGridHide() {
    setTimeout(function () {
        const hasInstallments =
            $('#paymentScheduleList').data('kendoGrid').dataSource.view().length > 0;
        $(document).find('.noInstallmentDiv').toggleClass('hidden', hasInstallments);
        $(document).find('.existInstallmentDiv').toggleClass('hidden', !hasInstallments);
    });
}

function prePaymentGridHide() {
    setTimeout(function () {
        const hasPrePayments =
            $('#initialPaymentList').data('kendoGrid').dataSource.view().length > 0;
        $(document).find('.noPrepaymentData').toggleClass('hidden', hasPrePayments);
        $(document).find('.prePaymentListDiv').toggleClass('hidden', !hasPrePayments);
    });
}

function checkScheduleDateValidation() {
    let scheduleStartDate = normalizeDate(
        new Date(
            $('#editPaymentScheduleForm')
                .find('#edit_invoiced_start_date')
                .data('kendoDatePicker')
                .value()
        )
    );
    let courseStartDate = normalizeDate(
        new Date($('#editSchedulePaymentInfoModal').find('#hide_course_start_date').val())
    );

    if (scheduleStartDate < courseStartDate) {
        $(editScheduleDateWarningModalId).data('kendoDialog').open();
        return false;
    }
    return true;
}

function updateSchedulePaymentData() {
    let dataArr = formValidateAndReturnFormData('#editSchedulePaymentInfoModal');
    startAjaxLoader();
    if (dataArr) {
        ajaxActionV2('api/save-edit-payment-schedule', 'POST', dataArr, function (response) {
            notificationDisplay(response.message, '', response.status);
            if (response.status == 'success') {
                $('#editSchedulePaymentInfoModal').data('kendoWindow').close();
                paymentsCardUpdate();
                refreshBulkGrid();
                //refreshGrid2(singlePaymentHistoryGridId);
            }
            stopAjaxLoader();
        });
    }
}

function checkReCreatePaymentScheduleDate() {
    let scheduleStartDate = normalizeDate(
        new Date(
            $(reCreatePayScheduleFormId)
                .find('#installment_start_date')
                .data('kendoDatePicker')
                .value()
        )
    );
    let courseStartDate = normalizeDate(
        new Date($(reCreatePayScheduleModalId).find('#check_start_date').val())
    );

    if (scheduleStartDate < courseStartDate) {
        $(reCreateScheduleDateWarningModalId).data('kendoDialog').open();
        return false;
    }
    return true;
}

function saveReCreatePaymentScheduleData() {
    let tempSelectedDataArr = selectedDataArr;
    let dataArr = formValidateAndReturnFormData(reCreatePayScheduleModalId);
    let higherEdStatus = { is_higher_ed: isHigherEd };
    var opts = {};
    $.extend(opts, dataArr, tempSelectedDataArr, higherEdStatus);
    if (opts) {
        ajaxActionV2('api/save-regenerate-payment-schedule', 'POST', opts, function (response) {
            notificationDisplay(response.message, '', response.status);
            if (response.status == 'success') {
                $(reCreatePayScheduleModalId).data('kendoWindow').close();
                paymentsCardUpdate();
                refreshBulkGrid();
                installmentsGridHide();
            }
        });
    }
}

function getExportPaymentScheduleData(gridId) {
    let grid = $(gridId).data('kendoGrid');
    let selectedRows = grid.select();
    let selectedData = [];

    selectedRows.each(function (index, row) {
        let dataItem = grid.dataItem(row);

        // Add computed field for "Agent Commission" status
        if (dataItem.commission !== undefined) {
            let { bgColor, payStatus, paidCommission } = getAgentCommissionStatus(dataItem);
            dataItem.paid_agent_commission = paidCommission;
            dataItem.agent_commission_status = payStatus;
        } else {
            dataItem.paid_agent_commission = 'N/A';
            dataItem.agent_commission_status = 'N/A';
        }

        dataItem.invoiced_start_date = dateFormatForExport(
            dataItem.invoiced_start_date,
            displayDateFormatJS
        );
        dataItem.due_date = dateFormatForExport(dataItem.due_date, displayDateFormatJS);

        getSyncedAtForExport(dataItem);

        selectedData.push(dataItem);
    });

    if (selectedData.length === 0) {
        notificationDisplay('Please select at least one row.', '', 'error');
        return false;
    }

    let columns = grid.columns
        .map(function (column) {
            switch (column.field) {
                case 'commission':
                    return {
                        field: 'commission',
                        title: 'Agent Commission Value',
                    };
                case 'xero_invoice':
                case 'xero_synced_at':
                    return checkAndReturnXeroColumn(column.field);
                    break;
                default:
                    return column;
            }
        })
        .filter((column) => column !== undefined);

    columns.push(
        { field: 'paid_agent_commission', title: 'Paid Agent Commission' },
        { field: 'agent_commission_status', title: 'Agent Commission Status' }
    );

    return { selectedData, columns };
}

$('body').on('click', '.viewStudentSchedulePaymentInfoBtn', function (e) {
    e.preventDefault();
    let primaryID = $(this).attr('data-id');
    kendoWindowOpen(viewPaymentInfoModalId);
    singlePaymentHistoryList(primaryID);
    let dataArr = {
        detailId: primaryID,
        studentId: studentId,
        student_course_id: selectedStudCourseID,
    };
    ajaxActionV2('api/get-payment-schedule-details', 'POST', dataArr, function (response) {
        viewSchedulePayment(response.data[0]);
    });
});

$('body').on('click', '#exportPaymentSchedule', function (e) {
    e.preventDefault();
    let result = getExportPaymentScheduleData(pScheduleGridId);
    if (result) {
        const { selectedData, columns } = result;
        exportToExcel(selectedData, columns, 'Schedule Payment List');
    }
    //gridExportExcelData(pScheduleGridId, "Schedule Payment List");
});

$('body').on('click', '#exportPaymentInstallment', function (e) {
    e.preventDefault();
    let result = getExportPaymentScheduleData(pInitialPayGridId);
    if (result) {
        const { selectedData, columns } = result;
        exportToExcel(selectedData, columns, 'Initial Payment List');
    }
    //gridExportExcelData(pInitialPayGridId, "Initial Payment List");
});

$('body').on('click', '.deletePaymentScheduleBtn', function (e) {
    e.preventDefault();
    let primaryID = $(this).attr('data-id');
    if (primaryID > 0) {
        ajaxActionV2(
            'api/delete-installment-transaction',
            'POST',
            { detailId: primaryID },
            function (response) {
                let deleteData = response.data;
                let deletePaymentScheduleModal = $(document).find('#deletePaymentScheduleModal');

                deletePaymentScheduleModal.find('#transactionId').val(deleteData.id);
                deletePaymentScheduleModal.find('.trans_no').text(deleteData.id);
                deletePaymentScheduleModal.find('.receipt_no').text(deleteData.resceipt_number);
                deletePaymentScheduleModal
                    .find('.payment_amount')
                    .text(kendo.toString(deleteData.upfront_fee_to_pay, 'c'));
                deletePaymentScheduleModal.find('.gst').text();
                deletePaymentScheduleModal
                    .find('.due_date')
                    .text(
                        kendo.toString(kendo.parseDate(deleteData.due_date), dateFormatFrontSideJS)
                    );
                deletePaymentScheduleModal.find('.date_of_payment').text(deleteData.paid_on);
                deletePaymentScheduleModal.find('.payment_mode').text(deleteData.payment_mode_name);
                deletePaymentScheduleModal.find('.payment_type').text(deleteData.payment_type);
                deletePaymentScheduleModal.find('.remarks').text(deleteData.remarks);

                kendoWindowOpen('#deletePaymentScheduleModal');
                deleteInstallmentPayment();
                // paymentsCardUpdate();
            }
        );
    }
});

$('body').on('click', '.saveDeleteInstallmentForm', function (e) {
    let dataArr = formValidateAndReturnFormData('#deletePaymentScheduleModal');
    if (dataArr) {
        $(document).find('.saveDeleteInstallmentForm').prop('disabled', true);
        ajaxActionV2('api/delete-payment-schedule', 'POST', dataArr, function (response) {
            $(document).find('.saveDeleteInstallmentForm').prop('disabled', false);
            notificationDisplay(response.message, '', response.status);
            if (response.status == 'success') {
                $('#deletePaymentScheduleModal').data('kendoWindow').close();
                refreshBulkGrid();
                prePaymentGridHide();
                installmentsGridHide();
                paymentsCardUpdate();
            }
            //refreshGrid2(singlePaymentHistoryGridId);
        });
    }
});

$('body').on(
    'click',
    '.syncToXeroPaymentScheduleBtn',
    withActiveCourseCheck(function (e) {
        e.preventDefault();
        let primaryID = $(e.currentTarget).attr('data-id');
        $('#syncToXeroPaymentScheduleModal').data('kendoDialog').open();
        $('#syncToXeroPaymentScheduleModal').find('#syncToXeroPaymentScheduleId').val(primaryID);
    })
);

$('body').on(
    'click',
    '.syncFromXeroPaymentScheduleBtn',
    withActiveCourseCheck(function (e) {
        e.preventDefault();
        let primaryID = $(e.currentTarget).attr('data-id');
        $('#syncFromXeroPaymentScheduleModal').data('kendoDialog').open();
        $('#syncFromXeroPaymentScheduleModal')
            .find('#syncFromXeroPaymentScheduleId')
            .val(primaryID);
    })
);

$('body').on(
    'click',
    '.deletePaymentTransactionBtn',
    withActiveCourseCheck(function (e) {
        e.preventDefault();
        let primaryID = $(e.currentTarget).attr('data-id');
        var deletePaymentHistoryForm = $('#deletePaymentHistoryForm').kendoForm({
            validatable: defaultErrorTemplate(),
            orientation: 'vertical',
            items: [
                {
                    field: 'reason_to_delete',
                    id: 'reason_to_delete',
                    label: 'Reason For Delete?',
                    editor: 'TextArea',
                    editorOptions: { rows: 2 },
                    validation: { required: true },
                },
            ],
            buttonsTemplate:
                '<div class="modal-footer w-full inline-flex space-x-4 items-center justify-end py-5 bottom-0 right-0 fixed border-t bg-white px-6">\n' +
                '<div class="float-right flex space-x-4 items-center justify-end">\n' +
                '<button type="button" class="flex justify-center px-6 py-2 bg-white shadow border hover:shadow-lg rounded-lg  border-gray-300 focus:ring-2 focus:ring-offset-2 focus:ring-offset-white focus:ring-gray-400 cancelBtn" type="button">\n' +
                '<p type="button" class="text-sm font-medium leading-4 text-gray-700">CANCEL</p>\n' +
                '</button>\n' +
                '<input type="hidden" name="transaction_id" id="transaction_id" value="" /><button type="button" class="deletePaymentTransaction flex justify-center h-full px-3 py-2 bg-primary-blue-500 hover:shadow-lg rounded-lg focus:ring-2 focus:ring-offset-2 focus:ring-offset-white focus:ring-primary-blue-600">\n' +
                '<p class="text-sm font-medium leading-4 text-white">CONFIRM DELETE</p>\n' +
                '</button>\n' +
                '</div>\n' +
                '</div>',
            submit: function (ev) {
                ev.preventDefault();
                // saveRefundRequestForm('refundPaymentForm', 'refundPaymentModal', res.id);
            },
        });
        deletePaymentHistoryForm.data('kendoForm').setOptions({
            formData: {
                transaction_id: $(e.currentTarget).attr('data-id'),
            },
        });
        ajaxActionV2(
            'api/get-payment-transaction-data',
            'POST',
            { detailId: primaryID, canDelete: true },
            function (response) {
                if (response.status == 'error') {
                    notificationDisplay(response.message, '', response.status);
                    return false;
                }
                let res = response.data.transactionData[0];
                let deletePaymentTransactionModal = $(document).find(
                    '#deletePaymentTransactionModal'
                );
                deletePaymentTransactionModal.find('.transactionNo').text(res.transection_no);
                deletePaymentTransactionModal.find('.receiptNo').text(res.receipt_no);
                deletePaymentTransactionModal
                    .find('.paymentDate')
                    //.text(res.payment_date);
                    .text(kendo.toString(kendo.parseDate(res.payment_date), displayDateFormatJS));
                deletePaymentTransactionModal
                    .find('.totalPaidAmount')
                    .text(kendo.toString(res.paid_amount, 'c'));
                deletePaymentTransactionModal
                    .find('.refundAmount')
                    .text(kendo.toString(res.amount_refund, 'c'));
                deletePaymentTransactionModal
                    .find('.remarks')
                    .text(res.remarks != null ? res.remarks : '--');
                deletePaymentTransactionModal.find('#transaction_id').val(primaryID);
                //deletePaymentTransactionModal.find(".commissionPayable").text(kendo.toString(res.commission_payable || 0, "c"));
                //deletePaymentTransactionModal.find(".commissionPaid").text(kendo.toString(res.commission_paid || 0, "c"));
                kendoWindowOpen('#deletePaymentTransactionModal');
            }
        );
    })
);

$('body').on('click', '.deletePaymentTransaction', function (e) {
    e.preventDefault();
    let dataArr = formValidateAndReturnFormData('#deletePaymentHistoryForm');
    if (dataArr) {
        $(document).find('.deletePaymentTransaction').prop('disabled', true);
        ajaxActionV2('api/delete-payment-transaction', 'POST', dataArr, function (response) {
            $(document).find('.deletePaymentTransaction').prop('disabled', false);
            notificationDisplay(response.message, '', response.status);
            if (response.status == 'success') {
                paymentsCardUpdate();
                refreshBulkGrid();
                refreshGrid2(singlePaymentHistoryGridId);
                $('#deletePaymentTransactionModal').data('kendoWindow').close();
            }
        });
    }
});

$('body').on(
    'click',
    '.feeRefundDetailsBtn',
    withActiveCourseCheck(function (e) {
        e.preventDefault();
        let primaryID = $(e.currentTarget).attr('data-id');
        let studentRefundDetailsForm = $('#studentRefundDetailsForm')
            .html('')
            .kendoForm({
                validatable: defaultErrorTemplate(),
                orientation: 'vertical',
                items: [
                    {
                        field: 'transaction_number',
                        editor: 'MaskedTextBox',
                        label: 'Transaction Number',
                        attributes: { readOnly: true },
                    },
                    {
                        field: 'paid_amount',
                        editor: 'MaskedTextBox',
                        label: 'Student Paid Amount',
                        attributes: { readOnly: true },
                    },
                    {
                        field: 'student_refund_amount',
                        editor: customNumberInput,
                        label: 'Student Refund Amount',
                        validation: { required: true },
                        hint: '**Agent commission calculated automatic',
                    },
                    {
                        field: 'administration_cost',
                        editor: customNumberInput,
                        label: 'Administration Cost',
                        validation: { required: true },
                    },
                    {
                        field: 'student_refund_mode',
                        label: 'Student Refund Mode',
                        validation: { required: true },
                        editor: 'DropDownList',
                        editorOptions: {
                            optionLabel: 'Select Mode',
                            dataSource: getDropdownDataSource('get-payment-mode-list'),
                            dataTextField: 'Name',
                            dataValueField: 'Id',
                        },
                    },
                    {
                        field: 'student_refund_date',
                        label: 'Student Refund Date',
                        editor: customDateEditor,
                        dateFormat: dateFormatFrontSideJS,
                        validation: { required: true },
                    },
                ],
                buttonsTemplate: '',
            });
        let agentRefundDetailsForm = $('#agentRefundDetailsForm')
            .html('')
            .kendoForm({
                validatable: defaultErrorTemplate(),
                orientation: 'vertical',
                items: [
                    {
                        field: 'agent_commission_refunds',
                        // editor: customNumberInput,
                        editor: 'MaskedTextBox',
                        label: 'Agent Commission Refund Amount',
                        attributes: { readOnly: true },
                    },
                    {
                        field: 'gstRefundAmount',
                        editor: 'MaskedTextBox',
                        label: 'GST Refund Amount',
                        attributes: { readOnly: true },
                    },
                    {
                        field: 'agent_refunded',
                        // editor: customNumberInput,
                        editor: 'MaskedTextBox',
                        label: 'Agent Refunded',
                        attributes: { readOnly: true },
                    },
                    {
                        field: 'agent_refund_mode',
                        label: 'Agent Refund Mode',
                        validation: { required: true },
                        editor: 'DropDownList',
                        editorOptions: {
                            optionLabel: 'Select Mode',
                            dataSource: getDropdownDataSource('get-payment-mode-list'),
                            dataTextField: 'Name',
                            dataValueField: 'Id',
                        },
                    },
                    {
                        field: 'agent_refund_date',
                        label: 'Agent Refund Date',
                        editor: customDateEditor,
                        dateFormat: dateFormatFrontSideJS,
                        validation: { required: true },
                    },
                    {
                        field: 'remarks',
                        label: 'Remarks',
                        editor: 'TextArea',
                        validation: { required: true },
                        editorOptions: {
                            placeholder: 'Write comment...',
                            rows: 4,
                        },
                    },
                    {
                        field: 'student_net_received',
                        label: 'Student Net Received',
                        editor: customNumberInput,
                        validation: { required: true },
                    },
                ],
                buttonsTemplate:
                    '<input type="hidden" name="transaction_id" id="transaction_id" value="" />\n' +
                    '                <input type="hidden" name="agent_bonus" id="agent_bonus" value="" />\n' +
                    '                <input type="hidden" name="bonus_gst" id="bonus_gst" value="" />\n' +
                    '                <input type="hidden" name="gst_refund_amount" id="gst_refund_amount" value="" />',
            });
        ajaxActionV2(
            'api/get-payment-transaction-data',
            'POST',
            { detailId: primaryID },
            function (response) {
                let data = response.data;
                let bonusGST = data.transactionData[0].GST;
                let gstAmount =
                    bonusGST == 'GST'
                        ? (parseFloat(data.transactionData[0].agent_commission_refund_amount) *
                              10) /
                          100
                        : 0;
                let agent_refunded =
                    gstAmount + data.transactionData[0].agent_commission_refund_amount;
                studentRefundDetailsForm.data('kendoForm').setOptions({
                    formData: {
                        transaction_number: data.transactionData[0].transection_no,
                        paid_amount: data.transactionData[0].paid_amount,
                        student_refund_amount: data.transactionData[0].amount_refund,
                        administration_cost: data.transactionData[0].student_administration_cost,
                        student_refund_date: data.transactionData[0].student_refund_date,
                        student_refund_mode: data.transactionData[0].student_refund_mode,
                    },
                });
                agentRefundDetailsForm.data('kendoForm').setOptions({
                    formData: {
                        transaction_id: primaryID,
                        gstRefundAmount: gstAmount,
                        agent_commission_refunds:
                            data.transactionData[0].agent_commission_refund_amount,
                        agent_refunded: agent_refunded,
                        student_refund_amount: data.transactionData[0].amount_refund,
                        agent_refund_mode: data.transactionData[0].agent_refund_mode,
                        agent_refund_date: data.transactionData[0].agent_refund_date,
                        remarks: data.transactionData[0].refund_remarks,
                        student_net_receive: data.transactionData[0].student_net_receive,
                        agent_bonus: data.transactionData[0].commission_value,
                        bonus_gst: data.transactionData[0].GST,
                    },
                });
                kendoWindowOpen('#feeRefundDetailModal');
            }
        );
    })
);

$('body').on('click', '.feeRefundSave', function (e) {
    e.preventDefault();
    $(document)
        .find('#feeRefundDetailModal')
        .find('#gst_refund_amount')
        .val($(document).find('#feeRefundDetailModal').find('#gstRefundAmount').val());
    let dataArr = formValidateAndReturnFormData('#feeRefundDetailModal');
    if (dataArr) {
        ajaxActionV2('api/save-payment-refund-details', 'POST', dataArr, function (response) {
            notificationDisplay(response.message, '', response.status);
            if (response.status == 'success') {
                paymentsCardUpdate();
                refreshBulkGrid();
                refreshGrid2(singlePaymentHistoryGridId);
                $('#feeRefundDetailModal').data('kendoWindow').close();
            }
        });
    }
});

$('body').on(
    'click',
    '.paymentTransactionReverseBtn',
    withActiveCourseCheck(function (e) {
        e.preventDefault();
        let primaryID = $(e.currentTarget).attr('data-id');
        let paymentTransactionReverseModal = $(document).find('#paymentTransactionReverseModal');
        paymentTransactionReverseModal.find('#transaction_id').val(primaryID);
        paymentTransactionReverseModal
            .find('.invoice')
            .text($(e.currentTarget).attr('data-formatted-invoice'));
        paymentTransactionReverseModal
            .find('.paid_amount')
            .text('$' + $(e.currentTarget).attr('data-paid_amount'));
        paymentTransactionReverseModal
            .find('.deposited_amount')
            .text('$' + $(e.currentTarget).attr('data-deposited_amount'));
        paymentTransactionReverseModal
            .find('.payment_mode')
            .text($(e.currentTarget).attr('data-payment_mode'));
        paymentTransactionReverseModal
            .find('.payment_date')
            .text($(e.currentTarget).attr('data-payment_date'));
        setKendoTextArea('#reverse_comment', 'Write Comment...', 5, '');
        kendoWindowOpen('#paymentTransactionReverseModal');
    })
);

$('body').on(
    'click',
    '.paymentHistoryRevertPayment',
    withActiveCourseCheck(function (e) {
        e.preventDefault();
        let primaryID = $(e.currentTarget).attr('data-id');
        ajaxActionV2(
            'api/revert-payment-history',
            'POST',
            { primaryID: primaryID },
            function (response) {
                notificationDisplay(response.message, '', response.status);
                if (response.status == 'success') {
                    refreshGrid2(singlePaymentHistoryGridId);
                    paymentsCardUpdate();
                    refreshBulkGrid();
                }
            }
        );
    })
);

$('body').on('click', '.paymentTransactionReverse', function (e) {
    e.preventDefault();
    let dataArr = formValidateAndReturnFormData('#paymentTransactionReverseForm');
    if (dataArr) {
        ajaxActionV2('api/save-payment-transaction-reverse', 'POST', dataArr, function (response) {
            notificationDisplay(response.message, '', response.status);
            if (response.status == 'success') {
                paymentsCardUpdate();
                refreshBulkGrid();
                refreshGrid2(singlePaymentHistoryGridId);
                $('#paymentTransactionReverseModal').data('kendoWindow').close();
            }
        });
    }
});

$('body').on('click', '.closeAddRecordPayment', function (e) {
    e.preventDefault();
    $('#recordPaymentModal').data('kendoWindow').close();
});

$('body').on('click', '.closeAddNewPaymentSchedule', function (e) {
    e.preventDefault();
    $('#addNewPaymentScheduleModal').data('kendoWindow').close();
});

$('body').on('click', '.editSchedulePayment', function (e) {
    e.preventDefault();

    let validator = defaultFormValidator('#editPaymentScheduleForm');
    if (!validator.validate()) {
        return false;
    }

    if (!checkScheduleDateValidation()) {
        return false;
    }

    updateSchedulePaymentData();
});

$('body').on(
    'click',
    '.editSchedulePaymentInfoBtn',
    withActiveCourseCheck(function (e) {
        e.preventDefault();
        let primaryID = $(e.currentTarget).attr('data-id');
        if (primaryID > 0) {
            startAjaxLoader();
            ajaxActionV2(
                'api/get-payment-schedule-details',
                'POST',
                { detailId: primaryID },
                function (response) {
                    let editData = response.data[0];
                    kendoWindowOpen('#editSchedulePaymentInfoModal');
                    let editSchedulePaymentModal = $(document).find(
                        '#editSchedulePaymentInfoModal'
                    );
                    let editSchedulePayment = $(document).find('#editPaymentScheduleForm');
                    editSchedulePaymentModal.find('#editSchedulePaymentInfoId').val(primaryID);
                    editSchedulePaymentModal.find('#payment_status').val(editData.payment_status);
                    editSchedulePaymentModal.find('#upfront_fee_pay').val(editData.upfront_fee_pay);
                    editSchedulePaymentModal
                        .find('#hide_course_start_date')
                        .val(editData.course_start_date);
                    editSchedulePayment.find('#edit_agent_name').val(editData.agency_name);
                    editSchedulePayment
                        .find('#edit_agent_name')
                        .attr('style', 'border-width: 0px !important');
                    editSchedulePayment
                        .find('#edit_due_date')
                        .data('kendoDatePicker')
                        .value(editData.due_date);
                    editSchedulePayment
                        .find('#edit_invoiced_start_date')
                        .data('kendoDatePicker')
                        .value(editData.invoiced_start_date);
                    editSchedulePayment
                        .find('#edit_agent_commission')
                        .val(editData.commission_value);
                    editSchedulePaymentModal
                        .find('#edit_hidden_agent_commission')
                        .val(editData.commission_value);
                    editSchedulePaymentModal.find('#edit_agent_id').val(editData.agent_id);
                    editSchedulePayment.find('#edit_amount').val(editData.upfront_fee_to_pay);
                    //editSchedulePayment.find("#edit_paid_duration_text").val(editData.paid_duration_text); // For Day/week/month/Year //TODO::GNG-2874
                    //editSchedulePayment.find("#edit_paid_duration_day").data("kendoDropDownList").value(editData.paid_duration_day);  //TODO::GNG-2874
                    editSchedulePayment.find('#edit_remarks').val(editData.remarks);
                    editSchedulePayment.find('.k-form-field-wrap').addClass('customSwitchButton');

                    if (editData.GST == 'GST') {
                        $('#edit_GST').data('kendoSwitch').check(true);
                    } else {
                        $('#edit_GST').data('kendoSwitch').check(false);
                    }
                    stopAjaxLoader();
                }
            );
        }
    })
);

$('body').on(
    'click',
    '.editPaymentHistoryBtn',
    withActiveCourseCheck(function (e) {
        e.preventDefault();
        let primaryID = $(e.currentTarget).attr('data-id');
        if (primaryID > 0) {
            startAjaxLoader();
            ajaxActionV2(
                'api/get-payment-transaction-details',
                'POST',
                { detailId: primaryID },
                function (response) {
                    let data = response.data;
                    kendoWindowOpen('#editPaymentHistoryModal');
                    let editPaymentHistory = $(document).find('#editPaymentHistoryForm');
                    editPaymentHistory.find('#transaction_id').val(primaryID);
                    editPaymentHistory
                        .find('#initial_payment_detail_id')
                        .val(data.initial_payment_detail_id);
                    editPaymentHistory.find('#receipt_no').val(data.receipt_no);
                    editPaymentHistory.find('#transection_no').val(data.transection_no);
                    editPaymentHistory.find('#agent_bonus').val(data.agent_bonus);
                    editPaymentHistory.find('#bad_debt').val(data.bad_debt);
                    editPaymentHistory.find('#deposited_amount').val(data.deposited_amount);
                    editPaymentHistory.find('#upfront_fee_to_pay').val(data.upfront_fee_to_pay);
                    editPaymentHistory.find('#transection_amount').val(data.paid_amount);
                    editPaymentHistory
                        .find('#payment_date')
                        .data('kendoDatePicker')
                        .value(data.payment_date);
                    editPaymentHistory
                        .find('#bank_deposit_date')
                        .data('kendoDatePicker')
                        .value(data.bank_deposit_date);
                    editPaymentHistory
                        .find('#bonus_paid_date')
                        .data('kendoDatePicker')
                        .value(data.bonus_paid_date);
                    editPaymentHistory
                        .find('#receipt_sent_date')
                        .data('kendoDatePicker')
                        .value(data.receipt_sent_date);
                    editPaymentHistory
                        .find('#payment_mode')
                        .data('kendoDropDownList')
                        .value(data.payment_mode);
                    editPaymentHistory.find('#remarks').val(data.remarks);
                    // editPaymentHistory.find('.k-form-field-wrap').addClass('customSwitchButton');
                    if (data.bonus_gst == 'GST') {
                        editPaymentHistory.find('#GST').data('kendoSwitch').check(true);
                    } else {
                        editPaymentHistory.find('#GST').data('kendoSwitch').check(false);
                    }
                    if (data.paidCommission > 0) {
                        editPaymentHistory
                            .find('#commission_after_bonus')
                            .data('kendoSwitch')
                            .check(true);
                    } else {
                        editPaymentHistory
                            .find('#commission_after_bonus')
                            .data('kendoSwitch')
                            .check(false);
                    }
                    if (data.receipt_sent == '1') {
                        editPaymentHistory.find('#receipt_sent').data('kendoSwitch').check(true);
                    } else {
                        editPaymentHistory.find('#receipt_sent').data('kendoSwitch').check(false);
                    }
                    stopAjaxLoader();
                }
            );
        }
    })
);

$('body').on('click', '.editPaymentHistory', function (e) {
    e.preventDefault();
    let dataArr = formValidateAndReturnFormData('#editPaymentHistoryForm');
    if (dataArr) {
        ajaxActionV2('api/save-edit-payment-transaction', 'POST', dataArr, function (response) {
            notificationDisplay(response.message, '', response.status);
            if (response.status == 'success') {
                paymentsCardUpdate();
                $('#editPaymentHistoryModal').data('kendoWindow').close();
                refreshGrid2(singlePaymentHistoryGridId);
            }
        });
    }
});

$('body').on(
    'click',
    '.sendMailBtn',
    withActiveCourseCheck(function (e) {
        e.preventDefault();
        kendoWindowOpen('#sendMailStudentModal');
    })
);

$('body').on('click', '.saveInvoiceCredit', function (e) {
    e.preventDefault();
    let dataArr = formValidateAndReturnFormData('#addInvoiceCreditForm');
    if (dataArr) {
        ajaxActionV2('api/save-invoice-credit-details', 'POST', dataArr, function (response) {
            notificationDisplay(response.message, '', response.status);
            if (response.status == 'success') {
                $('#addInvoiceCreditModal').data('kendoWindow').close();
                refreshBulkGrid();
                paymentsCardUpdate();
            }
        });
    }
});

$('body').on(
    'click',
    '.reCreatePaymentScheduleBtn',
    withActiveCourseCheck(function (e) {
        e.preventDefault();
        recreateInstallmentOrPrePayment('installment');
    })
);

$('body').on(
    'click',
    '.reCreatePrePaymentScheduleBtn',
    withActiveCourseCheck(function (e) {
        e.preventDefault();
        recreateInstallmentOrPrePayment('pre-payment');
    })
);

$('body').on('click', '.saveReCreateScheduleBtn', function (e) {
    e.preventDefault();
    let tempSelectedDataArr = selectedDataArr;
    let dataArr = formValidateAndReturnFormData(reCreatePayScheduleModalId);

    if (!dataArr) {
        return false;
    }

    if (dataArr.remaining_amount <= 0) {
        notificationDisplay('Amount must be greater than 0.', '', 'error');
        return false;
    }

    if (!checkReCreatePaymentScheduleDate()) {
        return false;
    }

    saveReCreatePaymentScheduleData();
});
