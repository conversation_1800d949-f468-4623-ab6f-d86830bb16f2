<?php

namespace App\Console\Commands;

use App\Model\v2\Tenant;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class CleanOldSessions extends Command
{
    protected $signature = 'sessions:clean-old';

    protected $description = 'Clean sessions older than one month for all tenants';

    public function handle()
    {
        $oneMonthAgo = Carbon::now()->subMonth();
        foreach (Tenant::all() as $tenant) {
            $tenant->run(function ($tenant) use ($oneMonthAgo) {

                DB::table('sessions')
                    ->where('last_activity', '<', $oneMonthAgo->timestamp)
                    ->delete();
            });
        }

        $this->info('Old sessions cleaned successfully.');
    }
}
