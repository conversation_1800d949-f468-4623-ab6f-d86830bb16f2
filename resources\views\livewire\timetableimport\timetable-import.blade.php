<div class="min-h-screen bg-gray-100" x-data="{
        helpModalOpen: false,
        errorModalOpen: false,
        deleteModalOpen: false,
        errorMessage: '',
        errorId: null,
        errorParts: [],
        recordToDelete: null,

        showErrorDetails(id, message) {
            this.errorId = id;
            this.errorMessage = message;

            // Try to parse the message as JSON
            try {
                const parsedErrors = JSON.parse(message);
                if (Array.isArray(parsedErrors)) {
                    // If it's a JSON array, use it directly
                    this.errorParts = parsedErrors;
                } else {
                    // If it's JSON but not an array, create a single item array
                    this.errorParts = [parsedErrors];
                }
            } catch (e) {
                // If it's not valid JSON, fall back to the old comma-separated behavior
                this.errorParts = message.split(',').filter(part => part.trim() !== '');
            }

            this.errorModalOpen = true;
        },

        viewDetails(id) {
            // Use Livewire to fetch the data - Livewire 3 syntax
            window.dispatchEvent(new CustomEvent('open-timetable-modal'));
            Livewire.dispatch('viewDetails', { id: id });
        },

        editDetails(id) {
        console.log('idssss', id)
            // Use Livewire to fetch the data - Livewire 3 syntax
            window.dispatchEvent(new CustomEvent('open-timetable-edit-modal'));
            Livewire.dispatch('edit', { id: id });
        },

        confirmDelete(id) {
            this.recordToDelete = id;
            this.deleteModalOpen = true;
        },

        executeDelete() {
            if (this.recordToDelete) {
                // Use Livewire 3 syntax
                Livewire.dispatch('deleteRecord', { id: this.recordToDelete });

                this.deleteModalOpen = false;
                this.recordToDelete = null;
            }
        },

        init() {
            // Handle escape key to close modals
            document.addEventListener('keydown', e => {
                if (e.key === 'Escape') {
                    this.helpModalOpen = false;
                    this.errorModalOpen = false;
                    this.deleteModalOpen = false;
                }
            });
        }
    }">
    <div class="max-w-7xl mx-auto">
        <!-- Header Section -->
        <div class="bg-white rounded-lg shadow-sm p-6 mb-6 space-y-4">
            <div class="flex items-center justify-between">
                <h2 class="text-2xl font-bold text-gray-800">Timetable Import</h2>

                <!-- Help Button -->
                <button type="button" @click="helpModalOpen = true"
                    class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    <svg class="w-5 h-5 mr-1 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24"
                        xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    Need Help?
                </button>
            </div>

            <!-- Custom CSS for Kendo UI uploader and Alpine.js -->
            <style>
                /* Alpine.js cloak to prevent flash of unstyled content */
                [x-cloak] {
                    display: none !important;
                }

                /* Make the select button more prominent */
                .k-upload-button {
                    background-color: var(--color-primary-blue-500) !important;
                    color: white !important;
                    border-color: var(--color-primary-blue-500) !important;
                    font-weight: 500 !important;
                    padding: 0.5rem 1rem !important;
                    border-radius: 0.375rem !important;
                    cursor: pointer !important;
                    transition: all 0.2s ease-in-out !important;
                }

                .k-upload-button:hover {
                    background-color: var(--color-primary-blue-500) !important;
                    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06) !important;
                }

                /* Improve the dropzone appearance */
                .k-dropzone {
                    border: 2px dashed #d1d5db !important;
                    padding: 1.5rem !important;
                    border-radius: 0.5rem !important;
                    background-color: #f9fafb !important;
                    margin-bottom: 1rem !important;
                }

                .k-dropzone:hover {
                    border-color: #9ca3af !important;
                    background-color: #f3f4f6 !important;
                }

                /* Fix any disabled appearance */
                .k-upload-button.k-state-disabled {
                    opacity: 1 !important;
                    pointer-events: auto !important;
                }

                #csv_file {
                    height: 100%;
                    overflow: hidden;
                    width: 100%;
                }
            </style>

            <!-- File Upload Section -->
            <div>
                <div class="tw-async-upload" id="tw-async-upload" wire:ignore>
                    <!-- Using wire:ignore to prevent Livewire from re-rendering this section -->
                    <input name="csv_file" id="csv_file" type="file" aria-label="files" />
                </div>
                <div class="flex space-x-4 mt-4">
                    <button type="button" wire:click="downloadSampleCsv" class="tw-btn-primary">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                        </svg>
                        Download Sample CSV
                    </button>
                </div>
            </div>

            @if (session()->has('message'))
            <div class="bg-green-100 border-l-4 border-green-500 text-green-700 p-4 mb-6" role="alert">
                <p>{{ session('message') }}</p>
            </div>
            @endif
        </div>

        <!-- Table Section -->
        <div class="bg-white rounded-lg shadow-sm overflow-hidden">
            <div class="px-6 py-4 border-b border-gray-200 max-md:flex-col flex justify-between items-start gap-4">
                <div>
                    <h3 class="text-lg font-semibold text-gray-800">Imported Records</h3>
                    <p class="text-sm text-gray-500 mt-1">
                        Records are sorted by date, showing
                        {{ $sortDirection === 'desc' ? 'the most recent imports first' : 'the oldest imports first' }}
                        (click <span class="font-medium">'Imported At'</span> heading to change)
                    </p>
                </div>
                <div class="flex flex-col justify-start items-end gap-2 max-md:w-full">
                    <div class="relative">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"
                            class="absolute left-2 top-1/2 -translate-y-1/2 w-6 h-6 text-gray-400">
                            <path
                                d="M10.5 5C13.5376 5 16 7.46243 16 10.5C16 11.8388 15.5217 13.0659 14.7266 14.0196L18.8536 18.1464C19.0488 18.3417 19.0488 18.6583 18.8536 18.8536C18.68 19.0271 18.4106 19.0464 18.2157 18.9114L18.1464 18.8536L14.0196 14.7266C13.0659 15.5217 11.8388 16 10.5 16C7.46243 16 5 13.5376 5 10.5C5 7.46243 7.46243 5 10.5 5ZM10.5 6C8.01472 6 6 8.01472 6 10.5C6 12.9853 8.01472 15 10.5 15C12.9853 15 15 12.9853 15 10.5C15 8.01472 12.9853 6 10.5 6Z"
                                fill="#9CA3AF" />
                        </svg>
                        <input type="text"
                            class="pr-8 pl-8 border border-gray-300 rounded-md py-2 px-3 focus:outline-none focus:ring-2 focus:ring-blue-500"
                            placeholder="Search text" wire:model.live.debounce.500ms="searchTerm" />
                    </div>
                    <div class="flex items-center space-x-2">
                        <span class="text-sm text-gray-500" wire:loading wire:target="poll">
                            <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-blue-500 inline"
                                xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor"
                                    stroke-width="4">
                                </circle>
                                <path class="opacity-75" fill="currentColor"
                                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z">
                                </path>
                            </svg>
                            Refreshing...
                        </span>
                        <button wire:click="togglePolling"
                            class="px-3 py-1 text-xs rounded-md {{ $isPolling ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800' }}">
                            {{ $isPolling ? 'Auto-refresh On' : 'Auto-refresh Off' }}
                        </button>
                    </div>

                </div>

            </div>

            @if($isPolling)
            <div wire:poll.10000ms="poll"></div>
            @endif

            <!-- Kendo UI Grid Table -->
            <div class="p-0">
                <x-v2.grid-table id="timetable-import-grid" wire:ignore>
                </x-v2.grid-table>
            </div>

            {{-- <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th
                                class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider min-w-fit">
                                Import Id</th>
                            <th
                                class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider min-w-fit">
                                Imported at
                                <button wire:click="toggleSortDirection"
                                    class="inline-flex items-center ml-1 text-blue-600 focus:outline-none"
                                    title="{{ $sortDirection === 'desc' ? 'Sorted by newest first (click to reverse)' : 'Sorted by oldest first (click to reverse)' }}">
                                    <span wire:loading.remove wire:target="toggleSortDirection">
                                        @if($sortDirection === 'desc')
                                        <!-- Down arrow - newest first -->
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"
                                            xmlns="http://www.w3.org/2000/svg">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M3 4h13M3 8h9m-9 4h6m4 0l4-4m0 0l4 4m-4-4v12"></path>
                                        </svg>
                                        @else
                                        <!-- Up arrow - oldest first -->
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"
                                            xmlns="http://www.w3.org/2000/svg">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M3 4h13M3 8h9m-9 4h9m5-4v12m0 0l-4-4m4 4l4-4"></path>
                                        </svg>
                                        @endif
                                    </span>
                                    <span wire:loading wire:target="toggleSortDirection">
                                        <!-- Loading spinner -->
                                        <svg class="animate-spin w-4 h-4" fill="none" stroke="currentColor"
                                            viewBox="0 0 24 24">
                                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor"
                                                stroke-width="4"></circle>
                                            <path class="opacity-75" fill="currentColor"
                                                d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z">
                                            </path>
                                        </svg>
                                    </span>
                                </button>
                            </th>
                            <th
                                class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider min-w-fit">
                                Imported By</th>
                            <th
                                class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider min-w-fit">
                                Campus</th>
                            <th
                                class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider min-w-fit">
                                Semester</th>
                            <th
                                class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider min-w-fit">
                                Batch</th>
                            <th
                                class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider min-w-fit">
                                Start Week</th>
                            <th
                                class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider min-w-fit">
                                End Week</th>
                            <th
                                class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider min-w-fit">
                                Subject Code</th>
                            <th
                                class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider min-w-fit">
                                Teacher Code</th>
                            <th
                                class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider min-w-fit">
                                Room Code</th>
                            <th
                                class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider min-w-fit">
                                Status</th>
                            <th
                                class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Actions</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        @forelse ($rows as $i => $row)
                        <tr
                            class="{{ $row->status === 'completed' ? 'bg-gray-50 hover:bg-gray-100' : 'hover:bg-gray-50' }}">
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ $row->import_batch_id ?? ''
                                }}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                {{ $row->created_at ? $row->created_at->format('d-m-Y g:i A') : '' }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ $row->creator->name ?? ''
                                }}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ $row->campus ?? '' }}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ $row->semester ?? '' }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ $row->batch ?? '' }}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                {{ $row->start_week ? date('d-m-Y', strtotime($row->start_week)) : '' }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                {{ $row->end_week ? date('d-m-Y', strtotime($row->end_week)) : '' }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ $row->subject ?? '' }}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ $row->teacher ?? '' }}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ $row->room_code ?? '' }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full
                                        {{ $row->status === 'completed' ? 'bg-green-100 text-green-800' :
                                           ($row->status === 'error' ? 'bg-red-100 text-red-800' :
                                           'bg-yellow-100 text-yellow-800') }}">
                                    {{ ucfirst($row->status) }}
                                </span>
                                @if($row->error_message)
                                <button type="button"
                                    class="ml-2 text-xs text-red-600 hover:text-red-800 focus:outline-none"
                                    @click="showErrorDetails('{{ $row->id }}', '{{ addslashes($row->error_message) }}')">
                                    <svg class="inline-block w-4 h-4" fill="none" stroke="currentColor"
                                        viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                    View Error
                                </button>
                                @endif
                            </td>

                            <td class="px-6 py-4 whitespace-nowrap text-sm text-center">
                                <div class="flex justify-center space-x-2">
                                    <!-- View Details button available for all records -->
                                    <button @click="viewDetails({{ $row->id }})"
                                        class="text-indigo-600 hover:text-indigo-900" title="View Details">
                                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                                        </svg>
                                    </button>

                                    @if($row->status !== 'completed')
                                    <!-- Actions only available for non-completed records -->
                                    <button wire:click="edit({{ $row->id }})" class="text-blue-600 hover:text-blue-900"
                                        title="Edit">
                                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                                        </svg>
                                    </button>
                                    <button @click="confirmDelete({{ $row->id }})"
                                        class="text-red-600 hover:text-red-900" title="Delete">
                                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                        </svg>
                                    </button>
                                    <button wire:click="resync({{ $row->id }})"
                                        class="text-green-600 hover:text-green-900 relative group"
                                        title="Re-import (auto-refresh will be enabled)">
                                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                                        </svg>
                                        <span
                                            class="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 text-xs text-white bg-gray-700 rounded-md opacity-0 group-hover:opacity-100 transition-opacity duration-300 whitespace-nowrap">
                                            Re-import (auto-refresh will be enabled)
                                        </span>
                                    </button>
                                    @endif
                                </div>
                            </td>
                        </tr>
                        @empty
                        <tr>
                            <td colspan="12" class="px-6 py-4 text-center text-sm text-gray-500">
                                No records imported yet.
                            </td>
                        </tr>
                        @endforelse
                    </tbody>
                </table>
            </div> --}}

            <div class="px-6 py-4 border-t border-gray-200">
                {{ $rows->links() }}
            </div>
        </div>
    </div>

    <livewire:timetableimport.timetable-import-edit-modal />
    <livewire:timetableimport.timetable-import-details-modal />

    <!-- Error Details Modal -->
    <div x-cloak x-show="errorModalOpen" x-transition:enter="transition ease-out duration-300"
        x-transition:enter-start="opacity-0" x-transition:enter-end="opacity-100"
        x-transition:leave="transition ease-in duration-200" x-transition:leave-start="opacity-100"
        x-transition:leave-end="opacity-0"
        class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
        <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white"
            @click.outside="errorModalOpen = false">
            <div class="flex justify-between items-center border-b pb-3">
                <h3 class="text-xl font-semibold text-gray-700">Error Details</h3>
                <button @click="errorModalOpen = false" class="text-gray-400 hover:text-gray-500">
                    <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12">
                        </path>
                    </svg>
                </button>
            </div>

            <div class="mt-4">
                <div class="bg-red-50 border-l-4 border-red-500 p-4">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <svg class="h-5 w-5 text-red-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                            </svg>
                        </div>
                        <div class="ml-3">
                            <!-- JSON formatted errors -->
                            <div
                                x-show="Array.isArray(errorParts) && errorParts.length > 0 && typeof errorParts[0] === 'object'">
                                <ul class="text-sm text-red-700 list-disc pl-5 space-y-3">
                                    <template x-for="(error, index) in errorParts" :key="index">
                                        <li>
                                            <span x-show="error.error_title" class="font-semibold"
                                                x-text="error.error_title"></span>
                                            <span x-show="error.error_title">: </span>
                                            <span x-text="error.error_description || error"></span>
                                        </li>
                                    </template>
                                </ul>
                            </div>

                            <!-- Legacy format: Single error message -->
                            <div class="text-sm text-red-700"
                                x-show="!Array.isArray(errorParts) || (errorParts.length <= 1 && typeof errorParts[0] !== 'object')"
                                x-text="errorMessage">
                            </div>

                            <!-- Legacy format: Multiple error messages as bullet points -->
                            <ul class="text-sm text-red-700 list-disc pl-5 space-y-1"
                                x-show="Array.isArray(errorParts) && errorParts.length > 1 && typeof errorParts[0] !== 'object'">
                                <template x-for="part in errorParts" :key="part">
                                    <li x-text="typeof part === 'string' ? part.trim() : part"></li>
                                </template>
                            </ul>
                        </div>
                    </div>
                </div>

                <div class="mt-6 flex justify-end">
                    <button @click="errorModalOpen = false"
                        class="px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-400">
                        Close
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div x-cloak x-show="deleteModalOpen" x-transition:enter="transition ease-out duration-300"
        x-transition:enter-start="opacity-0" x-transition:enter-end="opacity-100"
        x-transition:leave="transition ease-in duration-200" x-transition:leave-start="opacity-100"
        x-transition:leave-end="opacity-0"
        class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
        <div class="relative top-1/2 -translate-y-1/2 mx-auto p-5 border w-11/12 md:w-2/5 lg:w-1/3 shadow-lg rounded-md bg-white"
            @click.outside="deleteModalOpen = false">
            <div class="flex flex-col items-center gap-4">
                <div class="flex gap-3">
                    <div class="w-6 h-6 rounded-full flex items-center justify-center bg-red-200">
                        <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path
                                d="M7.99934 6.19998V7.39998M7.99934 9.79998H8.00534M3.84242 12.2H12.1563C13.08 12.2 13.6574 11.2 13.1955 10.4L9.03857 3.19998C8.57669 2.39998 7.42199 2.39998 6.96011 3.19998L2.80318 10.4C2.3413 11.2 2.91865 12.2 3.84242 12.2Z"
                                stroke="#EF4444" stroke-width="1.2" stroke-linecap="round" stroke-linejoin="round" />
                        </svg>
                    </div>
                    <div class="space-y-2">
                        <h3 class="text-lg font-bold text-gray-800 mb-2">Are you sure?</h3>
                        <p class="text-sm text-gray-600 mb-6">
                            Do you really want to delete this record? This process cannot be undone.</p>
                    </div>

                </div>
                <div class="flex gap-4 items-center justify-end w-full">
                    <button @click="deleteModalOpen = false" class="tw-btn-secondary min-w-[120px]">
                        Cancel
                    </button>
                    <button @click="executeDelete()" class="tw-btn-danger min-w-[120px]">
                        Yes, Delete
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Help Modal -->
    <div x-cloak x-show="helpModalOpen" x-transition:enter="transition ease-out duration-300"
        x-transition:enter-start="opacity-0" x-transition:enter-end="opacity-100"
        x-transition:leave="transition ease-in duration-200" x-transition:leave-start="opacity-100"
        x-transition:leave-end="opacity-0"
        class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
        <div class="relative flex flex-col top-12 mx-auto border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white h-[calc(100vh-100px)] overflow-y-auto"
            @click.outside="helpModalOpen = false">
            <div class="flex justify-between items-center border-b p-5 pb-3">
                <h3 class="text-xl font-semibold text-gray-700">Need Help?</h3>
                <button @click="helpModalOpen = false" class="text-gray-400 hover:text-gray-500">
                    <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12">
                        </path>
                    </svg>
                </button>
            </div>

            <div class="text-gray-600 flex-1 overflow-y-auto p-5 space-y-4">
                <p class="mb-4">To get started, download the sample CSV file below and use it as a template for
                    your data.</p>

                <div class="bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-4">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <svg class="h-5 w-5 text-yellow-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                            </svg>
                        </div>
                        <div class="ml-3">
                            <p class="text-sm text-yellow-700">
                                <strong>Important:</strong> Please make sure to follow the same format and
                                column headers as shown in the sample file when uploading your own CSV. This
                                ensures that your data is processed correctly.
                            </p>
                        </div>
                    </div>
                </div>

                <h4 class="font-medium text-gray-700 mb-2">Tips for a successful upload:</h4>
                <ul class="list-disc pl-5 mb-4 space-y-1">
                    <li>Please do not rename, remove, or rearrange column headers.</li>
                    <li>Keep the file in .csv format (Comma Separated Values).</li>
                    <li>Ensure there are no empty required fields.</li>
                    <li>Use plain text values only (avoid formulas or special characters).</li>
                </ul>

                <h4 class="font-medium text-gray-700 mb-2">Field Validation Rules:</h4>
                <div class="bg-blue-50 border-l-4 border-blue-400 p-4 mb-4">
                    <div class="ml-3">
                        <h5 class="font-semibold text-blue-800 mb-1">Days Field</h5>
                        <p class="text-sm text-blue-700 mb-2">
                            The Days field must contain valid day names separated by pipe (|) characters.
                        </p>
                        <ul class="list-disc pl-5 text-sm text-blue-700 mb-3">
                            <li>Valid day names: Monday, Tuesday, Wednesday, Thursday, Friday, Saturday, Sunday
                            </li>
                            <li>Example: "Monday|Wednesday|Friday"</li>
                        </ul>

                        <h5 class="font-semibold text-blue-800 mb-1">Class Type</h5>
                        <p class="text-sm text-blue-700 mb-2">
                            The Class Type must be one of the following values:
                        </p>
                        <ul class="list-disc pl-5 text-sm text-blue-700 mb-3">
                            @foreach(config('constants.arrClassType') as $key => $value)
                            <li>{{ $key }} - {{ $value }}</li>
                            @endforeach
                        </ul>

                        <h5 class="font-semibold text-blue-800 mb-1">Attendance Type</h5>
                        <p class="text-sm text-blue-700 mb-2">
                            The Attendance Type must be one of the following values:
                        </p>
                        <ul class="list-disc pl-5 text-sm text-blue-700 mb-3">
                            @foreach(config('constants.arrAttendanceType') as $key => $value)
                            <li>{{ $key }} - {{ $value }}</li>
                            @endforeach
                        </ul>

                        <h5 class="font-semibold text-blue-800 mb-1">Start Week and End Week</h5>
                        <p class="text-sm text-blue-700 mb-2">
                            The Start Week and End Week dates must meet the following requirements:
                        </p>
                        <ul class="list-disc pl-5 text-sm text-blue-700 mb-3">
                            <li>Dates must be in DD-MM-YYYY format (e.g., 30-10-2023)</li>
                            <li>Dates must be aligned with semester division weeks in the system</li>
                            <li><strong>Start Week must be earlier than End Week</strong> (e.g., 25-10-2023 to
                                30-10-2023)</li>
                            <li>Both dates must exist in the semester division for the specified term</li>
                        </ul>

                        <h5 class="font-semibold text-blue-800 mb-1">Start Time and Finish Time</h5>
                        <p class="text-sm text-blue-700 mb-2">
                            The Start Time and Finish Time must meet the following requirements:
                        </p>
                        <ul class="list-disc pl-5 text-sm text-blue-700">
                            <li>Times must be in HH:MM format with 2 digits for hours (e.g., 09:00, 14:30)</li>
                            <li>Start Time must be earlier than Finish Time</li>
                            <li>Times must be between 06:00 and 23:00 (6:00 AM to 11:00 PM)</li>
                        </ul>
                    </div>
                </div>

                <p>Once your file is ready, upload it here to import your data.</p>
            </div>
            <div class="flex justify-center p-5 border-t">
                <button wire:click="downloadSampleCsv" class="tw-btn-primary">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                    Download Sample CSV
                </button>
            </div>
        </div>
    </div>
</div>



<script id="status-template" type="text/x-kendo-template">
    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
        # if (status === 'completed') { #
            bg-green-100 text-green-800
        # } else if (status === 'error') { #
            bg-red-100 text-red-800
        # } else { #
            bg-yellow-100 text-yellow-800
        # } #">
        #: status.charAt(0).toUpperCase() + status.slice(1) #
    </span>

    {{-- # if (error_message) { #
        <button type="button"
            class="ml-2 text-xs text-red-600 hover:text-red-800 focus:outline-none"
            onclick="showErrorDetails('#= id #', '#= kendo.htmlEncode(error_message) #')">
            <svg class="inline-block w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                      d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            View Error
        </button>
    # } # --}}
</script>

<script id="action-template" type="text/x-kendo-template">
    <div class="flex justify-center space-x-2">
        <button @click="viewDetails(#= id #)" class="tw-btn-icon h-10 leading-10 h-fit text-gray-500 hover:text-indigo-900" title="View Details">
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
            </svg>
        </button>
    
        # if (status !== 'completed') { #
        <button @click="editDetails(#= id #)" class="tw-btn-icon h-10 leading-10 h-fit text-gray-500 hover:text-blue-900" title="Edit">
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
            </svg>
        </button>
        <button @click="confirmDelete(#= id #)" class="tw-btn-icon h-10 leading-10 h-fit text-gray-500 hover:text-red-900" title="Delete">
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
            </svg>
        </button>
        <button wire:click="resync(#= id #)" wire:target="resync(#= id #)" 
        wire:loading.attr="disabled" class="tw-btn-icon h-10 leading-10 h-fit text-green-600 hover:text-green-900 relative group"
            title="Re-import (auto-refresh will be enabled)">
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" wire:loading.class="animate-spin"
            wire:target="resync(#= id #)">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
            </svg>
            {{-- <span
                class="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 text-xs text-white bg-gray-700 rounded-md opacity-0 group-hover:opacity-100 transition-opacity duration-300 whitespace-nowrap">
                Re-import (auto-refresh will be enabled)
            </span> --}}
        </button>
        # } #
    </div>
</script>

<script>
    window.importedData = @json($rows);
</script>