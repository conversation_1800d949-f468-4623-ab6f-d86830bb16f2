import axios from 'axios';
import qs from 'qs';
import { usePage } from '@inertiajs/vue3';

const headers = {
    'X-Requested-With': 'XMLHttpRequest',
    'Content-Type': 'application/json',
    Accept: 'application/json',
};

const api = axios.create({
    paramsSerializer: (params) => {
        return qs.stringify(params, { arrayFormat: 'repeat' });
    },
    headers,
    baseURL: `${window.origin}/`,
});

const notify = (type, message, time = 1000) => {
    const typeMap = {
        success: 'axiosResponseSuccess',
        error: 'axiosResponseError',
        warning: 'axiosResponseWarning',
        info: 'axiosResponseInfo',
    };
    window['Fire'].emit(typeMap[type] || 'axiosResponseError', {
        message,
        title: message,
        time: time,
    });
};
const getMessage = (response) => {
    const messages = response?.data?.message;
    if (Array.isArray(messages)) {
        return messages.join(', ');
    } else if (typeof messages === 'string') {
        return messages;
    } else if (typeof messages === 'object') {
        return Object.values(messages).join(', ');
    }
    return null;
};

const getCode = (response) => {
    if (response.status !== 200) return response.status;
    return response.data.code;
};

const handelError = (response) => {
    const code = getCode(response);
    //start with 200
    if (`${code}`.startsWith('20')) return;
    if (response && code === 404) {
        notify('error', 'Resource not found. Contact System Admin.');
    } else if (response && code === 555) {
        notify('error', getMessage(response), 'negative');
    } else if (response && code === 401) {
        notify('error', getMessage(response), 'negative');
    } else if (response && code === 400) {
        notify('error', getMessage(response), 'negative');
    } else if (response && code === 4001) {
        notify('error', getMessage(response), 'negative');
    } else if (response && code === 500) {
        notify('error', 'Internal Server Error. Contact System Admin.');
    } else if (response && code === 403) {
        notify('error', 'Unauthorized Access.', 'negative');
    } else {
        notify('error', getMessage(response) ?? 'Something went wrong. Contact System Admin.');
    }
};
api.interceptors.response.use(
    function (response) {
        let message = getMessage(response);

        if (message && message.length > 0) {
            notify('success', message);
        }

        return response;
    },
    function (error) {
        handelError(error.response);
        return Promise.reject(error);
    }
);
export { api };
