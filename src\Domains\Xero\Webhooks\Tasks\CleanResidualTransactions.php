<?php

namespace Domains\Xero\Webhooks\Tasks;

use App\Exceptions\ApplicationException;
use App\Model\v2\StudentInitialPaymentDetails;
use Closure;
use Domains\Xero\DTO\InvoiceUpdatedPayload;
use Domains\Xero\Entities\Payment;
use Domains\Xero\Entities\Prepayment;
use Domains\Xero\Repositories\Prepayments;
use Illuminate\Support\Facades\DB;

class CleanResidualTransactions
{
    public function __invoke(InvoiceUpdatedPayload $payload, Closure $next)
    {
        $model = $payload->model;
        if ($model->isStudentPaymentDetails()) {
            $invoice = $payload->invoice;

            $studentPaymentDetail = $model->invoiceable;

            $totalPaid = $invoice->AmountCredited + $invoice->AmountPaid;
            $studentPaymentDetail->syncScheduleAmountAndStatusData($studentPaymentDetail->upfront_fee_to_pay, $totalPaid, StudentInitialPaymentDetails::STATUS_AUTHORISED);

            $ids = array_merge(
                collect($invoice->getPayments())->pluck('PaymentID')->toArray(),
                collect($invoice->getPrepayments())->pluck('PrepaymentID')->toArray(),
                collect($invoice->getOverpayments())->pluck('OverpaymentID')->toArray(),
                collect($invoice->getCreditNotes())->pluck('CreditNoteID')->toArray()
            );

            DB::beginTransaction();
            try {
                $res = $studentPaymentDetail->transactions()
                    ->where(function ($q) use ($ids) {
                        $q->notSyncedWithXero()
                            ->orWhereNotIn('xero_payment_id', $ids);
                    })->delete();
                DB::commit();
                return $res;
            } catch (\Exception $e) {
                DB::rollBack();
                echo $e->getMessage();
                throw new ApplicationException($e->getMessage());
            }
        }

        return $next($payload);
    }

}
