@extends('frontend.layouts.frontend')
@section('title', $pagetitle )

@section('content')
<script>
    var paramScript = "";
//dd = '{"aa":"aa","aa1":"aass1"}';
paramScript = '<?php echo $arrSlug;  ?>';
</script>
<style>
    table#GridView1 tr:first-child:hover {
        background-color: #f48024 !important;
    }

    .table-block th {
        color: #fcf4ff;
        border-width: 1px;
        border-style: solid;
        border-color: #ccc;
        background-color: #333;
        background-repeat: repeat-x;
        background-position: left top;
        font-family: "Trebuchet MS", Verdana, Arial, Helvetica, sans-serif;
        font-size: 11px;
        padding-top: 1px;
        padding-right: 4px;
        padding-bottom: 1px;
        padding-left: 4px;
        vertical-align: top;
    }
</style>
<!-- Content Wrapper. Contains page content -->
<div class="main-conntent">
    @section('content-header')

    @endsection

    @if ( $errors->count() > 0 )
    <section class="content server-side-validation">
        <div class="row">
            <div class="col-md-12">
                <p>The following errors have occurred:</p>
                <ul class="error-list">
                    @foreach( $errors->all() as $message )
                    <li>{{ $message }}</li>
                    @endforeach
                </ul>
            </div>
        </div>
    </section>
    @endif

    <!-- Main content -->
    <section class="content">
        <div class="row">
            <div class="col-md-12">
                <div class="box box-info">
                    <div class="custom-header">
                        <div class="row">
                            <div class="col-md-10">
                                <h3 class="box-title"> Edit PDF Template </h3>
                            </div>
                            <div class="col-md-2">
                                <span class="pull-right add-btn-block">
                                    <a href="javascript:;" class="link-black text-sm sample-pdf-template"
                                        data-original-title="View PDF Template Slug" data-toggle="tooltip">
                                        <div class="btn-add"><i class="far fa fa-file-text"></i></div>
                                    </a>
                                </span>
                            </div>
                        </div>
                    </div>
                    <div class="box-body">
                        <div class="row">
                            {{ Form::model(array('method' => 'post'), ['class' => 'form-horizontal vertical-add-form',
                            'id' => 'PdfTemplateForm']) }}
                            {{ Form::hidden('id', (empty($arrPdfTemplateContent->id)? null :$arrPdfTemplateContent->id),
                            array('class' => 'form-control','id'=>'id')) }}
                            <div class="col-md-12">
                                <div class="form-group">
                                    <label class="col-sm-4 control-label"> PDF Type : </label>
                                    <div class="col-sm-5">
                                        {{ Form::select('pdf_type', $arrPdfType, (empty($arrPdfTemplateContent->type)?
                                        null :$arrPdfTemplateContent->type), array('class' => 'form-control', 'id' =>
                                        'pdf_type','disabled'=>'disabled')) }}
                                        {{ Form::hidden('type', (empty($arrPdfTemplateContent->type)? null
                                        :$arrPdfTemplateContent->type), array('class' => 'form-control','id'=>'type'))
                                        }}
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label for="inputText" class="col-sm-4 control-label"> Body Message : <span id=""
                                            class="required-field">*<div></div></span></label>
                                    <div class="col-sm-12">
                                        {{ Form::textarea('pdf_template', $arrPdfTemplateContent->pdf_template,
                                        array('class' => 'form-control editor','id'=>'editor1')) }}
                                    </div>
                                </div>
                                <div class="col-sm-12 text-center padding-t-40">
                                    <input name="Add" class="btn btn-info" value="Update" type="submit">
                                    <input name="default" id="defaultSet" class="btn btn-info" type="button"
                                        value="Set default">
                                </div>
                            </div>
                            {{ Form::close() }}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>
<style>
    .error {
        border: 1px solid red !important;
    }
</style>
<!-- /.content-wrapper -->
<div id="defaultSetBody"></div>
<div class="modal fade" id="samplePdfTemplateModal" role="dialog">
    <div class="modal-dialog" style="width:80%">
        <div class="modal-content">
            <div class="modal-header">
                <button aria-label="Close" data-dismiss="modal" class="close" type="button">
                    <span aria-hidden="true">×</span>
                </button>
                <h4 class="modal-title"> View PDF Template Slug </h4>
            </div>
            <div class="modal-body">
                <div class="box box-info">
                    <div class="box-body">
                        <div class="row">
                            <div class="col-md-12">
                                <div class="table-responsive no-padding">
                                    <table class="table table-hover table-custom" width="50%">
                                        <tbody>
                                            <tr>
                                                <th>Slug</th>
                                                <th>Details</th>
                                            </tr>
                                            <tr>
                                                <th>{college_name}</th>
                                                <th>Name of college</th>
                                            </tr>
                                            <tr>
                                                <th>{college_legal_name}</th>
                                                <th>Name of college legal</th>
                                            </tr>
                                            <tr>
                                                <th>{college_ABN}</th>
                                                <th>ABN of college </th>
                                            </tr>
                                            <tr>
                                                <th>{college_CRICOS_code}</th>
                                                <th>CRICOS code of college</th>
                                            </tr>
                                            <tr>
                                                <th>{college_RTO_code}</th>
                                                <th>RTO code of college</th>
                                            </tr>
                                            <tr>
                                                <th>{college_street_address}</th>
                                                <th>Street address of college</th>
                                            </tr>
                                            <tr>
                                                <th>{college_street_suburb}</th>
                                                <th>Street suburb of college</th>
                                            </tr>
                                            <tr>
                                                <th>{college_street_state}</th>
                                                <th>Street state of college</th>
                                            </tr>
                                            <tr>
                                                <th>{college_street_postcode}</th>
                                                <th>Street postcode of college</th>
                                            </tr>
                                            <tr>
                                                <th>{college_contact_phone}</th>
                                                <th>Contact phone of college</th>
                                            </tr>
                                            <tr>
                                                <th>{college_contact_email}</th>
                                                <th>Contact email of college</th>
                                            </tr>
                                            <tr>
                                                <th>{college_account_email}</th>
                                                <th>Account email of college</th>
                                            </tr>
                                            <tr>
                                                <th>{college_logo}</th>
                                                <th>Logo of college</th>
                                            </tr>
                                            <tr>
                                                <th>{college_signature}</th>
                                                <th>Signature of college</th>
                                            </tr>
                                            <tr>
                                                <th>{college_url}</th>
                                                <th>URL of college</th>
                                            </tr>
                                            <tr>
                                                <th>{application_reference_id}</th>
                                                <th>Application reference no</th>
                                            </tr>
                                            <tr>
                                                <th>{current_date}</th>
                                                <th>Use current date</th>
                                            </tr>
                                            <tr>
                                                <th>{student_name_title}</th>
                                                <th>Title of student (Like Mrs, Miss)</th>
                                            </tr>
                                            <tr>
                                                <th>{student_first_name}</th>
                                                <th>First name of Student</th>
                                            </tr>
                                            <tr>
                                                <th>{student_family_name}</th>
                                                <th>Last name of student</th>
                                            </tr>
                                            <tr>
                                                <th>{student_gender}</th>
                                                <th>Gender of student</th>
                                            </tr>
                                            <tr>
                                                <th>{student_date_of_birth}</th>
                                                <th>Date of birth of Student</th>
                                            </tr>
                                            <tr>
                                                <th>{student_email}</th>
                                                <th>Student of Email</th>
                                            </tr>
                                            <tr>
                                                <th>{nationality_of_student}</th>
                                                <th>Nationality of student</th>
                                            </tr>
                                            <tr>
                                                <th>{student_passport_no}</th>
                                                <th>Passport number of student</th>
                                            </tr>
                                            <tr>
                                                <th>{student_current_street_no}</th>
                                                <th>Current street number of student</th>
                                            </tr>
                                            <tr>
                                                <th>{student_current_street_name}</th>
                                                <th>Current street name of student</th>
                                            </tr>
                                            <tr>
                                                <th>{student_current_city}</th>
                                                <th>Current city of student</th>
                                            </tr>
                                            <tr>
                                                <th>{student_current_state}</th>
                                                <th>Current state of student</th>
                                            </tr>
                                            <tr>
                                                <th>{student_current_postcode}</th>
                                                <th>Current postcode of student</th>
                                            </tr>
                                            <tr>
                                                <th>{student_country_name}</th>
                                                <th>Country name of student</th>
                                            </tr>
                                            <tr>
                                                <th>{student_country_of_birth}</th>
                                                <th>Birth Country name of student</th>
                                            </tr>
                                            <tr>
                                                <th>{student_emergency_contact_person}</th>
                                                <th>Student Emergency Contact Person Name</th>
                                            </tr>
                                            <tr>
                                                <th>{student_emergency_relationship}</th>
                                                <th>Student Relation with Emergency Contact Person</th>
                                            </tr>
                                            <tr>
                                                <th>{student_emergency_address}</th>
                                                <th>Student Emergency Contact Person Address</th>
                                            </tr>
                                            <tr>
                                                <th>{student_emergency_phone}</th>
                                                <th>Student Emergency Contact Person Phone</th>
                                            </tr>
                                            <tr>
                                                <th>{student_emergency_email}</th>
                                                <th>Student Emergency Contact Person Email</th>
                                            </tr>
                                            <tr>
                                                <th>{student_subject_calming_for_credit}</th>
                                                <th>Studnet subject calming for credit</th>
                                            </tr>
                                            <tr>
                                                <th>{student_scholarship_percentage}</th>
                                                <th>Student Scholarship Percentage (%)</th>
                                            </tr>
                                            <tr>
                                                <th>{student_wil_requirements}</th>
                                                <th>Work Integrated Learning (WIL) Requirements</th>
                                            </tr>
                                            <tr>
                                                <th>{studnet_third_party_providers}</th>
                                                <th>Third-Party Providers engaged by CIHE to deliver / support the
                                                    course</th>
                                            </tr>
                                            <tr>
                                                <th>{student_subject_calming_for_credit}</th>
                                                <th>Studnet subject calming for credit</th>
                                            </tr>
                                            <tr>
                                                <th>{student_course_fee_after_scholarship}</th>
                                                <th>Studnet course fee after scholarship</th>
                                            </tr>
                                            <tr>
                                                <th>{student_course_fee_after_scholarship_and_initial_payment}</th>
                                                <th>Studnet course fee after scholarship and initial payment</th>
                                            </tr>
                                            <tr>
                                                <th>{course_duration_after_advance_standing}</th>
                                                <th>Course duration after advance standing</th>
                                            </tr>
                                            <tr>
                                                <th>{student_course_detail_table}</th>
                                                <th>
                                                    <table class="table-block" border="1">
                                                        <tr>
                                                            <th> Course </th>
                                                            <th> Cricos Code </th>
                                                            <th> Campus Location</th>
                                                            <th> Course Period </th>
                                                            <th> Course Length </th>
                                                            <th> Delivery Medium </th>
                                                            <th> Vocational Placement Hours / Work-based Training </th>
                                                            <th> Material Fee</th>
                                                            <th> Tuition Fee</th>
                                                        </tr>
                                                        <tr>
                                                            <td>CHC50113:Diploma of Early Childhood Education and Care
                                                            </td>
                                                            <td><span>097644F</span></td>
                                                            <td><span>Brisbane Campus</span></td>
                                                            <td><span>16/09/2019 - 01/08/2021</span></td>
                                                            <td> <span>98 week(s)</span></td>
                                                            <td> <span> Face to Face</span></td>
                                                            <td> <span>360</span></td>
                                                            <td> <span> $ 300.00</span></td>
                                                            <td> <span>$ 15,900.00</span></td>
                                                        </tr>
                                                    </table>
                                                </th>
                                            </tr>
                                            <tr>
                                                <th>{total_fee_due}</th>
                                                <th>Student course total fee due of student</th>
                                            </tr>
                                            <tr>
                                                <th>{student_course_material_fee}</th>
                                                <th>Material fee of first course</th>
                                            </tr>
                                            <tr>
                                                <th>{student_course_enroll_fee}</th>
                                                <th>Enrollment fee of first course</th>
                                            </tr>
                                            <tr>
                                                <th>{student_course_upfront_fee}</th>
                                                <th>Upfront fee of first course</th>
                                            </tr>
                                            <tr>
                                                <th>{material_fee}</th>
                                                <th>Sum of Student all course of Material fee</th>
                                            </tr>
                                            <tr>
                                                <th>{initial_payment}</th>
                                                <th>Sum of Enrollment Fee + Material Fee + Upfront Fee</th>
                                            </tr>
                                            <tr>
                                                <th>{offer_issue_date_after_X_days}</th>
                                                <th>Offer issue date after X date (currently X=28)</th>
                                            </tr>
                                            <tr>
                                                <th>{student_course_payment_schedule_fee}</th>
                                                <th>Student course total schedule payment fee</th>
                                            </tr>
                                            <tr>
                                                <th>{student_work_placement_detail_table}</th>
                                                <th>
                                                    The course {course_code} : {course_name} in Brisbane Campus requires
                                                    360 hours of work-based training
                                                </th>
                                            </tr>
                                            <tr>
                                                <th>{student_initial_payment_required_table}</th>
                                                <th>
                                                    <table class="" cellspacing="0" border="1" id="GridView1"
                                                        style="border-collapse:collapse;">
                                                        <tr style="background-color: #f48024;color: white;">
                                                            <th>Initial Payment Required</th>
                                                            <th><span class="font14 white">Fee Type</span></th>
                                                            <th><span class="font14 white">Amount</span></th>
                                                        </tr>
                                                        <tr>
                                                            <td>CHC50113 Diploma of Early Childhood Education and Care -
                                                                Deposit Fee</td>
                                                            <td>Tuition Fee</td>
                                                            <td>$550.00</td>
                                                        </tr>
                                                        <tr>
                                                            <td>Enrollment Fees</td>
                                                            <td>Non-Tuition Fee</td>
                                                            <td>$250.00</td>
                                                        </tr>
                                                        <tr>
                                                            <td>Diploma of Early Childhood Education and Care - Material
                                                                Fee</td>
                                                            <td>Non-Tuition Fee</td>
                                                            <td>$300.00</td>
                                                        </tr>
                                                        <tr>
                                                            <td colspan="2"><b class="red">TOTAL FEE DUE NOW</b></td>
                                                            <td><b class="red"> $1,100.00</b></td>
                                                        </tr>
                                                    </table>
                                                </th>
                                            </tr>
                                            <tr>
                                                <th>{student_course_special_condition_table}</th>
                                                <th>
                                                    <table class="table-block" cellspacing="0" rules="all" border="1"
                                                        id="GridView11" style="border-collapse:collapse;width: 100%;">
                                                        <tbody>
                                                            <tr>
                                                                <th class="font16" style="text-align: center;">Special
                                                                    Condition/s:</th>
                                                            </tr>
                                                            <tr>
                                                                <td><b>CHC50113 : Diploma of Early Childhood Education
                                                                        and Care</b></td>
                                                            </tr>
                                                            <tr>
                                                                <td>All students are required to go on a direct debit.
                                                                    Overseas Student Health Cover needs to be
                                                                    provided.Additional documents may be<br>required
                                                                    before vocational placement.</td>
                                                            </tr>
                                                        </tbody>
                                                    </table>
                                                </th>
                                            </tr>
                                            <tr>
                                                <th>{college_bank_name}</th>
                                                <th>Bank name of collage</th>
                                            </tr>
                                            <tr>
                                                <th>{college_bank_account_name}</th>
                                                <th>Bank account name of college</th>
                                            </tr>
                                            <tr>
                                                <th>{college_bank_BSB}</th>
                                                <th>Bank BSB of college</th>
                                            </tr>
                                            <tr>
                                                <th>{college_bank_account_number}</th>
                                                <th>Bank account number of college</th>
                                            </tr>
                                            <tr>
                                                <th>{student_offer_id}</th>
                                                <th>Generate offer number of college</th>
                                            </tr>
                                            <tr>
                                                <th>{college_bank_swift_code}</th>
                                                <th>Bank swift code of college</th>
                                            </tr>
                                            <tr>
                                                <th>{college_bank_branch}</th>
                                                <th>Bank branch of college</th>
                                            </tr>
                                            <tr>
                                                <th>{student_payment_schedule_table}</th>
                                                <th>
                                                    <table id="Table2" class="table-block" border="1">
                                                        <thead>
                                                            <tr>
                                                                <td colspan="3" class="bgred">Payment Schedule* Details:
                                                                </td>
                                                            </tr>
                                                            <tr>
                                                                <td colspan="3">Diploma of Early Childhood Education and
                                                                    Care</td>
                                                            </tr>
                                                            <tr>
                                                                <th>DUE DATE</th>
                                                                <th>AMOUNT</th>
                                                                <th>FEE TYPE</th>
                                                            </tr>
                                                        </thead>
                                                        <tbody>
                                                            <tr>
                                                                <td>10/10/2019</td>
                                                                <td>$902.94</td>
                                                                <td> Tutition Fee </td>
                                                            </tr>
                                                            <tr>
                                                                <td>10/11/2019</td>
                                                                <td>$902.94</td>
                                                                <td> Tutition Fee </td>
                                                            </tr>
                                                            <tr>
                                                                <td>10/12/2019</td>
                                                                <td>$902.94</td>
                                                                <td> Tutition Fee </td>
                                                            </tr>
                                                            <tr>
                                                                <td>10/01/2020</td>
                                                                <td>$902.94</td>
                                                                <td> Tutition Fee </td>
                                                            </tr>
                                                        </tbody>
                                                    </table>
                                                </th>
                                            </tr>
                                            <tr>
                                                <th>{student_parent_guardian_table}</th>
                                                <th>
                                                    If student is below 18 years of age, one of the parent/legal
                                                    guardian needs to sign.<br>
                                                    <table id="Table1" class="table-block" border="1">
                                                        <thead>
                                                            <tr>
                                                                <td style="border-width:0px !important;border-bottom: 1px solid !important;
                                                            border-top: 0px !important;" class="font14"><b>Parent
                                                                        Name:</b> </td>
                                                                <td style="border-width:0px !important;border-bottom: 1px solid !important;
                                                            border-top: 0px !important;" class="font14">
                                                                    &nbsp;&nbsp;&nbsp; - &nbsp;&nbsp; </td>
                                                                <td style="border-width:0px !important;border-bottom: 1px solid !important;
                                                            border-top: 0px !important;" class="font14"><b>Relation
                                                                        With Student:</b> </td>
                                                                <td style="border-width:0px !important;border-bottom: 1px solid !important;
                                                            border-top: 0px !important;" class="font14">
                                                                    &nbsp;&nbsp;&nbsp; - &nbsp;&nbsp; </td>
                                                            </tr>
                                                            <tr>
                                                                <td style="border-width:0px !important;border-left: none !important;
                                                            border-right: none !important;" class="font14">
                                                                    <b>Signed:</b> </td>
                                                                <td style="border-width:0px !important;border-bottom: 1px solid !important;
                                                            border-top: 0px !important;" class="font14">
                                                                    &nbsp;&nbsp;&nbsp; - &nbsp;&nbsp; </td>
                                                                <td style="border-width:0px !important;border-left: none !important;
                                                            border-right: none !important;" class="font14">
                                                                    <b>Dated:</b></td>
                                                                <td style="border-width:0px !important;border-bottom: 1px solid !important;
                                                                border-top: 0px !important;" class="font14">
                                                                    &nbsp;&nbsp;&nbsp; - &nbsp;&nbsp; </td>
                                                            </tr>
                                                        </thead>
                                                    </table>
                                                </th>
                                            </tr>
                                            <tr>
                                                <th>{student_course_detail_with_study_design_2_table}</th>
                                                <th>
                                                    <table class="table-block" cellspacing="0" rules="all" border="1"
                                                        style="border-collapse:collapse;width: 100%">
                                                        <thead>
                                                            <tr>
                                                                <th scope="col" style="width: 30%;"> Course </th>
                                                                <th scope="col" style="width: 15%;"> Cricos Code </th>
                                                                <th scope="col" style="width: 13%;"> Start Date</th>
                                                                <th scope="col" style="width: 12%;"> End Date </th>
                                                                <th scope="col" style="width: 20%;" colspan="2"> Course
                                                                    Duration </th>
                                                            </tr>
                                                        </thead>
                                                        <tbody>
                                                            <tr>
                                                                <td>CHC50113:Diploma of Early Childhood Education and
                                                                    Care</td>
                                                                <td>097644F</td>
                                                                <td>05/08/2019</td>
                                                                <td>20/06/2021</td>
                                                                <td colspan="2"> 52 Weeks including holidays </td>
                                                            </tr>
                                                            <tr>
                                                                <th colspan="2" style="text-align:left;">Mode of Study
                                                                </th>
                                                                <td colspan="2"> 15 Hours Face to Face </td>
                                                                <td colspan="2"> 5 Hours Online </td>
                                                            </tr>
                                                            <tr>
                                                                <th colspan="2" style="text-align:left;"> Location of
                                                                    Study </th>
                                                                <td colspan="4"> Level 2, Suite 201, 16-18 Wentworth
                                                                    Street, Parramatta, NSW-2150 </td>
                                                            </tr>
                                                            <tr>
                                                                <th colspan="2" style="text-align:left;"> Prerequisite
                                                                    Information </th>
                                                                <td colspan="4"> Some courses may have pre-requisite
                                                                    requirement, please be aware that student will not
                                                                    be able to commence on the course or unit of
                                                                    competency until the pre-requisite is met by the
                                                                    student. </td>
                                                            </tr>
                                                        </tbody>
                                                    </table>
                                                </th>
                                            </tr>
                                            <tr>
                                                <th>{student_course_details_design_2_table}</th>
                                                <th>
                                                    <table class="table-block" cellspacing="0" rules="all" border="1"
                                                        style="border-collapse:collapse;width: 100%">
                                                        <thead>
                                                            <tr>
                                                                <th scope="col"> Course </th>
                                                                <th scope="col"> Cricos Code </th>
                                                                <th scope="col"> Start Date</th>
                                                                <th scope="col"> End Date </th>
                                                                <th scope="col" colspan="2"> Course Duration </th>
                                                            </tr>
                                                        </thead>
                                                        <tbody>
                                                            <tr>
                                                                <td>CHC50113:Diploma of Early Childhood Education and
                                                                    Care</td>
                                                                <td>097644F</td>
                                                                <td>05/08/2019</td>
                                                                <td>20/06/2021</td>
                                                                <td colspan="2"> 52 Weeks including holidays </td>
                                                            </tr>
                                                        </tbody>
                                                    </table>
                                                </th>
                                            </tr>
                                            <tr>
                                                <th>{student_course_fees_and_charges_design_2_table}</th>
                                                <th>
                                                    <table class="table-block" cellspacing="0" rules="all" border="1"
                                                        style="border-collapse:collapse;width: 100%">
                                                        <thead>
                                                            <tr>
                                                                <th scope="col" style="width: 80%;"> Fees </th>
                                                                <th scope="col"> Amounts </th>
                                                            </tr>
                                                        </thead>
                                                        <tbody>
                                                            <tr>
                                                                <td>CHC50113:Diploma of Early Childhood Education and
                                                                    Care - Deposit Fee1</td>
                                                                <td>$10,000.00</td>
                                                            </tr>
                                                            <tr>
                                                                <td>Enrollment Fees</td>
                                                                <td>$250.00</td>
                                                            </tr>
                                                            <tr>
                                                                <td>Diploma of Early Childhood Education and
                                                                    Care-Material Fee</td>
                                                                <td>$300.00</td>
                                                            </tr>
                                                            <tr>
                                                                <td> Overseas Student Health Cover (OSHC) - 29-07-2019
                                                                    To 31-08-2021</td>
                                                                <td>$973.15</td>
                                                            </tr>
                                                            <tr>
                                                                <td> <strong> Total Amount (Incl. Material Fees)
                                                                        <strong></td>
                                                                <td style="width: 20%;">$11,523.15</td>
                                                            </tr>
                                                        </tbody>
                                                    </table>
                                                </th>
                                            </tr>
                                            <tr>
                                                <th>{student_payment_schedule_design_2_table}</th>
                                                <th>
                                                    <table class="table-block" cellspacing="0" rules="all" border="1"
                                                        style="border-collapse:collapse;width: 100%">
                                                        <thead>
                                                            <tr>
                                                                <th scope="col"> Payment Schedule </th>
                                                                <th scope="col" style="width: 15%;"> Due Date </th>
                                                                <th scope="col" style="width: 15%;"> Amount </th>
                                                            </tr>
                                                        </thead>
                                                        <tbody>
                                                            <tr>
                                                                <td colspan="3">CHC50113 : Diploma of Early Childhood
                                                                    Education and Care </td>
                                                            </tr>
                                                            <tr>
                                                                <td> Tutition Fee </td>
                                                                <td style="width: 15%;">15/02/2020</td>
                                                                <td style="width: 15%;">$825.00</td>
                                                            </tr>
                                                            <tr>
                                                                <td> Tutition Fee </td>
                                                                <td style="width: 15%;">22/02/2020</td>
                                                                <td style="width: 15%;">$825.00</td>
                                                            </tr>
                                                            <tr>
                                                                <td> Tutition Fee </td>
                                                                <td style="width: 15%;">29/02/2020</td>
                                                                <td style="width: 15%;">$825.00</td>
                                                            </tr>
                                                            <tr>
                                                                <td> Tutition Fee </td>
                                                                <td style="width: 15%;">07/03/2020</td>
                                                                <td style="width: 15%;">$825.00</td>
                                                            </tr>
                                                            <tr>
                                                                <td colspan="2"> <strong> Total Remaining Amount
                                                                        <strong> </td>
                                                                <td style="width: 15%;"> <strong>$3,300.00</strong>
                                                                </td>
                                                            </tr>
                                                        </tbody>
                                                    </table>
                                                </th>
                                            </tr>
                                            <tr>
                                                <th>{student_course_total_tuition_fees_domestic_table}</th>
                                                <th>
                                                    <table class="table-block" cellspacing="0" rules="all" border="1"
                                                        style="border-collapse:collapse;width: 100%">
                                                        <thead>
                                                            <tr>
                                                                <th scope="col">Total Tuition Fees </th>
                                                                <th scope="col" style="width: 15%;">Amount </th>
                                                            </tr>
                                                        </thead>
                                                        <tbody>
                                                            <tr>
                                                                <td>CHC50113 : Diploma of Early Childhood Education and
                                                                    Care </td>
                                                                <td style="width: 15%;">$500.00</td>
                                                            </tr>

                                                            <tr>
                                                                <td> <strong>Total Amount Due AUS$ : <strong> </td>
                                                                <td style="width: 15%;"><strong>$500.00</strong> </td>
                                                            </tr>
                                                        </tbody>
                                                    </table>
                                                </th>
                                            </tr>
                                            <tr>
                                                <th>{student_course_initial_payment_required_domestic_table}</th>
                                                <th>
                                                    <table class="table-block" cellspacing="0" rules="all" border="1"
                                                        style="border-collapse:collapse;width: 100%">
                                                        <thead>
                                                            <tr>
                                                                <th scope="col"> Initial Payment Required </th>
                                                                <th scope="col" style="width: 15%;"> Amount </th>
                                                            </tr>
                                                        </thead>
                                                        <tbody>
                                                            <tr>
                                                                <td>CHC50113 : Diploma of Early Childhood Education and
                                                                    Care – Deposit Fee </td>
                                                                <td style="width: 15%;">$100.00</td>
                                                            </tr>
                                                            <tr>
                                                                <td> <strong> TOTAL FEE DUE NOW <strong> </td>
                                                                <td style="width: 15%;"> <strong>$100.00</strong> </td>
                                                            </tr>
                                                        </tbody>
                                                    </table>
                                                </th>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button data-dismiss="modal" class="btn btn-danger" type="button"> Cancel </button>

            </div>
        </div>
    </div>
</div>
@endsection