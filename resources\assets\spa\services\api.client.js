import axios from 'axios';

const SKIP_NOTIFICATION_URLS = ['/broadcasting/auth', '/sanctum/csrf-cookie'];

const getClient = (baseUrl = null) => {
    const options = {
        baseURL: baseUrl,
        withCredentials: true,
    };

    axios.defaults.withCredentials = true;
    const client = axios.create(options);

    // Add a request interceptor
    client.interceptors.request.use(
        (requestConfig) => {
            return requestConfig;
        },
        (requestError) => {
            return Promise.reject(requestError);
        }
    );

    // Add a response interceptor
    client.interceptors.response.use(
        (response) => {
            responseNotifire();
            getResponseMessage(response);
            return response;
        },
        (error) => {
            let response = error.response;
            // console.log(response);
            responseNotifire();
            /* don't show notifications if the urls are among the skip ones. */
            responseErrorNotifier(response.data);
            // if (SKIP_NOTIFICATION_URLS.indexOf(response.config.url) === -1) {
            // }

            return Promise.reject(error);
        }
    );

    client.defaults.headers.common['X-Requested-With'] = 'XMLHttpRequest';

    return client;
};

const responseNotifire = () => {
    if (window['Fire']) {
        setTimeout(() => {
            window['Fire'].emit('axiosResponseReceived', null);
        }, 500);
    }
};
const responseSuccessNotifire = (message) => {
    if (window['Fire']) {
        setTimeout(() => {
            window['Fire'].emit('axiosResponseSuccess', { message });
        }, 500);
    }
};

const responseWarningNotifire = (message) => {
    if (window['Fire']) {
        setTimeout(() => {
            window['Fire'].emit('axiosResponseWarning', { message });
        }, 500);
    }
};

const responseErrorNotifier = (err) => {
    if (window['Fire']) {
        setTimeout(() => {
            window['Fire'].emit('axiosResponseError', err);
        }, 500);
    }
};

const getResponseMessage = (response) => {
    let apiResponse = response['data'];
    if (apiResponse['status'] && apiResponse['status'] == 'success') {
        if (apiResponse['message'] && apiResponse['message'] != '') {
            responseSuccessNotifire(apiResponse['message']);
            return response;
        }
    }
    if (apiResponse['status'] && apiResponse['status'] == 'warning') {
        if (apiResponse['message']) {
            responseWarningNotifire(apiResponse['message']);
            return response;
        }
    }

    if (apiResponse['status'] && apiResponse['status'] == 'error') {
        if (apiResponse.code == 422 || apiResponse.code == 411 || apiResponse.code == 400) {
            showValidationError(apiResponse);
            return response;
        }

        responseErrorNotifier({ message: apiResponse['message'] });
        return Promise.reject(apiResponse);
    }
};

class ApiClient {
    client;
    constructor(baseUrl = null) {
        // console.log(import.meta.env.VITE_BASE_URL);
        baseUrl = baseUrl || window['APP_URL'];
        baseUrl = baseUrl + '/';
        // console.log(baseUrl);
        this.client = getClient(baseUrl);
    }

    get(url, conf = {}) {
        return this.client
            .get(url, conf)
            .then((response) => Promise.resolve(response.data))
            .catch((error, err) => {
                return Promise.reject(error.response);
            });
    }

    delete(url, conf = {}) {
        return this.client
            .delete(url, conf)
            .then((response) => Promise.resolve(response.data))
            .catch((error) => Promise.reject(error.response));
    }

    head(url, conf = {}) {
        return this.client
            .head(url, conf)
            .then((response) => Promise.resolve(response.data))
            .catch((error) => Promise.reject(error.response));
    }

    options(url, conf = {}) {
        return this.client
            .options(url, conf)
            .then((response) => Promise.resolve(response.data))
            .catch((error) => Promise.reject(error.response));
    }

    post(url, data = {}, conf = {}) {
        // console.log('axios', data);
        return this.client
            .post(url, data, conf)
            .then((response) => Promise.resolve(response.data))
            .catch((error) => Promise.reject(error.response));
    }

    put(url, data = {}, conf = {}) {
        return this.client
            .put(url, data, conf)
            .then((response) => Promise.resolve(response.data))
            .catch((error) => Promise.reject(error.response));
    }

    patch(url, data = {}, conf = {}) {
        return this.client
            .patch(url, data, conf)
            .then((response) => Promise.resolve(response.data))
            .catch((error) => Promise.reject(error.response));
    }
}

const apiClient = new ApiClient();

export default apiClient;

function showValidationError(error) {
    // console.log(error);
    let $errors = [];
    if (typeof error.data !== 'undefined') {
        for (let er in error.data) {
            if (error.data.hasOwnProperty(er) && error.data[er][0] !== 'undefined') {
                // console.log(error.errors[er]);
                $errors.push(error.data[er][0]);
            }
        }
    }

    if (typeof error.errors !== 'undefined') {
        for (let er in error.errors) {
            if (error.errors.hasOwnProperty(er) && error.errors[er][0] !== 'undefined') {
                // console.log(error.errors[er]);
                $errors.push(error.errors[er][0]);
            }
        }
    }

    console.log($errors);

    if ($errors.length > 0) {
        responseErrorNotifier({
            title: 'Validation Error',
            severity: 'warn',
            message: $errors.join('<br/>'),
        });
    }
}
