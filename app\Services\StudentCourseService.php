<?php

namespace App\Services;

use App;
use App\DTO\studentProfile\EditEnrollResultCourse;
use App\DTO\studentProfile\EditEnrollStudentCourse;
use App\DTO\studentProfile\SaveStudentTrainingPlan;
use App\DTO\studentProfile\SaveSubjectToCourse;
use App\DTO\studentProfile\StudentServiceInfo;
use App\DTO\studentProfile\UpdateStudentTrainingPlan;
use App\Exceptions\ApplicationException;
use App\Helpers\Helpers;
use App\Model\v2\AreaOfDisability;
use App\Model\v2\AssessmentDueDate;
use App\Model\v2\CampusVenue;
use App\Model\v2\CertificateIdFormate;
use App\Model\v2\CollegeCampus;
use App\Model\v2\CollegeMaterials;
use App\Model\v2\ContractCode;
use App\Model\v2\ContractFundingSource;
use App\Model\v2\CourseCalendar;
use App\Model\v2\CoursesIntakeDate;
use App\Model\v2\CourseSite;
use App\Model\v2\CourseTraining;
use App\Model\v2\Employer;
use App\Model\v2\InvoiceSetting;
use App\Model\v2\OfferDocumentChecklist;
use App\Model\v2\OfferUpfrontFeeSchedule;
use App\Model\v2\OSHC;
use App\Model\v2\OSHCProvider;
use App\Model\v2\ResultGrade;
use App\Model\v2\Semester;
use App\Model\v2\SemesterDivision;
use App\Model\v2\SetupProviderFacility;
use App\Model\v2\SetupSection;
use App\Model\v2\SetupServices;
use App\Model\v2\SetupServicesCategory;
use App\Model\v2\SetupServicesName;
use App\Model\v2\Student;
use App\Model\v2\StudentAdditionalServiceRequest;
use App\Model\v2\StudentCertificateRegister;
use App\Model\v2\StudentCommunicationLog;
use App\Model\v2\StudentCourses;
use App\Model\v2\StudentDetails;
use App\Model\v2\StudentInitialPaymentDetails;
use App\Model\v2\StudentOfferDocuments;
use App\Model\v2\StudentServiceInformation;
use App\Model\v2\StudentSubjectEnrolment;
use App\Model\v2\StudyReason;
use App\Model\v2\UnitModule;
use App\Process\StudentProfile\AddCoeForOfferProcess;
use App\Repositories\CommonRepository;
use App\Repositories\StudentCourseRepository;
use App\Repositories\StudentProfileCommonRepository;
use App\Repositories\StudentRepository;
use App\Traits\CommonTrait;
use App\Traits\ResponseTrait;
use Carbon\Carbon;
use Domains\Xero\Facades\Xero;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\DB;
use Support\Services\UploadService;

class StudentCourseService
{
    use CommonTrait;
    use ResponseTrait;

    private $studentProfileCommonService;

    private $commonRepository;

    private $studentRepository;

    private $studentCourseRepository;

    private $offerUpfrontFeeSchedule;

    private $setupSection;

    private $studentCommunicationLog;

    private $studentProfileCommonRepository;

    private $oshc;

    private $studentCourse;

    private $oshcProvider;

    private $areaOfDisability;

    private $setupServicesCategory;

    private $setupServices;

    private $setupProviderFacility;

    private $studentServiceInformation;

    private $studentAdditionalServiceRequest;

    private $courseTraining;

    private $courseCalendar;

    private $semester;

    private $unitModule;

    private $assessmentDueDate;

    private $student;

    private $coursesIntakeDate;

    private $studyReason;

    private $resultGrade;

    private $semesterDivision;

    private $offerDocumentChecklist;

    private $collegeMaterials;

    private $studentOfferDocuments;

    private $studentDetails;

    private $employer;

    private $contractCode;

    private $courseSite;

    private $campusVenue;

    public function __construct(
        // StudentCourseRepository           $studentCourseRepository,
        CommonRepository $commonRepository,
        StudentProfileCommonRepository $studentProfileCommonRepository,
        StudentProfileCommonService $studentProfileCommonService,
        OSHC $oshc,
        StudentCourses $studentCourses,
        OSHCProvider $oshcProvider,
        AreaOfDisability $areaOfDisability,
        SetupServicesCategory $setupServicesCategory,
        SetupServices $setupServices,
        SetupProviderFacility $setupProviderFacility,
        StudentAdditionalServiceRequest $studentAdditionalServiceRequest,
        StudentServiceInformation $studentServiceInformation,
        CourseTraining $courseTraining,
        CourseCalendar $courseCalendar,
        Semester $semester,
        UnitModule $unitModule,
        AssessmentDueDate $assessmentDueDate,
        Student $student,
        CoursesIntakeDate $coursesIntakeDate,
        StudyReason $studyReason,
        ResultGrade $resultGrade,
        SemesterDivision $semesterDivision,
        OfferDocumentChecklist $offerDocumentChecklist,
        CollegeMaterials $collegeMaterials,
        StudentOfferDocuments $studentOfferDocuments,
        OfferUpfrontFeeSchedule $offerUpfrontFeeSchedule,
        SetupSection $setupSection,
        StudentCommunicationLog $studentCommunicationLog,
        StudentDetails $studentDetails,
        Employer $employer,
        ContractCode $contractCode,
        CourseSite $courseSite,
        CampusVenue $campusVenue
    ) {
        // $this->studentCourseRepository      = $studentCourseRepository;
        $this->studentProfileCommonService = $studentProfileCommonService;
        $this->commonRepository = $commonRepository;
        $this->studentProfileCommonRepository = $studentProfileCommonRepository;
        $this->studentRepository = new StudentRepository($student);
        $this->studentCourseRepository = new StudentCourseRepository($setupServices);
        $this->oshc = new StudentCourseRepository($oshc);
        $this->studentCourse = new StudentCourseRepository($studentCourses);
        $this->oshcProvider = new StudentCourseRepository($oshcProvider);
        $this->areaOfDisability = new StudentCourseRepository($areaOfDisability);
        $this->setupServicesCategory = new StudentCourseRepository($setupServicesCategory);
        $this->setupServices = new StudentCourseRepository($setupServices);
        $this->setupProviderFacility = new StudentCourseRepository($setupProviderFacility);
        $this->studentServiceInformation = new StudentCourseRepository($studentServiceInformation);
        $this->studentAdditionalServiceRequest = new StudentCourseRepository($studentAdditionalServiceRequest);
        $this->courseTraining = new StudentCourseRepository($courseTraining);
        $this->courseCalendar = new StudentCourseRepository($courseCalendar);
        $this->semester = new StudentCourseRepository($semester);
        $this->unitModule = new StudentCourseRepository($unitModule);
        $this->assessmentDueDate = new StudentCourseRepository($assessmentDueDate);
        $this->student = new StudentCourseRepository($student);
        $this->coursesIntakeDate = new StudentCourseRepository($coursesIntakeDate);
        $this->studyReason = new StudentCourseRepository($studyReason);
        $this->resultGrade = new StudentCourseRepository($resultGrade);
        $this->semesterDivision = new StudentCourseRepository($semesterDivision);
        $this->offerDocumentChecklist = new StudentCourseRepository($offerDocumentChecklist);
        $this->collegeMaterials = new StudentCourseRepository($collegeMaterials);
        $this->studentOfferDocuments = new StudentCourseRepository($studentOfferDocuments);
        $this->offerUpfrontFeeSchedule = new StudentCourseRepository($offerUpfrontFeeSchedule);
        $this->setupSection = new StudentCourseRepository($setupSection);
        $this->studentCommunicationLog = new StudentCourseRepository($studentCommunicationLog);
        $this->studentDetails = new StudentCourseRepository($studentDetails);
        $this->employer = new StudentCourseRepository($employer);
        $this->contractCode = new StudentCourseRepository($contractCode);
        $this->courseSite = new StudentCourseRepository($courseSite);
        $this->campusVenue = new StudentCourseRepository($campusVenue);
    }

    public function getCertificateIssueListData(Request $request)
    {
        $result = $this->studentCourseRepository->getCertificateIssueListData($request);

        return [
            'data' => $result['data'],
            'total' => $result['total'],
        ];
    }

    public function getEnrollSubjectListData(Request $request)
    {
        $result = $this->studentCourseRepository->getEnrollSubjectListData($request);

        return [
            'data' => $result['data'],
            'total' => $result['total'],
        ];

    }

    public function getEnrollSubSemesterList(Request $request)
    {
        $studentCourseData = StudentCourses::find($request->student_course_id);
        $startYear = date('Y', strtotime($studentCourseData->start_date));
        $endYear = date('Y', strtotime($studentCourseData->finish_date));
        $data = Semester::whereBetween('year', [$startYear, $endYear])->get(['id as Id', 'semester_name as Name'])->toArray();

        return $data;
    }

    public function getEnrollVenueLocation($request)
    {
        return $this->studentCourseRepository->getEnrollVenueLocation($request);
    }

    public function getStudyReason()
    {
        return $this->studyReason->getAll(['avaitmiss_id as Id', 'title as Name'], 'avaitmiss_id', 'ASC');
    }

    public function getMarkOutcomeData($request)
    {
        $whereArr = ['college_id' => $request->college_id];
        $columnArr = [
            'id as Id',
            'grade as Name',
        ];

        return ResultGrade::select($columnArr)->where($whereArr)->orderBy('id', 'desc')->get()->toArray();
    }

    public function getTermList($request)
    {
        $whereArr = [
            'college_id' => $request->user()->college_id,
            'semester_id' => $request->semester_id,
        ];
        $data = $this->semesterDivision->getWhereGroupBy($whereArr, ['term as Id', 'term as Name'], 'term');
        // $result = (isset($data) && count($data) > 0) ? $data : ['Id'=>'', 'Name'=>'No Data Found'];
        $result = (isset($data) && count($data) > 0) ? $data : [];

        return $result;
    }

    public function getTermDetails($request)
    {
        $whereArr = [
            'college_id' => $request->user()->college_id,
            'term' => $request->term,
            'semester_id' => $request->semester_id,
        ];

        return SemesterDivision::select(DB::raw('MIN(week_start) as week_start'), DB::raw('MAX(week_finish) as week_finish'))
            ->where($whereArr)
            ->first()->toArray();

    }

    public function saveStudentSubjectEnrollment(SaveSubjectToCourse $request, $isHigherEd)
    {
        $data = $request->toArray();
        $flag = false;
        $collegeId = Auth::user()->college_id;
        $enrollType = $data['type_of_enrollment'];
        $subjectIds = isset($data['subject_id']) ? $data['subject_id'] : [];
        $unitIds = isset($data['unit_id']) ? $data['unit_id'] : [];
        $data['course_id'] = StudentCourses::find($data['student_course_id'])->course_id;
        $data['outcome_identifier'] = isset($data['outcome_identifier']) ? $data['outcome_identifier'] : '';
        if ($enrollType == 'subject' && empty($subjectIds)) {
            $result = ['status' => 'error', 'message' => 'Please select at least 1 subject'];
        } elseif ($enrollType == 'unit' && empty($unitIds)) {
            $result = ['status' => 'error', 'message' => 'Please select at least one unit.'];
        } else {
            // $assessmentData = $this->assessmentDueDate->getWhereFirst(['college_id' => $collegeId]);
            $assessmentData = $this->assessmentDueDate->getWhere(['college_id' => $collegeId]);
            if (count($assessmentData) == 0) {
                return ['status' => 'error', 'message' => 'Please update default assessment due date from setting...!'];
            }
            $getDueDay = $assessmentData[0]['default_due_day'];
            $sDate = date('Y-m-d', strtotime($data['start_date'].'- '.$getDueDay.' days'));
            $eDate = date('Y-m-d', strtotime($data['finish_date'].'+ '.$getDueDay.' days'));
            $activityStart = $data['activity_start_date'];
            $activityEnd = $data['activity_finish_date'];

            if (strtotime($activityStart) <= strtotime($sDate) || strtotime($activityEnd) >= strtotime($eDate)) {
                $result = ['status' => 'error', 'message' => 'Activity start and end dates must fall within the course duration.'];
            } elseif (! empty($subjectIds) || ! empty($unitIds)) {
                $whereArr = [
                    'college_id' => $collegeId,
                    'student_id' => $data['student_id'],
                    'course_id' => $data['course_id'],
                    'semester_id' => $data['semester_id'],
                ];
                if ($enrollType == 'subject') {
                    $checkDuplicate = $this->studentCourseRepository->studentSubjectEnrolment($whereArr, $subjectIds);
                } else {
                    $checkDuplicate = $this->studentCourseRepository->studentUnitEnrolment($whereArr, $unitIds, $isHigherEd);
                }
                if ($checkDuplicate > 0) {
                    $result = ['status' => 'error', 'message' => 'Duplicate Record Found'];
                } else {
                    $flag = true;
                }
            }
        }

        // TODO::GNG-2516
        if ($isHigherEd) {
            $gradeData = $this->resultGrade->find($data['mark_outcome']);
            $data['final_outcome'] = null;
            $data['grade'] = $gradeData->grade;
            $data['competency'] = $gradeData->grade;
            // $data['competency'] = ">=". $gradeData->marks . " (". $gradeData->grade .")";
        } else {
            $data['competency'] = $data['final_outcome'];
        }

        if ($flag) {
            if ($enrollType == 'subject') {
                foreach ($subjectIds as $subjectId) {
                    $unitData = $this->unitModule->getWhere(['college_id' => $collegeId, 'subject_id' => $subjectId]);
                    foreach ($unitData as $unitRow) {
                        $objSubjectEnrolment = $this->studentCourseRepository->saveStudentSubjectEnrollData($data, $subjectId, $unitRow['id']);
                        $this->studentCourseRepository->saveStudentUnitEnrollData($data, $objSubjectEnrolment, $unitRow);
                    }
                }
            }
            if ($enrollType == 'unit') {
                foreach ($unitIds as $unitId) {
                    $objUnit = $this->unitModule->find($unitId);

                    $attempt = StudentSubjectEnrolment::where([
                        'student_id' => $data['student_id'],
                        'course_id' => $data['course_id'],
                        'subject_id' => $objUnit->subject_id,
                        'unit_id' => $unitId]);

                    if ($attempt->count() > 0) {
                        // Check lasyt attempt activity finish date
                        $lastAttemptArr = $attempt->orderBy('subject_attempt', 'DESC')->first(['activity_start_date', 'activity_finish_date'])->toArray();
                        $lastAttemptEnd = Carbon::parse($lastAttemptArr['activity_finish_date']); // Example date for the last attempt end
                        $newStartDate = Carbon::parse($data['activity_start_date']); // Example date for the new start date

                        if (! ($newStartDate->gt($lastAttemptEnd))) {
                            return ['status' => 'error', 'message' => 'New start date is not greater than the end date of the last attempt.'];
                        }
                    }
                    $objSubjectEnrolment = $this->studentCourseRepository->saveStudentSubjectEnrollData($data, $objUnit->subject_id, $unitId, $attempt->count());
                    $this->studentCourseRepository->saveStudentUnitEnrollData($data, $objSubjectEnrolment, $objUnit);
                }
            }

            return ['status' => 'success', 'message' => 'Student Enrollment Result is Saved Successfully'];
        }

        return $result;
    }

    public function getEnrolledCourse($request)
    {
        $studentCourseData = StudentCourses::find($request->student_course_id);

        $resultQuery = StudentCourses::withStudentDetails($request->student_id);
        $resultQuery->where('course_id', $studentCourseData->course_id);
        $resultArr = $resultQuery->get()->toArray();

        foreach ($resultArr as $key => $res) {
            // $resultArr[$key]['bg_color'] = $this->getColorCode($res['status']);
            $resultArr[$key]['bg_color'] = Helpers::getStudCourseColorCode($res['status']);
        }

        $dropDownArray = array_map(function ($course) {
            return [
                'id' => $course['id'],
                'course_title' => $course['course_title'],
            ];
        }, $resultArr);

        return $dropDownArray;
    }

    public function updateResultCourse(EditEnrollResultCourse $data)
    {
        $post = $data->toArray();
        $primaryIDs = explode(',', $post['primaryID']);
        $newStudentCourse = StudentCourses::find($post['newStudentCourseId']);
        $errors = [];
        foreach ($primaryIDs as $primaryID) {
            $enrolmentObj = StudentSubjectEnrolment::find($primaryID);
            $enrolmentStartDate = strtotime($enrolmentObj->activity_start_date);
            $enrolmentEndDate = strtotime($enrolmentObj->activity_finish_date);
            $newCourseStartDate = strtotime($newStudentCourse->start_date);
            $newCourseEndDate = strtotime($newStudentCourse->finish_date);
            if ($enrolmentStartDate < $newCourseStartDate || $enrolmentEndDate > $newCourseEndDate) {
                $errors[] = "Enrolment ID $primaryID: Enrolment dates must fall within the new course start and end dates";

                continue;
            }
            $enrolmentObj->student_course_id = $post['newStudentCourseId'];
            $enrolmentObj->save();
        }

        if (! empty($errors)) {
            return ['status' => 'error', 'message' => implode(', ', $errors)];
        }

        return ['status' => 'success', 'message' => 'Result course updated successfully'];
    }

    public function getStudentCourseDetails($request)
    {
        $sqlDateFormat = Helpers::toMysqlDateFormat();
        $studentId = $request->student_id;
        $studentCourseId = $request->student_course_id;
        $collegeId = $request->college_id;
        $columnArr = [
            'generated_stud_id',
            'student_type',
            'profile_picture',
            'first_name',
            'family_name',
            DB::raw("CONCAT(first_name, ' ', family_name) as full_name"),
        ];

        $studData = $this->student->getWhereFirst(['id' => $studentId], $columnArr);
        $studCourseData = $this->studentCourse->findData($studentCourseId);
        $student_detail = array_merge($studData, $studCourseData);

        $intakeDateList = [];
        if (! empty($student_detail['intake_year'])) {
            $intakeWhereArr = [
                'course_id' => $student_detail['course_id'],
                'intake_year' => $student_detail['intake_year'],
            ];
            $intakeColumnArr = ['intake_start as Id', 'intake_start as Name'];
            $intakeDateList = $this->coursesIntakeDate->getWhere($intakeWhereArr, $intakeColumnArr);
            foreach ($intakeDateList as $key => $value) {
                $intakeDateList[$key]['Name'] = date('d-m-Y', strtotime($value['Name']));
            }
        }
        $data['student_detail'] = $student_detail;
        $data['student_detail']['profile_pic'] = $this->getStudentProfilePicPath($student_detail['student_id'], $student_detail['profile_picture']);
        $data['student_detail']['isHigherEd'] = StudentCourses::checkCourseIsHigherEd($studentCourseId);
        $data['student_detail']['intakeDateList'] = $intakeDateList;
        $data['offerIdData']['data'] = StudentCourses::where(['student_id' => $studentId])->groupBy('offer_id')->select(['offer_id as Id', 'offer_id as Name'])->get()->toArray();
        $data['coursesData']['data'] = $this->studentProfileCommonRepository->getCourses();
        $data['campusData']['data'] = CollegeCampus::select('name as Name', 'id as Id')->where('status', 1)->get()->toArray();
        $data['resultData']['data'] = $this->convertConstantsFormat(Config::get('constants.arrResultsCalculationMethod'));
        $data['agentsData']['data'] = $this->studentProfileCommonRepository->getAgentsByCollegeId($collegeId);
        $data['intakeYearData']['data'] = $this->convertConstantsFormat(Config::get('constants.arrIntakeYearEdit'));
        $data['templateData']['data'] = $this->studentProfileCommonRepository->getTemplatesByCollegeAndCourseId($collegeId, $student_detail['course_id']);
        $data['surveyContactData']['data'] = $this->convertConstantsFormat(Config::get('constants.arrSurveyContactStatusDiv'));
        $data['courseDurationType']['data'] = $this->convertConstantsFormat(Config::get('constants.arrCourseDurationType'));
        $data['courseStatusData']['data'] = $this->convertConstantsFormat(Config::get('constants.arrCourseStatus'));
        $data['studyReasonData']['data'] = StudyReason::select('avaitmiss_id as Id', 'title as Name')->orderBy('avaitmiss_id', 'ASC')->get()->toArray();
        $data['employerData']['data'] = Employer::select('employer_name as Name', 'id as Id')->get()->toArray();
        $data['contractSchedule']['data'] = $this->getContractCodeList($collegeId);
        $data['subjectCredit']['data'] = $this->convertConstantsFormat(Config::get('constants.arrSubjectCalmingForCredit'));

        $case1 = OfferUpfrontFeeSchedule::where('student_course_id', $studentCourseId)->count();
        $case2 = StudentInitialPaymentDetails::where('student_course_id', $studentCourseId)->count();
        $data['isScheduleExist'] = ($case1 > 0 || $case2 > 0) ? true : false;
        $data['isDeferredCourse'] = ($studCourseData['status'] == StudentCourses::STATUS_DEFERRED) ? true : false;

        $typeArr = Config::get('constants.generateCertificateListType');
        $data['isCertificateGenerated'] = StudentCertificateRegister::where(['rto_student_certificate_register.college_id' => $collegeId, 'student_id' => $studentId, 'course_id' => $student_detail['course_id'], 'certificate_type' => $typeArr['C']])->count();
        if ($data['isCertificateGenerated'] > 0) {
            $data['certificate_data'] = StudentCertificateRegister::leftjoin('rto_users', 'rto_student_certificate_register.created_by', '=', 'rto_users.id')
                ->where(['rto_student_certificate_register.college_id' => $collegeId, 'student_id' => $studentId, 'course_id' => $student_detail['course_id'], 'certificate_type' => $typeArr['C']])
                ->orderBy('rto_student_certificate_register.id', 'desc')
                ->select('rto_student_certificate_register.*', 'rto_users.name', DB::raw("DATE_FORMAT(date_generated, '$sqlDateFormat') AS date_generated"))->first()->toArray();
        } else {
            $data['certificate_data'] = [];
        }

        return $data;
    }

    public function getContractCodeList($collegeId)
    {
        $resultArr = [];
        $arrFundingSource = Config::get('constants.arrFundingSource');
        $nullResult[0]['Id'] = 0;
        $nullResult[0]['Name'] = 'No Contract Schedule Found';
        $arrContractCode = ContractFundingSource::from('rto_contract_funding_source as rcfs')
            ->join('rto_contract_code as rcc', 'rcc.id', '=', 'rcfs.contract_code_id')
            ->where('rcfs.college_id', $collegeId)
            ->get(['rcfs.*', 'rcc.contract_code']);
        foreach ($arrContractCode as $key => $row) {
            $resultArr[$key]['Name'] = $row->contract_code.' ['.$arrFundingSource[$row->funding_source].']';
            $resultArr[$key]['Id'] = $row->id;
        }

        if (count($resultArr) > 0) {
            return $resultArr;
        } else {
            return $nullResult;
        }
    }

    public function updateEnrollStudentCourse(EditEnrollStudentCourse $data)
    {
        $post = $data->toArray();
        $primaryId = $post['student_course_id'];

        if ($primaryId > 0) {
            $case1 = OfferUpfrontFeeSchedule::where('student_course_id', $primaryId)->count();
            $case2 = StudentInitialPaymentDetails::where('student_course_id', $primaryId)->count();
            $post['isScheduleExist'] = ($case1 > 0 || $case2 > 0) ? true : false;
        }

        $finalResult = [];
        DB::beginTransaction();
        try {
            if (isset($primaryId)) {
                if ((! $post['isScheduleExist']) ? ($post['agent_id'] != '' && ! empty($post['campus_id'])) : ! empty($post['campus_id'])) {
                    $scArr = $this->studentCourse->find($primaryId);
                    if (! isset($scArr)) {
                        return ['status' => 'error', 'message' => 'Course Not Fond.'];
                    }
                    $scUpdateData = $this->setCourseEnrollDataArr($post, 'update');
                    $finalResult = $this->studentCourse->update($scUpdateData, $primaryId);

                    if ($scUpdateData['course_template'] != '') {

                        $enrollPost['student_course_id'] = $scArr->id;
                        $enrollPost['student_id'] = $scArr->student_id;
                        $enrollPost['course_id'] = $scArr->course_id;
                        $enrollPost['college_id'] = Auth::user()->college_id;
                        $this->studentProfileCommonRepository->enrollTemplateUnit($enrollPost);
                    }

                    $newStatus = (! empty($post['status'])) ? $post['status'] : '';
                    $this->studentProfileCommonService->manageCourseStatusHistory($scArr->student_id, $scArr->id, $newStatus, $scArr->status);
                    DB::commit();

                    return ['status' => 'success', 'message' => 'Course update successfully.'];
                }
            }
        } catch (\Exception $e) {
            DB::rollBack();
            throw new ApplicationException($e->getMessage());
        }
    }

    private function setCourseEnrollDataArr($post, $type)
    {
        $userId = Auth::user()->id;
        /*$total_weeks = 0;
        if (! $post['isScheduleExist']) {
            $total_weeks = round(abs(strtotime($post['finish_date']) - strtotime($post['start_date'])) / 604800);
        }*/
        $resData = $this->buildBaseData($post);

        if ($type == 'add') {
            $this->addAdditionalData($resData, $post, $userId);
        }

        return $resData;
    }

    private function buildBaseData($post)
    {
        $userId = Auth::user()->id;
        $dataArr = [
            'offer_id' => $post['offer_id'] ?? null,
            'campus_id' => $post['campus_id'] ?? null,
            'intake_year' => $post['intake_year'] ?? null,
            'intake_date' => $post['intake_date'] ? date('Y-m-d', strtotime($post['intake_date'])) : null,
            'issued_date' => $post['issued_date'] ? date('Y-m-d', strtotime($post['issued_date'])) : null,
            'census_date1' => $post['census_date1'] ? date('Y-m-d', strtotime($post['census_date1'])) : null,
            'census_date2' => $post['census_date2'] ? date('Y-m-d', strtotime($post['census_date2'])) : null,
            'census_date3' => $post['census_date3'] ? date('Y-m-d', strtotime($post['census_date3'])) : null,
            'course_template' => $post['course_template'] ?? '',
            'default_unit_fee' => $post['default_unit_fee'] ?? null,
            'is_finish_dt' => $post['is_finish_dt'] ?? '0',
            'enroll_fee' => $post['enroll_fee'] ?? null,
            'course_material_fee' => $post['course_material_fee'] ?? null,
            'status' => $post['status'] ?? '',
            'study_reason_id' => $post['study_reason_id'] ?? null,
            'application_request' => $post['application_request'] ?? '',
            'credit_transfer_request' => $post['credit_transfer_request'] ?? null,
            'special_instructions' => $post['special_instructions'] ?? '',
            'is_orientation' => $post['is_orientation'] ?? '0',
            'survey_contact_status' => $post['survey_contact_status'] ?? null,
            'purchasing_contract_identifier' => $post['purchasing_contract_identifier'] ?? null,
            'associated_course_identifier' => $post['associated_course_identifier'] ?? null,
            'is_fulltimelearing' => $post['is_full_time_learning'] ?? null,
            'employer_id' => $post['employer_id'] ?? null,
            'contract_schedule_id' => $post['contract_schedule_id'] ?? null,
            'purchasing_contract_ode' => $post['purchasing_contract_ode'] ?? 0,
            'schedule_code' => $post['schedule_code'] ?? null,
            'auto_update_to_sru' => $post['auto_update_to_sru'] ?? 0,
            'advance_standing_credit' => $post['advance_standing_credit'] ?? 0,
            'is_material_fee_inc_initail_payment' => $post['is_material_fee_inc_initail_payment'] ?? 0,
            'purchasing_contract_schedule_identifier' => $post['purchasing_contract_schedule_identifier'] ?? null,
            'internal' => isset($post['is_internal']) && $post['is_internal'] ? 'Y' : 'N',
            'external' => isset($post['is_external']) && $post['is_external'] ? 'Y' : 'N',
            'workplace_based_delivery' => isset($post['workplace_based_delivery']) && $post['workplace_based_delivery'] ? 'Y' : 'N',
            'is_claim' => isset($post['is_claim']) && $post['is_claim'] ? $post['is_claim'] : '0',
            'is_qualification' => isset($post['is_qualification']) && $post['is_qualification'] ? $post['is_qualification'] : '0',
            'is_certificate' => isset($post['is_certificate']) && $post['is_certificate'] ? $post['is_certificate'] : '0',
            'mode_of_delivery' => ($post['mode_of_delivery']) ? $post['mode_of_delivery'] : null,
            'wil_requirements' => ($post['wil_requirements']) ? $post['wil_requirements'] : null,
            'third_party_providers' => ($post['third_party_providers']) ? $post['third_party_providers'] : null,
            'scholarship_percentage' => ($post['scholarship_percentage']) ? $post['scholarship_percentage'] : null,
            'is_receiving_any_scholarship' => isset($post['is_receiving_any_scholarship']) && $post['is_receiving_any_scholarship'] ? 'yes' : 'no',
            'subject_clming_for_credit' => ($post['subject_clming_for_credit']) ? $post['subject_clming_for_credit'] : null,
            'finish_date' => $post['finish_date'] ? date('Y-m-d', strtotime($post['finish_date'])) : null,
            'course_duration_type' => $post['course_duration_type'] ?? '2', // '2' for week (default)
            'total_weeks' => $post['total_weeks'] ?? 0,
            'updated_by' => $userId,
        ];

        if (! $post['isScheduleExist']) {
            $dataArr['start_date'] = $post['start_date'] ? date('Y-m-d', strtotime($post['start_date'])) : null;
            $dataArr['finish_date'] = $post['finish_date'] ? date('Y-m-d', strtotime($post['finish_date'])) : null;
            $dataArr['total_weeks'] = $post['total_weeks'] ?? 0;
            $dataArr['course_duration_type'] = $post['course_duration_type'] ?? null;
            $dataArr['course_fee'] = $post['course_fee'] ?? null;
            $dataArr['course_upfront_fee'] = $post['course_upfront_fee'] ?? null;
            $dataArr['agent_id'] = $post['agent_id'] ?? null;
        }

        return $dataArr;
    }

    private function addAdditionalData(&$resData, $post, $userId)
    {
        $resData['student_id'] = $post['student_id'];
        $resData['course_id'] = $post['course_id'];
        $resData['offer_id'] = $post['offer_id'];
        $resData['offer_status'] = 'Enrolled';
        $resData['course_type_id'] = $post['course_type_id'];
        $resData['res_cal_method'] = $post['res_cal_method'] ?? null;
        $resData['created_by'] = $userId;
    }

    public function getOshcProvider($request)
    {
        $whereArr = ['college_id' => $request->user()->college_id];

        return $this->oshcProvider->getWhere($whereArr, ['provider_name as Name', 'id as Id']);
    }

    public function getOshcType($request)
    {
        $whereArr = ['college_id' => $request->college_id];
        if (isset($request->oshc_provider)) {
            $whereArr['provide_id'] = $request->oshc_provider;
        }
        // return $this->oshc->getWhere($whereArr, ['oshc_type as Name','id as Id']);

        return OSHC::where($whereArr)
            ->select('id', 'oshc_type', 'duration')
            ->get()
            ->map(function ($item) {
                return [
                    'Id' => $item['id'],
                    'Name' => $item['oshc_type'].' ('.$item['duration'].' Month)',
                ];
            })
            ->toArray();
    }

    public function getOshcDuration($request)
    {
        $whereArr = ['college_id' => $request->college_id];
        if (isset($request->oshc_provider)) {
            $whereArr['provide_id'] = $request->oshc_provider;
        }
        if (isset($request->oshc_id)) {
            $whereArr['id'] = $request->oshc_id;
        }

        return $this->oshc->getWhere($whereArr, ['duration as Name', 'id as Id']);
    }

    public function getOshcInfo($studentId)
    {
        $result = StudentDetails::Where(['student_id' => $studentId])
            ->leftjoin('rto_oshc_providers as rop', 'rop.id', '=', 'rto_student_details.OSHC_provider')
            ->leftjoin('rto_oshc as ro', 'ro.provide_id', '=', 'rto_student_details.OSHC_provider')
            ->select('rto_student_details.*', 'rop.provider_name', 'ro.oshc_type as oshc_type_name')
            ->get()->first()->toArray();
        $result['OSHC_start_date'] = (empty($result['OSHC_start_date'])) ? 'N/A' : date(Config::get('app.dateFormatFrontSidePHP'), strtotime($result['OSHC_start_date']));
        $result['OSHC_end_date'] = (empty($result['OSHC_end_date'])) ? 'N/A' : date(Config::get('app.dateFormatFrontSidePHP'), strtotime($result['OSHC_end_date']));
        $result['hear_about_id_name'] = Config::get('constants.arrHereAbout')[$result['hear_about_id']];
        $result['disability_text'] = ($result['disability']) ? 'Yes' : 'No';
        $result['area_of_disability'] = explode(',', $result['area_of_disability']);
        $result['disability_list'] = AreaOfDisability::Select('avaitmiss_id as value', 'name as label')->orderBy('avaitmiss_id', 'ASC')->get()->toArray();
        $selectedDisabilityNames = [];
        foreach ($result['area_of_disability'] as $selectedDisability) {
            foreach ($result['disability_list'] as $disability) {
                if ($disability['value'] == $selectedDisability) {
                    $selectedDisabilityNames[] = $disability['label'];
                    break;
                }
            }
        }
        $result['selectedDisabilityNames'] = $selectedDisabilityNames;

        return $result;

        /*$getColumns = ['OSHC_provider', 'OSHC_type', 'OSHC_duration', 'OSHC_fee', 'OSHC_start_date', 'OSHC_end_date', 'hear_about_id', 'aboutus_specify', 'disability', 'area_of_disability'];
        $result = $this->studentDetails->getWhereFirst(['student_id' => $studentId], $getColumns);
        if(isset($result['area_of_disability'])){
            $result['area_of_disability'] = explode(',', $result['area_of_disability']);
        }

        $columnArr = ['avaitmiss_id as value','name as label'];
        $result['disability_list'] = $this->areaOfDisability->getAll($columnArr, 'avaitmiss_id', 'ASC');

        return $result;*/
    }

    public function getStudentOshcData($request)
    {
        return $this->oshc->findData($request->id, 'duration');
    }

    public function saveStudentOshcServiceData(StudentServiceInfo $postData)
    {
        $postData->updated_by = Auth::user()->id;
        $postData->OSHC_course_id = StudentCourses::where('id', $postData->student_course_id)->value('course_id');
        unset($postData->student_course_id);

        DB::beginTransaction();
        try {
            $res = StudentDetails::Where('student_id', $postData->student_id)->update($postData->toArray());
            DB::commit();

            return $res;
        } catch (\Exception $e) {
            DB::rollBack();
            safeDD($e);
            throw new ApplicationException($e->getMessage());
        }
    }

    public function getServicesCategory($request)
    {
        $whereArr = [
            'college_id' => $request->college_id,
            'service_name_id' => $request->service_name_id,
        ];

        return $this->setupServicesCategory->getWhere($whereArr, ['category_name as Name', 'id as Id']);
    }

    public function getFacilityName($request)
    {
        $whereArr = [
            'college_id' => $request->college_id,
            'is_active' => '1',
        ];
        if (isset($request->category_id) && ! empty($request->category_id)) {
            $whereArr['category_id'] = $request->category_id;
        }

        return $this->setupServices->getWhere($whereArr, ['facility_name as Name', 'id as Id']);
    }

    public function getServiceProvider($request)
    {
        return $this->setupProviderFacility->getServiceProviderData($request);
    }

    public function getProviderPrice(Request $request)
    {
        $whereArr = [
            'college_id' => $request->college_id,
            'category_id' => $request->category_id,
            'facility_id' => $request->facility_id,
            'service_provider_id' => $request->service_provider_id,
        ];

        return SetupProviderFacility::where($whereArr)->select('*')->first();

    }

    public function getAdditionalServiceData($request)
    {
        return $this->studentAdditionalServiceRequest->findData($request->id);
    }

    public function getAdditionalServiceListData($request)
    {
        $result = $this->studentAdditionalServiceRequest->getAdditionalServiceListData($request);
        $arrServicesName = SetupServicesName::select('services_name as Name', 'id as Id')->where('college_id', $request->college_id)->get()->toArray();

        return [
            'arrServicesName' => $arrServicesName,
            'data' => $result['data'],
            'total' => $result['total'],
        ];

    }

    public function saveStudentAdditionalService($post)
    {
        $loginData = Auth::user();
        $post['college_id'] = Auth::user()->college_id;
        $post['updated_by'] = $loginData->id;
        $action = (isset($post['id']) && ! empty($post['id'])) ? 'update' : 'create';

        DB::beginTransaction();
        try {
            if ($action == 'update') {
                $result = $this->studentAdditionalServiceRequest->update($post, $post['id']);
            } else {
                $post['created_by'] = $loginData->id;
                $result = $this->studentAdditionalServiceRequest->create($post);
            }
            DB::commit();

            return $result;
        } catch (\Exception $e) {
            DB::rollBack();
            safeDD($e);
            throw new ApplicationException($e->getMessage());
        }
    }

    public function deleteAdditionalServiceData($request)
    {
        $result = $this->studentAdditionalServiceRequest->delete($request->id);
        if ($result) {
            return ['status' => 'success', 'message' => 'Delete Successfully.'];
        } else {
            return ['status' => 'error', 'message' => 'Something went wrong.'];
        }
    }

    public function getCoeHistoryListData($request)
    {
        $result = $this->studentCourseRepository->getCoeHistoryListData($request);

        return [
            'data' => $result['data'],
            'total' => $result['total'],
        ];
    }

    public function getFeeScheduleListData($request)
    {
        $result = $this->studentCourseRepository->getFeeScheduleListData($request);

        return [
            'data' => $result['data'],
            'total' => $result['total'],
        ];
    }

    public function getStudentTrainingPlanListData($request)
    {
        $courseId = $this->studentCourse->find($request->student_course_id)->course_id;
        $result = $this->studentCourseRepository->getStudentTrainingPlanListData($request, $courseId);

        return [
            'data' => $result['data'],
            'total' => $result['total'],
        ];
    }

    public function getTrainingPlanData($request)
    {
        if (! empty($request->id)) {
            $data['plan_detail'] = $this->studentCourseRepository->getTrainingPlanData($request->id);
        }
        $data['student_detail'] = $this->studentCourseRepository->getStudentCourseData($request->college_id, $request->student_id, $request->student_course_id);
        $data['employerData']['data'] = $this->employer->selectData(['id as Id', 'employer_name as Name']);
        $data['contractCodeData']['data'] = $this->contractCode->selectData(['id as Id', 'contract_code as Name']);
        $data['courseSiteData']['data'] = $this->courseSite->selectData(['id as Id', 'course_site_name as Name']);
        $data['venueData']['data'] = $this->campusVenue->selectData(['id as Id', DB::raw('CONCAT(venue_code,":",venue_name) AS Name')]);
        $data['trainingStatusData']['data'] = $this->convertConstantsFormat(Config::get('constants.arrTrainingStatus'));
        $data['fundingSourceData']['data'] = $this->convertConstantsFormat(Config::get('constants.arrFundingSource'));
        $data['contractTypeData']['data'] = $this->convertConstantsFormat(Config::get('constants.arrContractType'));
        $data['stateData']['data'] = $this->convertConstantsFormat(Config::get('constants.arrState'));

        return $data;
    }

    public function saveTrainingPlanData(SaveStudentTrainingPlan $request)
    {
        $post = $request->toArray();
        $loginData = Auth::user();
        $courseId = $this->studentCourse->find($post['student_course_id'])->course_id;
        DB::beginTransaction();
        try {
            if (isset($courseId) && $courseId > 0) {
                unset($post['student_course_id']);
                $post['course_id'] = $courseId;
                $post['created_by'] = $loginData->id;
                $post['updated_by'] = $loginData->id;
                $post['venue_code'] = (! empty($post['venue_code'])) ? $post['venue_code'] : '';

                $res = $this->courseTraining->create($post);
                DB::commit();
                if ($res) {
                    return $this->successResponse('Course Training Plan Add Successfully', 'data', $res);
                }
            }
        } catch (\Exception $e) {
            DB::rollBack();
            throw new ApplicationException($e->getMessage());
        }

    }

    public function updateTrainingPlanData(UpdateStudentTrainingPlan $request)
    {
        $post = $request->toArray();
        $updatedBy = Auth::user()->id;
        $primaryId = $post['course_training_plan_id'];
        $isValid = $this->courseTraining->find($primaryId);
        //        DB::beginTransaction();
        //        try {
        if ($isValid) {
            unset($post['course_training_plan_id']);
            $post['updated_by'] = $updatedBy;
            $res = $this->courseTraining->update($post, $primaryId);
            if ($res) {
                DB::commit();

                return $this->successResponse('Course Training Plan Update Successfully', 'data', $res);
            }
        }
        //        } catch (\Exception $e) {
        //            DB::rollBack();
        //            throw new ApplicationException($e->getMessage());
        //        }

    }

    public function deleteTrainingPlanData($request)
    {
        try {
            DB::beginTransaction();
            $res = $this->courseTraining->delete($request->id);
            DB::commit();

            return $res;
        } catch (\Exception $e) {
            DB::rollBack();
            safeDD($e);
            throw new ApplicationException($e->getMessage());
        }
    }

    public function getFeeScheduleDetailsData($request)
    {
        $data['data'] = $this->studentCourseRepository->getFeeScheduleDetails($request);
        $data['total'] = $this->studentCourseRepository->getFeeScheduleDetails($request, true);

        return $data;
    }

    public function getUploadedOfferChecklistDocumentsData($request)
    {
        return $this->studentCourseRepository->getUploadedOfferChecklistDocumentsData($request);
    }

    public function uploadOfferChecklistDocument($request)
    {
        $this->updateStudentDocumentsField($request);
        echo json_encode(['uploaded' => true, 'status' => 'success', 'message' => 'Uploaded Successfully']);
        exit;
    }

    public function updateStudentDocumentsField($request)
    {
        $collegeId = $request->college_id;
        $studentId = $request->student_id;
        $userId = Auth::user()->id;

        $rto_offer_document_checklist_id = $request->input('uploadid');
        $comment = $request->input('comment');
        $studentDocument = $request->input('document_id');
        $studentDocumentImages = $request->file('files');
        $rootFolder = Config::get('constants.arrCollegeRootFolder');
        $filePath = Config::get('constants.uploadFilePath.OfferFiles');
        $destinationPath = Helpers::changeRootPath($filePath, $studentId);
        $dataArr = [
            'college_id' => $collegeId,
            'folder_name' => $rootFolder['OfferFiles'],
            'sub_folder_name' => $studentId,
            'user_id' => $userId,
        ];
        $clgMaterialParentId = $this->getSubParentId($dataArr);
        $offerWhereArr = [
            'rto_student_id' => $studentId,
            'rto_offer_document_checklist_id' => $rto_offer_document_checklist_id,
        ];
        $offerId = $this->studentOfferDocuments->getWhereVal($offerWhereArr, 'id');

        if (isset($offerId) && $offerId > 0) {
            $studOfferDocArr = $this->studentOfferDocuments->findData($offerId);
            $file = $studentDocumentImages;
            $originalName = json_decode($request->input('metadata'))->fileName;
            $fileSize = $file->getSize();
            $filename = hashFileName($originalName);
            $upload_success = $file->move($destinationPath['default'], $filename);
            $studDataArr = [
                'original_name' => $originalName,
                'folder_or_file' => $filename,
                'size' => $fileSize,
                'type' => 'File',
                'file_path' => $destinationPath['view'],
            ];
            CollegeMaterials::where('id', $studOfferDocArr['document_material_id'])->update($studDataArr);
            $studOfferDocArrUpdate = [
                'file_name' => $originalName,
                'approved' => 1,
                'last_checked_by' => $userId,
            ];
            StudentOfferDocuments::where('id', $offerId)->update($studOfferDocArrUpdate);
        } else {
            $studOfferDocArr['rto_student_id'] = $studentId;
            $studOfferDocArr['rto_offer_document_checklist_id'] = $rto_offer_document_checklist_id;

            $file = $studentDocumentImages;
            $originalName = json_decode($request->input('metadata'))->fileName;
            $fileSize = $file->getSize();
            $filename = hashFileName($originalName);
            $upload_success = $file->move($destinationPath['default'], $filename);
            $studDataArr = [
                'college_id' => $collegeId,
                'original_name' => $originalName,
                'file_name' => $filename,
                'size' => $fileSize,
                'type' => 'File',
                'parent_id' => $clgMaterialParentId,
                'file_path' => $destinationPath['view'],
                'user_id' => $userId,
            ];
            $docMaterialId = $this->addCollegeMaterialInfo($studDataArr);
            $studOfferDocArr['document_material_id'] = $docMaterialId;
            $studOfferDocArr['file_name'] = $originalName;
            $studOfferDocArr['approved'] = 1;
            $studOfferDocArr['last_checked_by'] = $userId;
            $this->studentOfferDocuments->create($studOfferDocArr);
        }

    }

    public function getSubParentId($data)
    {
        $mainParentId = $this->getMainParentId($data);
        if ($mainParentId > 0) {
            $whereArr = [
                'folder_or_file' => $data['sub_folder_name'],
                'college_id' => $data['college_id'],
                'parent_id' => $mainParentId,
                'type' => 'Dir',
            ];
            $primaryId = $this->collegeMaterials->getWhereVal($whereArr, 'id');
            if (isset($primaryId)) {
                return $primaryId;
            } else {
                $dataArr = [
                    'college_id' => $data['college_id'],
                    'parent_id' => $mainParentId,
                    'folder_name' => $data['sub_folder_name'],
                    'type' => 'Dir',
                    'user_id' => $data['user_id'],
                ];
                $primaryId = $this->addCollegeMaterialParent($dataArr);

                return $primaryId;
            }
        }

        return false;
    }

    public function getMainParentId($data)
    {
        $whereArr = [
            'folder_or_file' => $data['folder_name'],
            'college_id' => $data['college_id'],
            'parent_id' => 0,
            'type' => 'Fix',
        ];
        $mainParentId = $this->collegeMaterials->getWhereVal($whereArr, 'id');

        if (isset($mainParentId)) {
            return $mainParentId;
        } else {
            $dataArr = [
                'college_id' => $data['college_id'],
                'parent_id' => '0',
                'folder_name' => $data['folder_name'],
                'type' => 'Fix',
                'user_id' => $data['user_id'],
            ];
            $primaryId = $this->addCollegeMaterialParent($dataArr);

            return $primaryId;
        }
    }

    public function addCollegeMaterialParent($data)
    {
        $dataArr = [
            'college_id' => $data['college_id'],
            'parent_id' => $data['parent_id'],
            'folder_or_file' => $data['folder_name'],
            'type' => $data['type'],
            'created_by' => $data['user_id'],
            'updated_by' => $data['user_id'],
        ];
        $res = $this->collegeMaterials->create($dataArr);

        return ($res) ? $res->id : false;
    }

    public function addCollegeMaterialInfo($data)
    {
        $dataArr = [
            'college_id' => $data['college_id'],
            'parent_id' => $data['parent_id'],
            'folder_or_file' => $data['file_name'],
            'size' => $data['size'],
            'type' => $data['type'],
            'original_name' => $data['original_name'],
            'file_path' => $data['file_path'],
            'created_by' => $data['user_id'],
            'updated_by' => $data['user_id'],
        ];
        $res = $this->collegeMaterials->create($dataArr);

        return $res->id;
        // return TRUE;
    }

    public function generateAgentInvoicePdf($request)
    {
        $getData = json_decode($request->data);
        $filePath = Config::get('constants.uploadFilePath.CollegeLogo');
        $destinationPath = Helpers::changeRootPath($filePath);
        $data = [
            'logoPath' => $destinationPath['default'],
            'arrState' => Config::get('constants.arrState'),
            'arrStudentInfo' => $this->studentRepository->getStudentDetail($getData->student_id),
            'objCollegeDetails' => $this->commonRepository->getCollegeDetails($getData->college_id),
            'objStudentCourse' => $this->studentCourseRepository->getAppliedStudentCourse($getData->student_id, $getData->student_course_id),
        ];
        $pdf = App::make('dompdf.wrapper');
        $pdf->loadView('v2.sadmin.student.pages.agent-invoice-pdf', $data);

        return $pdf->download('agent-invoice.pdf', $data);

        /*return view('frontend.student_course.agent-invoice-pdf', $data);
        return $pdf->stream();exit;*/
    }

    public function previewOfferLetter($request)
    {
        return $this->studentCourse->find($request->student_course_id)->course_id;
    }

    public function getStudentOfferChecklist($request)
    {
        $arrDocumentType = Config::get('constants.arrDocumentType');
        $studentData = $this->student->find($request->student_id);
        $documentNames = $this->offerDocumentChecklist->getWhere([
            'is_active' => 1,
            'college_id' => $request->user()->college_id,
            'student_origin' => $studentData->student_type,
        ]);
        $studentDocument = $this->studentCourseRepository->getStudentDocument($request->student_id);

        // Prepare the checklist data
        $arrStudentDocument = [];
        $trueFlaseArray = [];
        foreach ($documentNames as $document) {
            $documentChecklistId = $document['id'];
            $studentDocumentId = '';
            $fileName = '';
            $documentMaterialId = '';
            $comment = '';
            $approved = '';
            $lastCheckedBy = '';
            $lastUpdated = '';

            foreach ($studentDocument as $studentDoc) {
                if ($studentDoc->rto_offer_document_checklist_id === $documentChecklistId) {
                    $studentDocumentId = $studentDoc->id;
                    $fileName = $studentDoc->file_name;
                    $documentMaterialId = $studentDoc->document_material_id;
                    $comment = $studentDoc->comment;
                    $approved = $studentDoc->approved;
                    $lastCheckedBy = $studentDoc->name;
                    $lastUpdated = date('d/m/Y h:i:s A', strtotime($studentDoc->updated_at));
                    break;
                }
            }
            $arrStudentDocument[] = [
                'id' => $document['id'],
                'studentId' => $request->student_id,
                'collegeId' => $request->user()->college_id,
                'student_course_id' => $request->student_course_id,
                'document_name' => $document['document_name'],
                'document_type' => $document['document_type'],
                'is_compulsory' => $document['is_compulsory'],
                'is_active' => $document['is_active'],
                'student_origin' => $document['student_origin'],
                'student_document_id' => $studentDocumentId,
                'file_name' => $fileName,
                'document_material_id' => $documentMaterialId,
                'comment' => $comment,
                'approved' => $approved,
                'last_checked_by' => $lastCheckedBy,
                'last_updated' => $lastUpdated,
            ];
            $trueFlaseArray[$document['id']] = isset($fileName) ? 0 : ($document['is_compulsory'] ? 1 : 0);
        }

        return [
            'arrStudentDocument' => $arrStudentDocument,
            'objDocumentNames' => $documentNames,
            'arrDocumentType' => $arrDocumentType,
            'trueFlaseArray' => $trueFlaseArray,
        ];
    }

    public function saveStudentCertificate($post)
    {
        $post['course_id'] = $this->studentCourse->find($post['student_course_id'])->course_id;
        $post['created_by'] = $post['updated_by'] = Auth::user()->id;
        $whereArr = [
            'college_id' => $post['college_id'],
            'type' => $post['certificate_type'],
        ];
        $post['certificate_no'] = '';
        $certificateData = CertificateIdFormate::Where($whereArr)->get()->first();
        if ($certificateData) {
            $post['certificate_no'] = $certificateData->prefix.($certificateData->last_auto_increment_number + 1).$certificateData->suffix;
        }

        DB::beginTransaction();
        try {
            $res = StudentCertificateRegister::create($post);
            if ($res) {
                CertificateIdFormate::where($whereArr)->increment('last_auto_increment_number');
            }
            DB::commit();

            return $res;
        } catch (\Exception $e) {
            DB::rollBack();
            safeDD($e);
            throw new ApplicationException($e->getMessage());
        }
    }

    public function deleteStudentCertificate($primaryId)
    {
        DB::beginTransaction();
        try {
            $res = StudentCertificateRegister::destroy($primaryId);
            DB::commit();

            return $res;
        } catch (\Exception $e) {
            DB::rollBack();
            safeDD($e);
            throw new ApplicationException($e->getMessage());
        }
    }

    public function uploadStudentCertificate($request)
    {
        $loginUserId = Auth::user()->id;
        $primaryId = $request->student_certificate_id;

        $studentCertificate = $request->file('student_certificate');
        $filePath = Config::get('constants.uploadFilePath.StudentCertificate');
        $destinationPath = Helpers::changeRootPath($filePath);

        $originalName = json_decode($request->input('metadata'))->fileName;
        $filename = time().$originalName;

        DB::beginTransaction();
        try {
            $isUploaded = $studentCertificate->move($destinationPath['default'], $filename);
            if ($isUploaded) {
                $result = StudentCertificateRegister::Where(['id' => $primaryId])->update(['is_file_name' => $filename, 'updated_by' => $loginUserId]);
            }
            DB::commit();

            return $result;
        } catch (\Exception $e) {
            DB::rollBack();
            safeDD($e);
            throw new ApplicationException($e->getMessage());
        }
    }

    public function downloadStudentCertificate($primaryId)
    {
        // $studCertificateData = StudentCertificateRegister::find($primaryId)->first();
        $studCertificateData = StudentCertificateRegister::Where(['id' => $primaryId])->get()->first();
        $filename = $studCertificateData->is_file_name;
        if (! empty($filename)) {
            $filePath = Config::get('constants.uploadFilePath.StudentCertificate');
            $destinationPath = Helpers::changeRootPath($filePath);

            //            return $destinationPath['view']. $filename;
            return UploadService::download($destinationPath['view'].$filename);
            //            return  ['path'=>$destinationPath['view'].$filename,'file_name' =>$filename];
        }

        return false;
    }

    /* Below functions are not re-structured */
    public function saveUpfrontFeeSchedule($collegeId, $studentId, $courseId, $selectedCourseId, $isSave, $formData)
    {
        // Parse form data
        $arrData = [];
        parse_str($formData, $arrData);

        // Retrieve student upfront fee schedule
        $studentUpfrontFeeSchedule = $this->studentCourseRepository->getStudentUpfrontFeeSchedule($selectedCourseId);

        $startDate = date('Y-m-d', strtotime($studentUpfrontFeeSchedule->start_date));
        $endDate = date('Y-m-d', strtotime($studentUpfrontFeeSchedule->finish_date));
        $installmentDate = date('Y-m-d', strtotime($arrData['installment_start_date']));

        if ($installmentDate < $startDate || $installmentDate > $endDate) {
            $result = ['status' => 'error', 'message' => 'Cannot generate! Schedule duedate will be out of course startdate and finishdate.'];

            return response()->json($result);
        }

        $remainingFee = $studentUpfrontFeeSchedule->course_fee - $studentUpfrontFeeSchedule->course_upfront_fee;
        $noOfInstallment = $arrData['no_of_installment'];
        $installmentFee = $remainingFee / $noOfInstallment;

        $installmentStartDate = strtotime($arrData['installment_start_date']);
        $courseFinishDate = strtotime($studentUpfrontFeeSchedule->finish_date);

        $i = 1;
        $newInstallmentDate = [];
        $step = '+'.$arrData['frequency'].' '.$arrData['duration'];
        while ($installmentStartDate <= $courseFinishDate && $i <= $noOfInstallment) {
            $newInstallmentDate[] = date('Y-m-d', $installmentStartDate);
            $installmentStartDate = strtotime($step, $installmentStartDate);
            $i++;
        }

        $upfrontFeeSchedules = [];
        for ($i = 0; $i < count($newInstallmentDate); $i++) {
            $upfrontFeeSchedule = [
                'college_id' => $collegeId,
                'student_id' => $studentId,
                'course_id' => $courseId,
                'student_course_id' => $selectedCourseId,
                'remaining_fee' => $remainingFee,
                'installment_start_date' => $newInstallmentDate[$i],
                'no_of_installment' => ($arrData['no_of_installment'] != '') ? $arrData['no_of_installment'] : null,
                'frequency' => ($arrData['frequency'] != '') ? $arrData['frequency'] : null,
                'duration' => ($arrData['duration'] != '') ? $arrData['duration'] : null,
                'installment_amount' => $installmentFee,
                'created_by' => auth()->user()->id,
                'updated_by' => auth()->user()->id,
            ];

            $upfrontFeeSchedules[] = $upfrontFeeSchedule;
        }

        if ($isSave == 1) {
            $this->studentCourseRepository->deleteOfferUpfrontFeeSchedule($collegeId, $selectedCourseId);
            $this->studentCourseRepository->saveUpfrontFeeSchedules($upfrontFeeSchedules);

            return $this->successResponse('Data Save successfully', 'data', $upfrontFeeSchedules);
        }

        return $this->successResponse('Data found successfully', 'data', $upfrontFeeSchedules);
    }

    public function deleteOfferFeeScheduleData($request)
    {
        $offerScheduleData = OfferUpfrontFeeSchedule::find($request['id']);
        $whereArr = [
            'college_id' => $offerScheduleData->college_id,
            'student_id' => $offerScheduleData->student_id,
            'course_id' => $offerScheduleData->course_id,
            'student_course_id' => $offerScheduleData->student_course_id,
            // 'invoiced_start_date'   => $offerScheduleData->installment_start_date
        ];

        /*$hasTransactions = StudentInitialPaymentDetails::where($whereArr)->whereHas('activeTransactions')->exists();
        if($hasTransactions){
            throw new ApplicationException('Cannot delete this invoice. It is already paid.');
        }*/
        $hasGenerated = StudentInitialPaymentDetails::where($whereArr)->exists();
        if ($hasGenerated) {
            throw new ApplicationException('This schedule has already been imported and cannot be deleted.');
        }

        if (Xero::isConnected()) {
            /*$initialPaymentDataArr = StudentInitialPaymentDetails::where($whereArr)->get()->first();
            if($initialPaymentDataArr){
                $resData = StudentInitialPaymentDetails::find($initialPaymentDataArr->id);
                if(!$resData->canDelete()){
                    throw new ApplicationException('Cannot delete this invoice. It is already paid.');
                }
            }*/
        }

        DB::beginTransaction();
        try {
            $res = $offerScheduleData->delete();
            DB::commit();

            return $res;
        } catch (\Exception $e) {
            DB::rollBack();
            safeDD($e);
            throw new ApplicationException($e->getMessage());
        }
    }

    public function bulkDeleteOfferFeeScheduleData($request)
    {
        if (count($request['ids']) == 0) {
            throw new ApplicationException('Please select at least one record');
        }

        $offerScheduleData = OfferUpfrontFeeSchedule::find($request['ids'][0]);
        $whereArr = [
            'college_id' => $offerScheduleData->college_id,
            'student_id' => $offerScheduleData->student_id,
            'course_id' => $offerScheduleData->course_id,
            'student_course_id' => $offerScheduleData->student_course_id,
        ];

        /*$hasTransactions = StudentInitialPaymentDetails::where($whereArr)->whereHas('activeTransactions')->exists();
        if($hasTransactions){
            throw new ApplicationException('Cannot delete this invoice. It is already paid.');
        }*/
        $hasGenerated = StudentInitialPaymentDetails::where($whereArr)->exists();
        if ($hasGenerated) {
            throw new ApplicationException('This schedule has already been imported and cannot be deleted.');
        }

        DB::beginTransaction();
        try {
            $res = OfferUpfrontFeeSchedule::whereIn('id', $request['ids'])->delete();
            DB::commit();

            return $res;
        } catch (\Exception $e) {
            DB::rollBack();
            safeDD($e);
            throw new ApplicationException($e->getMessage());
        }
    }

    public function getOfferId($post)
    {
        return $this->studentCourse->find($post['student_course_id'])->offer_id;
    }

    public function getOfferCommunicationLog($request)
    {
        $result = $this->studentCourseRepository->getOfferCommunicationLogListData($request->all());

        return [
            'data' => $result['data'],
            'total' => $result['total'],
        ];

    }

    public function getSectionTypeListData($request)
    {
        return $this->setupSection->getWhere(['college_id' => Auth::user()->college_id, 'type' => $request->section_type], ['value as Name', 'id as Id']);
    }

    public function addOfferCommunicationLogData($request)
    {
        $postData = $request->toArray();
        $postData['created_by'] = Auth::user()->id;
        $postData['updated_by'] = Auth::user()->id;
        $postData['comment_by'] = Auth::user()->id;
        DB::beginTransaction();
        try {
            $insertData = $this->studentCommunicationLog->create($postData);
            DB::commit();

            return $insertData;
        } catch (\Exception $e) {
            DB::rollBack();
            safeDD($e);
            throw new ApplicationException($e->getMessage());
        }
    }

    public function deleteOfferCommunicationLog($postData)
    {
        DB::beginTransaction();
        try {
            $result = $this->studentCommunicationLog->delete($postData['id']);
            DB::commit();

            return $result;
        } catch (\Exception $e) {
            DB::rollBack();
            safeDD($e);
            throw new ApplicationException($e->getMessage());
        }
    }

    public function enableDisableOfferCommunicationLog($postData)
    {
        DB::beginTransaction();
        try {
            $result = $this->studentCommunicationLog->update(['visiblity' => $postData['visiblity']], $postData['id']);
            DB::commit();

            return $result;
        } catch (\Exception $e) {
            DB::rollBack();
            safeDD($e);
            throw new ApplicationException($e->getMessage());
        }
    }

    public function getStudentCOEDetails(Request $request)
    {
        $studentCourseId = $request->student_course_id;

        return $this->studentCourse->findData($studentCourseId, ['id', 'student_id', 'coe_applicable', 'coe_name', 'coe_image', 'coe_material_id']);
    }

    public function getCourseExtensionHistoryData(Request $request)
    {
        $studentCourseId = $request->student_course_id;

        // Get extension history from database
        $extensionHistory = DB::table('rto_course_extension_history as ceh')
            ->leftJoin('rto_users as u', 'ceh.created_by', '=', 'u.id')
            ->leftJoin('rto_student_courses as sc', 'ceh.student_course_id', '=', 'sc.id')
            ->leftJoin('rto_students as s', 'sc.student_id', '=', 's.id')
            ->where('ceh.student_course_id', $studentCourseId)
            ->select([
                'ceh.*',
                'u.name as created_by_name',
                's.name as student_name',
                DB::raw("DATE_FORMAT(ceh.created_at, '%d/%m/%Y %h:%i %p') as createAt"),
            ])
            ->orderBy('ceh.created_at', 'desc')
            ->get();

        return [
            'data' => $extensionHistory,
            'total' => count($extensionHistory),
        ];
    }

    public function extendCourseDueDate(Request $request)
    {
        // dd($request->input());
        DB::beginTransaction();
        try {
            $studentCourseId = $request->selectedStudCourseIDForExtend ?? $request->student_course_id;
            $studentId = $request->studentId;

            // Get current course data
            $currentCourse = $this->studentCourse->findData($studentCourseId, ['start_date', 'finish_date', 'total_weeks', 'course_duration_type']);
            $extendFinishDate = \Carbon\Carbon::createFromFormat('d-m-Y', $request->extend_finish_date)->format('Y-m-d');

            // Update the course with new dates
            $updateData = [
                'finish_date' => $extendFinishDate,
                'total_weeks' => $request->extend_total_weeks,
                'updated_by' => Auth::user()->id,
                // 'course_duration_type' => $request->extend_course_duration_type,
            ];
            $this->studentCourse->update($updateData, $studentCourseId);

            // Save extension history
            $historyData = [
                'student_course_id' => $studentCourseId,
                'start_date' => $currentCourse['start_date'],
                'old_finish_date' => $currentCourse['finish_date'],
                'new_finish_date' => $extendFinishDate,
                'old_total_weeks' => $currentCourse['total_weeks'],
                'new_total_weeks' => $request->extend_total_weeks,
                'extension_reason' => $request->extension_reason,
                'created_by' => Auth::user()->id,
                'updated_by' => Auth::user()->id,
            ];

            App\Model\v2\StudentCourseExtensionHistory::created($historyData);

            DB::commit();

            return ['status' => 'success', 'data' => $updateData];
        } catch (\Exception $e) {
            DB::rollBack();

            return [
                'status' => 'error',
                'message' => 'Failed to extend course due date: '.$e->getMessage(),
                'data' => null,
            ];
        }
    }

    public function getContractScheduleData($request)
    {
        return ContractFundingSource::select('rto_contract_funding_source.*', 'rcc.contract_code')->join('rto_contract_code as rcc', 'rcc.id', '=', 'rto_contract_funding_source.contract_code_id')->where('rto_contract_funding_source.id', $request['contract_schedule_id'])->get()->toArray();

    }

    //    public function saveStudentCOEDetails($request,AddCoeForOfferProcess $process){
    //
    //        $studentId = $request->student_id;
    //        $studentCourseId = $request->student_course_id;
    //         $file = $request->file('COEDocument');
    //         if (!empty($file)) {
    //             $fileUploadResult = $this->uploadCOEFile($file, $request->student_id);
    //             if ($fileUploadResult) {
    //                 $studentCourseData['coe_image'] = $fileUploadResult['filename'];
    //             }
    //         }
    //
    //        $objStudent = $this->student->find($studentId);
    //        if(!empty($objStudent->generated_stud_id)) {
    //            $studentData['is_student'] = 1;
    //            $studentData['is_offered'] = 0;
    //            $studentCourseData['status'] = 'Enrolled';
    //            $studentCourseData['offer_status'] = 'Enrolled';
    //            $studentCourseData['updated_by'] = Auth::user()->id;
    //            $getCurrentStatus = $this->studentCourse->findData($studentCourseId, ['offer_status']);
    //            $offeredStatus = 'Enrolled';
    //            $logData = [
    //                'college_id' => $request->user()->college_id,
    //                'comment_by' => $request->user()->id,
    //                'student_id' => $request->input('student_id'),
    //                'today_date' => date('l,d F Y'),
    //                'type' => '',
    //                'log_type' => 'status',
    //                'status' => '',
    //                'log' =>  $getCurrentStatus['offer_status']. ','. $offeredStatus,
    //                'activity_log' => 'status',
    //                'view_by' => '',
    //                'visiblity' => 1,
    //                'created_by' => $request->user()->id,
    //                'updated_by' => $request->user()->id
    //            ];
    //            $saveLog = StudentCommunicationLog::create($logData);
    //        }
    //        $studentCourseData['coe_applicable']  = (!empty($request->is_applicable)) ? $request->is_applicable : '0';
    //        $studentCourseData['coe_name']  = ($request->is_applicable == 1) ? 'N/A' : $request->code_no;
    //        $result = $this->student->update($studentData, $studentId);
    //        return  $this->studentCourse->update($studentCourseData, $studentCourseId);
    //    }
    //    public function saveStudentCOEDetails(Request $request,AddCoeForOfferProcess $process){
    //
    //        $data = $process->run($request);
    // //        $studentId = $request->student_id;
    // //        $studentCourseId = $request->student_course_id;
    // //         $file = $request->file('COEDocument');
    // //         if (!empty($file)) {
    // //             $fileUploadResult = $this->uploadCOEFile($file, $request->student_id);
    // //             if ($fileUploadResult) {
    // //                 $studentCourseData['coe_image'] = $fileUploadResult['filename'];
    // //             }
    // //         }
    // //
    // //        $objStudent = $this->student->find($studentId);
    // //        if(!empty($objStudent->generated_stud_id)) {
    // //            $studentData['is_student'] = 1;
    // //            $studentData['is_offered'] = 0;
    // //            $studentCourseData['status'] = 'Enrolled';
    // //            $studentCourseData['offer_status'] = 'Enrolled';
    // //            $studentCourseData['updated_by'] = Auth::user()->id;
    // //            $getCurrentStatus = $this->studentCourse->findData($studentCourseId, ['offer_status']);
    // //            $offeredStatus = 'Enrolled';
    // //            $logData = [
    // //                'college_id' => $request->user()->college_id,
    // //                'comment_by' => $request->user()->id,
    // //                'student_id' => $request->input('student_id'),
    // //                'today_date' => date('l,d F Y'),
    // //                'type' => '',
    // //                'log_type' => 'status',
    // //                'status' => '',
    // //                'log' =>  $getCurrentStatus['offer_status']. ','. $offeredStatus,
    // //                'activity_log' => 'status',
    // //                'view_by' => '',
    // //                'visiblity' => 1,
    // //                'created_by' => $request->user()->id,
    // //                'updated_by' => $request->user()->id
    // //            ];
    // //            $saveLog = StudentCommunicationLog::create($logData);
    // //        }
    // //        $studentCourseData['coe_applicable']  = (!empty($request->is_applicable)) ? $request->is_applicable : '0';
    // //        $studentCourseData['coe_name']  = ($request->is_applicable == 1) ? 'N/A' : $request->code_no;
    // //        $result = $this->student->update($studentData, $studentId);
    // //        return  $this->studentCourse->update($studentCourseData, $studentCourseId);
    //    }
    public function fileUploadForCOE($request)
    {
        $da = $request->input();
        $studentId = $da['student_id'];
        $studentCourseId = $da['student_course_id'];
        $file = $request->file('COEDocument');
        if (! empty($file)) {
            $fileUploadResult = $this->uploadCOEFile($file, $da['student_id']);
            if ($fileUploadResult) {
                $studentCourseData['coe_image'] = $fileUploadResult['filename'];
                $this->studentCourse->update($studentCourseData, $studentCourseId);
            }
        }
    }

    public function SaveCOEStudentCommunicationLog($request)
    {

        $objStudent = $this->student->find($request['student_id']);
        if (! empty($objStudent->generated_stud_id)) {
            $studentData['is_student'] = 1;
            $studentData['is_offered'] = 0;
            $studentCourseData['status'] = 'Enrolled';
            $studentCourseData['offer_status'] = 'Enrolled';
            $studentCourseData['updated_by'] = Auth::user()->id;
            $getCurrentStatus = $this->studentCourse->findData($request['student_course_id'], ['offer_status']);
            $offeredStatus = 'Enrolled';
            $logData = [
                'college_id' => Auth::user()->college_id,
                'comment_by' => 1,
                'student_id' => $request['student_id'],
                'today_date' => date('l,d F Y'),
                'type' => '',
                'log_type' => 'status',
                'status' => '',
                'log' => $getCurrentStatus['offer_status'].','.$offeredStatus,
                'activity_log' => 'status',
                'view_by' => '',
                'visiblity' => 1,
                'created_by' => Auth::user()->id,
                'updated_by' => Auth::user()->id,
            ];
            $saveLog = StudentCommunicationLog::create($logData);
        }
    }

    public function updateCOEStudentData($request)
    {
        $studentData['is_student'] = 1;
        $studentData['is_offered'] = 0;
        $this->student->update($studentData, $request['student_id']);
    }

    public function updateCOEStudentCourseData($request)
    {

        $studentCourseData['coe_applicable'] = $request['is_applicable'] = (isset($request['is_applicable'])) ? $request['is_applicable'] : '0';
        $studentCourseData['coe_name'] = ($request['is_applicable'] == 1) ? 'N/A' : $request['code_no'];

        return $this->studentCourse->update($studentCourseData, $request['student_course_id']);
    }

    public function uploadCOEFile($file, $studentId)
    {
        $filePath = Config::get('constants.uploadFilePath.StudentCOE');
        $destinationPath = Helpers::changeRootPath($filePath, $studentId);
        $time = time();
        $originalName = $file->getClientOriginalName();
        $filename = $time.$originalName;
        //        $upload_success = $file->move($destinationPath['default'], $filename);
        $upload_success = UploadService::uploadAs($destinationPath['view'], $file, $filename);
        info('file uploaded form COE', [$upload_success]);
        if ($upload_success) {
            return [
                'filename' => $filename,
            ];
        }

        return null;
    }

    public function getOfferScheduleData($request)
    {
        $resData = StudentCourses::from('rto_student_courses as rsc')
            ->join('rto_agents as agent', 'agent.id', '=', 'rsc.agent_id')
            ->join('rto_courses as rc', 'rc.id', '=', 'rsc.course_id')
            ->join('rto_students as rs', 'rs.id', '=', 'rsc.student_id')
            ->leftJoin('rto_agent_commission as rac', function ($join) {
                $join->on('rac.agent_id', '=', 'agent.id')
                    ->on('rac.course', '=', 'rc.id');
            })
            ->where('rsc.id', $request['student_course_id'])
            ->get([
                'rs.id as student_id',
                DB::raw("CONCAT(rs.first_name,' ',rs.family_name) as student_name"),
                'rs.generated_stud_id',
                'rs.profile_picture',
                'rc.course_name',
                'rsc.*',
                'rac.commission as agent_commission',
                'rac.gst as agent_gst',
            ])
            ->first()
            ->toArray();

        $paidAmount = StudentInitialPaymentDetails::where([
            // 'payment_type'  => StudentInitialPaymentDetails::PAYMENT_TYPE_SCHEDULE,
            'college_id' => $request['college_id'],
            'student_course_id' => $request['student_course_id'],
        ])->sum('upfront_fee_pay');

        $resData['profile_pic'] = $this->getStudentProfilePicPath($request['student_id'], $resData['profile_picture']);
        $resData['paid_amount'] = $paidAmount;
        $resData['remaining_amount'] = $resData['course_fee'] - $resData['course_upfront_fee'];
        $resData['invoice_due_days'] = InvoiceSetting::getValueFromKey(InvoiceSetting::DAYS_AFTER_START_DATE)->value ?? 0;

        return $resData;
    }
}
