import _ from 'lodash';
import * as moment from 'dayjs';
import mitt from 'mitt';
import apiClient from '@spa/services/api.client';
import { api } from '@spa/plugins/axios.js';
// window._ = _
/**
 * We'll load the axios HTTP library which allows us to easily issue requests
 * to our Laravel back-end. This library automatically handles sending the
 * CSRF token as a header based on the value of the "XSRF" token cookie.
 */

Object.assign(window, { _: _, $http: apiClient, $moment: moment, Fire: mitt(), $httpAxios: api });
// window['$http'] = api;
// window['$moment'] = moment

// window.$http.defaults.headers.common['X-Requested-With'] = 'XMLHttpRequest';

/**
 * Echo exposes an expressive API for subscribing to channels and listening
 * for events that are broadcast by Laravel. Echo and event broadcasting
 * allows your team to easily build robust real-time web applications.
 */

// import Echo from 'laravel-echo';

// import Pusher from 'pusher-js';
// window.Pusher = Pusher;

// window.Echo = new Echo({
//     broadcaster: 'pusher',
//     key: process.env.MIX_PUSHER_APP_KEY,
//     cluster: process.env.MIX_PUSHER_APP_CLUSTER,
//     forceTLS: true
// });

Array.prototype.maxVal = function () {
    return Math.max.apply(null, this);
};

Array.prototype.minVal = function () {
    return Math.min.apply(null, this);
};
