<?php

namespace App\Services\api\v3;

use App\DTO\api\v3\CourseFilterDTO;
use App\DTO\api\v3\DocumentUploadDTO;
use App\DTO\api\v3\StudentApplicationDTO;
use App\DTO\api\v3\StudentApplicationEmploymentDTO;
use App\DTO\api\v3\StudentApplicationLanguageDTO;
use App\DTO\api\v3\StudentApplicationQualificationDTO;
use App\DTO\api\v3\StudentDisabilityDTO;
use App\DTO\api\v3\StudentEmergencyContactDTO;
use App\DTO\api\v3\StudentOshcDTO;
use App\DTO\api\v3\StudentSchoolingDTO;
use App\Model\v2\CampusVenue;
use App\Model\v2\CollegeCampus;
use App\Model\v2\Colleges;
use App\Model\v2\Country;
use App\Model\v2\Courses;
use App\Model\v2\CourseType;
use App\Model\v2\OfferDocumentChecklist;
use App\Model\v2\SetupSection;
use App\Model\v2\Student;
use App\Model\v2\StudentAdditionalServiceRequest;
use App\Model\v2\StudentApplicationProcess;
use App\Model\v2\StudentCourses;
use App\Model\v2\StudentDetails;
use App\Model\v2\StudentEducation;
use App\Model\v2\StudentEmployment;
use App\Model\v2\StudentOfferDocuments;
use App\Repositories\Repository;
use Carbon\Carbon;
use Illuminate\Pagination\Paginator;
use Illuminate\Support\Arr;

class CourseApiService {

    protected $allApplicationRelations = array(
        'studentCourses',
        'studentDetails',
        'studentEducation',
        'studentEmployment',
        'appprocess'
    );

    protected $SetupSection, $countrymodel, $documents;
    public function __construct() {
        
    }
    
    public function getCollegeId(){
        $authCollegeId = $collegeId = auth()->user()->college_id ?? null;
        if(!$authCollegeId){
            $collegeId = Colleges::orderByDesc("status")->orderByDesc("id")->value("id");
        }
        return $collegeId;
    }
    public function getAllTypes($collegeId){
        $types = CourseType::where(["status" => 1, "enable_for_api" => 1])
                ->where(function($q) use($collegeId){
                    $q->where("college_id", 0);
                    if($collegeId > 0) $q->orWhere("college_id", $collegeId);
                })
                ->select(["id", "title", "description"])
                ->orderBy("college_id")
                ->orderBy("display_order")
                ->get();
        return $types;
    }
    public function getModes($collegeId = null){
        $modes = config('constants.arrCourseDeliveryMode');
        Arr::forget($modes, '');
        return kendify($modes, "value", "label");
    }
    public function getCities($collegeId = null){
        $venues = CampusVenue::orderBy("sub_urb")->get("sub_urb")->pluck("sub_urb", "sub_urb");
        return kendify($venues, "value", "label");
    }
    public function getAcceptanceTypes($collegeId = null){
        $types = config('constants.arrDeliveryTarget');
        Arr::forget($types, '');
        return kendify($types, "value", "label");
    }
    public function getAvailableCourses(CourseFilterDTO $filterParams){
        $collegeId = auth()->user()->college_id ?? 3;
        $courseType = $filterParams->type ?? null;
        if(!$courseType){
            $courseType = Colleges::getCollegeInfoQuery()->value("course_type_for_api");
            $courseType = explode(",", $courseType);
            if(empty($courseType)){
                $courseType = ["nocourseallowed"];
            }
            // $allTypes = $this->getAllTypes($collegeId);
            // $courseType = $allTypes->pluck("id")->toArray();
        }
        $campuses = $venueCampuses = [];
        $filterByCampus = false;
        if($filterParams->campus){
            if(is_numeric($filterParams->campus)){
                $campuses = CollegeCampus::where("status", "=", 1)->where("id", "=", $filterParams->campus)->pluck("id")->toArray();
            }else{
                $campuses = CollegeCampus::where("status", "=", 1)->where("name", "like", "{$filterParams->campus}%")->pluck("id")->toArray();
            }
            $filterByCampus = true;
        }
        if($filterParams->city){
            $venueCampuses = CampusVenue::where("sub_urb", "like", "{$filterParams->city}%")->pluck("campus_id")->toArray();
            $filterByCampus = true;
        }
        $allCampuses = array_unique([...$campuses, ...$venueCampuses]);
        if($filterByCampus && empty($allCampuses)){
            $allCampuses = ["-1"];
        }
        //dd($allCampuses);
        //dd($filterParams->date);
        $courseQry = Courses::where(["activated_now" => 1])
                        ->whereHas('campuses', function ($q) use ($filterParams, $allCampuses) {
                            $q->whereHas('campus', function ($query) use ($allCampuses) {
                                $query->where('status', 1);
                                if (!empty($allCampuses)) {
                                    if (is_array($allCampuses)) {
                                        $query->whereIn('id', $allCampuses);
                                    } else {
                                        $query->where('id', $allCampuses);
                                    }
                                }
                            });
                        })
                        ->whereHas('intakes', function ($q) use ($filterParams, $allCampuses) {
                            $q->where('active', 1)
                                ->where("intake_start", ">=", Carbon::now());
                            if (!empty($filterParams->date)) {
                                $date = Carbon::parse($filterParams->date);
                                $q->whereMonth("intake_start", $date->month)
                                        ->whereYear('intake_start', $date->year);
                            }
                            $q->whereHas('campusIntakes', function ($query) use ($allCampuses) {
                                $query->where('status', 1);
                                if (!empty($allCampuses)) {
                                    if (is_array($allCampuses)) {
                                        $query->whereIn('campus_id', $allCampuses);
                                    } else {
                                        $query->where('campus_id', $allCampuses);
                                    }
                                }
                            });
                        })
                        ->with([
                            "campuses" => function ($q) use($filterParams, $allCampuses) {
                                $q->with(["campus" => function($cq){
                                    $cq->with(["venues" => function($vq){
                                        $vq->where("set_active", 1)
                                            ->orderByDesc("status_default");
                                    }]);
                                }])->whereHas("campus", function ($query) use($allCampuses) {
                                    $query->where("status", 1);
                                    if(!empty($allCampuses)){
                                        if(is_array($allCampuses)){
                                            $query->whereIn("id", $allCampuses);
                                        }else{
                                            $query->where("id", $allCampuses);
                                        }
                                    }
                                });
                            }, 
                            "intakes" => function($q) use($filterParams, $allCampuses){
                                $q->with("campusIntakes", function($cq) use($allCampuses){
                                        $cq->with(["campus" => function($cq){
                                            $cq->with(["venues" => function($vq){
                                                $vq->where("set_active", 1)
                                                    ->orderByDesc("status_default");
                                            }]);
                                        }]);
                                        if (!empty($allCampuses)) {
                                            if (is_array($allCampuses)) {
                                                $cq->whereIn('campus_id', $allCampuses);
                                            } else {
                                                $cq->where('campus_id', $allCampuses);
                                            }
                                        }
                                    })
                                    ->withCount(["totalapplications", "totalenrollments"])
                                    ->where("intake_start", ">=", Carbon::now())
                                    ->where("active", "=", 1)
                                    ->orderBy("intake_start");

                                if (!empty($filterParams->date)) {
                                    $date = Carbon::parse($filterParams->date);
                                    $q->whereMonth("intake_start", $date->month)
                                            ->whereYear('intake_start', $date->year);
                                }
                            }
                        ]);
        
        // if(!empty($filterParams->campus)){
        //     $courseQry->has("campuses");
        // }
        if($courseType){
            if(is_array($courseType)){
                $courseQry->whereIn("course_type_id", $courseType);
            }else{
                $courseQry->where("course_type_id", "=", $courseType);
            }
        }
        if(!empty($filterParams->mode)){
            if($filterParams->mode == "online"){
                $courseQry->where("online_hours", ">",  0)
                            ->where(function($q){
                                $q->where("face_to_face_hours", "=", 0)
                                    ->orWhere("online_hours", "=", null);
                            });
            }else if($filterParams->mode == "face-to-face" || $filterParams->mode == "offline"){
                $courseQry->where("face_to_face_hours", ">",  0)->where(function($q){
                    $q->where("online_hours", "=", 0)
                        ->orWhere("online_hours", "=", null);
                });;
            }else if($filterParams->mode == "hybrid"){
                $courseQry->where("online_hours", ">",  0)->where("face_to_face_hours", ">",  0);
            }
        }
        //dd(getParsedSQL($courseQry));

        if(!empty($filterParams->search)){
            $courseQry->where(function($q) use($filterParams){
                $q->where("course_code", "like", "{$filterParams->search}%")
                    ->orWhere("course_name", "like", "%{$filterParams->search}%");
            });
        }

        Paginator::currentPageResolver(function () use ($filterParams) {
            return $filterParams->page;
        });
        return $courseQry->paginate($filterParams->take);
    }

    public function getCourseDetail($courseCode = "", $selects = [], $actionWithSelects = 'sort'){
        $selectedIntake = $selects["intake"] ?? null;
        $selectedIntake = ($selectedIntake) ? explode(",", $selectedIntake) : [];

        $selectedCampus = $selects["campus"] ?? null;
        $selectedCampus = ($selectedCampus) ? explode(",", $selectedCampus) : [];

        $courseQry = Courses::with([
                            "campuses" => function ($q) use($selectedCampus, $actionWithSelects) {
                                if($selectedCampus){
                                    if($actionWithSelects == 'filter'){
                                        $q->whereIn("campus_id", $selectedCampus);
                                    }else{
                                        $q->orderByRaw("FIELD(campus_id, " . implode(',', $selectedCampus) . ") DESC");
                                    }
                                }
                                $q->with(["campus" => function($cq){
                                    $cq->with(["venues" => function($vq){
                                        $vq->where("set_active", 1)
                                            ->orderByDesc("status_default");
                                    }]);
                                }])->whereHas("campus", function ($query) {
                                    $query->where("status", 1);
                                });
                            }, 
                            "intakes" => function($q) use($selectedIntake, $selectedCampus, $actionWithSelects){
                                $q->where("intake_start", ">=", Carbon::now())
                                    ->where("active", "=", 1)
                                    ->with([
                                        "campusIntakes" => function($cq) use($selectedCampus, $actionWithSelects){
                                            $cq->with("campus");
                                            if($selectedCampus){
                                                if($actionWithSelects == 'filter'){
                                                    $cq->whereIn("campus_id", $selectedCampus);
                                                }else{
                                                    $cq->orderByRaw("FIELD(campus_id, " . implode(',', $selectedCampus) . ") DESC");
                                                }
                                            }
                                        }
                                    ])
                                    ->withCount(["totalapplications", "totalenrollments"])
                                    ->whereHas("campusIntakes");
                                if($selectedIntake){
                                    if($actionWithSelects == 'filter'){
                                        $q->whereIn("id", $selectedIntake);
                                    }else{
                                        $q->orderByRaw("FIELD(id, " . implode(',', $selectedIntake) . ") DESC");
                                    }
                                }
                                $q->orderBy("intake_start");
                            }
                        ])
                        ->where(["course_code" => $courseCode, "activated_now" => 1]);
        return $courseQry->first();
    }
    
}