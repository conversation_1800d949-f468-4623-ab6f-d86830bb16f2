<?php

use Stancl\Tenancy\Middleware\InitializeTenancyByDomain;
use Stancl\Tenancy\Middleware\PreventAccessFromCentralDomains;

Route::middleware([
    'auth',
    'verified',/* InitializeTenancyByDomain::class, */
    PreventAccessFromCentralDomains::class,
])->group(function () {
    // if (config('features.moodle')) {
    //     Route::get('moodle-setup', \App\Http\Livewire\Moodle\Setup::class)->name('moodle-setup');
    // }

    Route::get('integrations/stripe/setup', \App\Http\Livewire\TenantStripeSetup::class)
        ->name('tenant.stripe.setup');

    Route::get('integrations/shortcourse/setup', \App\Http\Livewire\TenantShortCourseSiteSetup::class)
        ->name('tenant.shortcourse.setup');
});
